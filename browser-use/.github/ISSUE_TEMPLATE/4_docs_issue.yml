name: 📚 Documentation Issue
description: Report an issue in the browser-use documentation
labels: ["documentation"]
title: "Documentation: ..."
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to improve our documentation! Please fill out the form below to help us fix the issue quickly.

  - type: dropdown
    id: type
    attributes:
      label: Type of Documentation Issue
      description: What type of documentation issue is this?
      options:
        - Missing documentation
        - Incorrect documentation
        - Unclear documentation
        - Broken link
        - Other (specify in description)
    validations:
      required: true

  - type: input
    id: page
    attributes:
      label: Documentation Page
      description: Which page or section of the documentation is this about?
      placeholder: "e.g. https://docs.browser-use.com/customize/browser-settings > Context Configuration > headless"
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Issue Description
      description: "Describe what's wrong or missing in the documentation"
      placeholder: e.g. Docs should clarify whether BrowserSession(no_viewport=False) is supported when running in BrowserSession(headless=False) mode...
    validations:
      required: true

  - type: textarea
    id: suggestion
    attributes:
      label: Suggested Changes
      description: If you have specific suggestions for how to improve the documentation, please share them
      placeholder: |
        e.g. The documentation could be improved by adding one more line here:
        ```diff
        Use `BrowserSession(headless=False)` to open the browser window (aka headful mode).
        + Viewports are not supported when headful, if `headless=False` it will force `no_viewport=True`.
        ```
    validations:
      required: false
