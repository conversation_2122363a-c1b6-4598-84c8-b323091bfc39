// electron/main.js
// npx electron electron/main.js

const fs = require('fs')
const path = require('path')
const os = require('os')

// Create or append to a log file in the user's home directory
const logPath = path.join(os.homedir(), 'jaaz-log.txt')
// Check if the log file exists and delete it
if (fs.existsSync(logPath)) {
  fs.unlinkSync(logPath)
}

const logStream = fs.createWriteStream(logPath, { flags: 'a' })

// Redirect all stdout and stderr to the log file
process.stdout.write = process.stderr.write = logStream.write.bind(logStream)

// Optional: Add timestamps to log output
const origLog = console.log
console.log = (...args) => {
  const time = new Date().toISOString()
  origLog(`[${time}]`, ...args)
}

console.error = (...args) => {
  const time = new Date().toISOString()
  origLog(`[${time}][ERROR]`, ...args)
}

// Initial log entry
console.log('🟢 Jaaz Electron app starting...')

const { app, BrowserWindow, ipcMain, dialog, session } = require('electron')
const { spawn } = require('child_process')

const { autoUpdater } = require('electron-updater')

const net = require('net')

// Initialize settings service
const settingsService = require('./settingsService')

function findAvailablePort(startPort) {
  return new Promise((resolve, reject) => {
    const server = net.createServer()

    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        // Port is in use, try the next one
        findAvailablePort(startPort + 1).then(resolve).catch(reject)
      } else {
        reject(err)
      }
    })

    server.listen(startPort, () => {
      const port = server.address().port
      server.close(() => {
        resolve(port)
      })
    })
  })
}

let mainWindow
let serverProcess

// Function to start the NestJS server
async function startServer() {
  try {
    const port = await findAvailablePort(57988)
    console.log(`🚀 Starting NestJS server on port ${port}`)

    const isDev = process.env.NODE_ENV === 'development'
    const serverPath = isDev 
      ? path.join(__dirname, '..', 'dist', 'main.js')
      : path.join(process.resourcesPath, 'server', 'dist', 'main.js')

    // Check if server file exists
    if (!fs.existsSync(serverPath)) {
      console.error(`❌ Server file not found at: ${serverPath}`)
      return null
    }

    serverProcess = spawn('node', [serverPath], {
      env: { ...process.env, PORT: port },
      stdio: ['pipe', 'pipe', 'pipe']
    })

    serverProcess.stdout.on('data', (data) => {
      console.log(`[NestJS] ${data.toString()}`)
    })

    serverProcess.stderr.on('data', (data) => {
      console.error(`[NestJS Error] ${data.toString()}`)
    })

    serverProcess.on('close', (code) => {
      console.log(`🔴 NestJS server process exited with code ${code}`)
    })

    // Wait a bit for server to start
    await new Promise(resolve => setTimeout(resolve, 3000))

    return `http://127.0.0.1:${port}`
  } catch (error) {
    console.error('❌ Failed to start server:', error)
    return null
  }
}

function createWindow(serverUrl) {
  console.log('🪟 Creating main window...')
  
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false
    },
    titleBarStyle: 'hiddenInset',
    show: false
  })

  // Load the React app from NestJS server
  const loadUrl = serverUrl || 'http://127.0.0.1:57988'
  console.log(`🌐 Loading URL: ${loadUrl}`)
  
  mainWindow.loadURL(loadUrl)

  mainWindow.once('ready-to-show', () => {
    console.log('✅ Main window ready to show')
    mainWindow.show()
  })

  mainWindow.on('closed', () => {
    console.log('🔴 Main window closed')
    mainWindow = null
  })

  // Open DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools()
  }
}

app.whenReady().then(async () => {
  console.log('🟢 Electron app ready')
  
  // Start the NestJS server
  const serverUrl = await startServer()
  
  if (serverUrl) {
    createWindow(serverUrl)
  } else {
    console.error('❌ Failed to start server, creating window with default URL')
    createWindow()
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow(serverUrl)
    }
  })
})

app.on('window-all-closed', () => {
  console.log('🔴 All windows closed')
  
  // Kill server process
  if (serverProcess) {
    console.log('🔴 Killing NestJS server process')
    serverProcess.kill()
  }
  
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', () => {
  console.log('🔴 App before quit')
  
  // Kill server process
  if (serverProcess) {
    console.log('🔴 Killing NestJS server process')
    serverProcess.kill()
  }
})

// Auto-updater events
autoUpdater.on('checking-for-update', () => {
  console.log('🔍 Checking for update...')
})

autoUpdater.on('update-available', (info) => {
  console.log('📦 Update available:', info)
})

autoUpdater.on('update-not-available', (info) => {
  console.log('✅ Update not available:', info)
})

autoUpdater.on('error', (err) => {
  console.error('❌ Auto-updater error:', err)
})

autoUpdater.on('download-progress', (progressObj) => {
  let log_message = "Download speed: " + progressObj.bytesPerSecond
  log_message = log_message + ' - Downloaded ' + progressObj.percent + '%'
  log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')'
  console.log(log_message)
})

autoUpdater.on('update-downloaded', (info) => {
  console.log('✅ Update downloaded:', info)
  autoUpdater.quitAndInstall()
})

// Check for updates
if (process.env.NODE_ENV !== 'development') {
  autoUpdater.checkForUpdatesAndNotify()
}

console.log('🟢 Electron main process initialized')
