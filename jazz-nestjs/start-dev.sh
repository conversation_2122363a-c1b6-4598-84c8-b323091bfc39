#!/bin/bash

echo "🎵 Starting Jazz NestJS in Development Mode..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first:"
    echo "   brew install node"
    echo "   or visit https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing dependencies if needed..."

# Install backend dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "Installing backend dependencies..."
    npm install
fi

# Install frontend dependencies if react/node_modules doesn't exist
if [ ! -d "react/node_modules" ]; then
    echo "Installing frontend dependencies..."
    cd react
    npm install
    cd ..
fi

echo "🚀 Starting development servers..."

# Start both backend and frontend in development mode
npm run dev
