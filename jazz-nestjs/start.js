#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🎵 Starting Jazz NestJS Application...');

// Check if dist directory exists
const distPath = path.join(__dirname, 'dist');
if (!fs.existsSync(distPath)) {
  console.log('📦 Building application...');
  
  // Build the application
  const buildProcess = spawn('npx', ['nest', 'build'], {
    stdio: 'inherit',
    shell: true
  });
  
  buildProcess.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Build completed successfully');
      startApplication();
    } else {
      console.error('❌ Build failed');
      process.exit(1);
    }
  });
} else {
  startApplication();
}

function startApplication() {
  console.log('🚀 Starting NestJS server...');
  
  const serverProcess = spawn('node', [path.join(__dirname, 'dist', 'main.js')], {
    stdio: 'inherit',
    env: { ...process.env, PORT: 57988 }
  });
  
  serverProcess.on('close', (code) => {
    console.log(`🔴 Server process exited with code ${code}`);
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('🔴 Shutting down...');
    serverProcess.kill('SIGINT');
    process.exit(0);
  });
}
