# 🚀 Jazz NestJS 启动指南

## 📋 环境要求

### 1. 安装Node.js和npm

#### macOS用户 (推荐使用Homebrew):
```bash
# 安装Homebrew (如果还没有)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装Node.js (包含npm)
brew install node

# 验证安装
node --version  # 应该显示 v18.x.x 或更高
npm --version   # 应该显示 9.x.x 或更高
```

#### 其他方式:
- 直接下载: https://nodejs.org/ (选择LTS版本)
- 使用nvm: `curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash`

## 🎯 启动步骤

### 第一次启动 (完整安装)

1. **进入项目目录**:
```bash
cd /Users/<USER>/Documents/GitHub/jazz-nestjs
```

2. **运行一键启动脚本**:
```bash
./start-dev.sh
```

或者手动执行：

3. **安装后端依赖**:
```bash
npm install
```

4. **安装前端依赖**:
```bash
cd react
npm install
cd ..
```

5. **启动开发模式**:
```bash
npm run dev
```

### 后续启动 (依赖已安装)

```bash
cd /Users/<USER>/Documents/GitHub/jazz-nestjs
npm run dev
```

## 🌐 启动模式详解

### 1. 开发模式 (`npm run dev`)
- **推荐用于开发和测试**
- 同时启动后端和前端
- 支持热重载
- 自动打开Electron桌面应用

**访问地址**:
- React前端: http://localhost:5174
- NestJS后端: http://localhost:57988
- API文档: http://localhost:57988/api/docs

### 2. 仅启动后端

```bash
# 开发模式 (支持热重载)
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### 3. 仅启动前端

```bash
cd react
npm run dev
```

### 4. 生产模式 (Electron应用)

```bash
# 构建应用
npm run build
cd react && npm run build && cd ..

# 启动Electron桌面应用
npm start
```

## 🔧 常见问题解决

### 问题1: `bash: npm: command not found`
**解决方案**: 安装Node.js
```bash
brew install node
```

### 问题2: 端口被占用
**解决方案**: 检查并杀死占用进程
```bash
# 检查端口占用
lsof -i :57988  # 后端端口
lsof -i :5174   # 前端端口

# 杀死进程
kill -9 <PID>
```

### 问题3: 依赖安装失败
**解决方案**: 清理并重新安装
```bash
# 清理后端依赖
rm -rf node_modules package-lock.json
npm install

# 清理前端依赖
cd react
rm -rf node_modules package-lock.json
npm install
cd ..
```

### 问题4: 权限问题
**解决方案**: 修复npm权限
```bash
sudo chown -R $(whoami) ~/.npm
```

### 问题5: TypeScript编译错误
**解决方案**: 检查类型错误
```bash
npx tsc --noEmit
```

## 📊 启动成功标志

### 后端启动成功:
```
🌟 Jazz NestJS server is running on: http://127.0.0.1:57988
📚 API documentation available at: http://127.0.0.1:57988/api/docs
```

### 前端启动成功:
```
  VITE v5.4.10  ready in 1234 ms

  ➜  Local:   http://localhost:5174/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
```

### Electron启动成功:
- 自动打开桌面应用窗口
- 显示Jazz应用界面

## 🎮 使用功能

启动成功后，您可以：

1. **AI聊天** - 与不同的AI模型对话
2. **图像生成** - 使用AI生成和编辑图像  
3. **画布设计** - 在画布上进行创意设计
4. **文件管理** - 管理工作空间文件
5. **设置配置** - 配置AI模型和应用偏好

## 🔄 开发工作流

```bash
# 1. 启动开发环境
npm run dev

# 2. 修改代码 (自动热重载)
# - 后端代码在 src/ 目录
# - 前端代码在 react/src/ 目录

# 3. 查看API文档
# 访问 http://localhost:57988/api/docs

# 4. 构建生产版本
npm run build
cd react && npm run build && cd ..

# 5. 启动生产应用
npm start
```

## 📞 获取帮助

如果遇到问题：
1. 检查Node.js版本: `node --version` (需要18+)
2. 检查npm版本: `npm --version`
3. 查看错误日志
4. 参考 `README.md` 和 `MIGRATION_SUMMARY.md`

祝您使用愉快！🎵
