# Jazz NestJS - AI Design Agent

This is the NestJS version of the Jazz AI design agent desktop application, converted from the original FastAPI Python backend while maintaining full compatibility with the React frontend.

## Architecture

- **Backend**: NestJS (TypeScript) - Converted from FastAPI Python
- **Frontend**: React 19 + TypeScript + Vite
- **Desktop**: Electron
- **Database**: SQLite with TypeORM
- **WebSocket**: Socket.IO for real-time communication
- **UI**: Tailwind CSS + Radix UI components

## Features

- ✅ AI Chat with multiple model providers (OpenAI, Anthropic, Ollama)
- ✅ Image generation and editing tools
- ✅ Canvas for design work
- ✅ Workspace file management
- ✅ Real-time WebSocket communication
- ✅ Settings and configuration management
- ✅ Desktop app with auto-updater

## Quick Start

### 1. Install Dependencies

```bash
# Install backend dependencies
npm install

# Install frontend dependencies
cd react
npm install
cd ..
```

### 2. Development Mode

```bash
# Start both backend and frontend in development mode
npm run dev
```

This will start:
- NestJS backend on `http://localhost:57988`
- React frontend on `http://localhost:5174`
- Electron desktop app

### 3. Production Build

```bash
# Build the application
npm run build

# Start the production server
npm run start:prod

# Or build and run the Electron app
npm start
```

## API Endpoints

The NestJS backend provides the following API endpoints:

### Configuration
- `GET /api/config` - Get current configuration
- `POST /api/config` - Update configuration
- `GET /api/config/exists` - Check if configuration exists

### Agent
- `GET /api/list_models` - Get available AI models
- `GET /api/workspace_download` - Download workspace files

### Chat
- `GET /api/chat/conversations` - Get all conversations
- `POST /api/chat/conversations` - Create new conversation
- `GET /api/chat/conversations/:id` - Get conversation by ID
- `POST /api/chat/conversations/:id/messages` - Add message to conversation

### Workspace
- `GET /api/workspace/files` - List workspace files
- `GET /api/workspace/file` - Get file content
- `POST /api/workspace/file` - Create/update file
- `DELETE /api/workspace/file` - Delete file
- `POST /api/workspace/upload` - Upload file

### Image Tools
- `POST /api/image-tools/generate` - Generate image from prompt
- `POST /api/image-tools/edit` - Edit image with AI
- `POST /api/image-tools/upscale` - Upscale image
- `POST /api/image-tools/remove-background` - Remove image background

### Canvas
- `GET /api/canvas` - Get all canvas projects
- `POST /api/canvas` - Create new canvas project
- `GET /api/canvas/:id` - Get canvas project by ID
- `PUT /api/canvas/:id` - Update canvas project

### Settings
- `GET /api/settings` - Get application settings
- `POST /api/settings` - Update settings
- `GET /api/settings/theme` - Get theme settings
- `POST /api/settings/theme` - Update theme

## WebSocket Events

The application uses Socket.IO for real-time communication:

### Client to Server
- `chat_message` - Send chat message to AI
- `generate_image` - Request image generation
- `canvas_update` - Update canvas state
- `workspace_action` - Perform workspace action

### Server to Client
- `message_received` - Acknowledge message received
- `message_chunk` - Streaming message chunk
- `message_complete` - Complete message received
- `image_generated` - Image generation complete
- `canvas_updated` - Canvas state updated
- `error` - Error occurred

## Configuration

The application uses a TOML configuration file located at `user_data/config.toml`:

```toml
[ollama]
url = "http://localhost:11434"

[openai]
api_key = ""
base_url = "https://api.openai.com/v1"

[anthropic]
api_key = ""
```

## File Structure

```
jazz-nestjs/
├── src/                    # NestJS backend source
│   ├── modules/           # Feature modules
│   │   ├── agent/         # AI agent functionality
│   │   ├── chat/          # Chat management
│   │   ├── config/        # Configuration
│   │   ├── canvas/        # Canvas operations
│   │   ├── image-tools/   # Image processing
│   │   ├── settings/      # App settings
│   │   ├── websocket/     # WebSocket gateway
│   │   └── workspace/     # File management
│   ├── services/          # Shared services
│   └── main.ts           # Application entry point
├── react/                 # React frontend
│   ├── src/              # Frontend source
│   └── dist/             # Built frontend (production)
├── electron/             # Electron main process
├── assets/               # Application assets
└── user_data/           # User data directory
    ├── config.toml      # Configuration file
    ├── localmanus.db    # SQLite database
    ├── workspace/       # User workspace
    └── conversations/   # Chat conversations
```

## Development

### Backend Development

```bash
# Start NestJS in watch mode
npm run start:dev

# Run tests
npm run test

# Build for production
npm run build
```

### Frontend Development

```bash
cd react

# Start Vite dev server
npm run dev

# Build for production
npm run build
```

### Electron Development

```bash
# Start Electron in development mode
npm run dev:electron

# Build Electron app
npm run build:electron
```

## Migration from Python FastAPI

This NestJS version maintains 100% API compatibility with the original Python FastAPI backend. All endpoints, request/response formats, and WebSocket events remain identical, ensuring seamless frontend integration.

### Key Changes Made:
- ✅ Converted Python FastAPI routes to NestJS controllers
- ✅ Replaced aiosqlite with TypeORM + SQLite
- ✅ Converted python-socketio to Socket.IO
- ✅ Maintained all original API endpoints and formats
- ✅ Preserved WebSocket event names and data structures
- ✅ Kept configuration file format (TOML)
- ✅ Maintained file structure and user data organization

## License

This project maintains the same license as the original Jazz application.
