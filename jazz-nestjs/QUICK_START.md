# 🚀 Jazz NestJS 快速启动指南

## 📋 前置要求

- Node.js 18+ 
- npm 或 yarn
- Git

## ⚡ 快速开始

### 1. 克隆并进入项目
```bash
cd jazz-nestjs
```

### 2. 一键安装和启动
```bash
# 运行安装脚本
chmod +x setup.sh
./setup.sh

# 或者手动安装
npm install
cd react && npm install && cd ..
```

### 3. 启动应用
```bash
# 开发模式 (推荐)
npm run dev

# 或者生产模式
npm start
```

## 🎯 启动模式说明

### 开发模式 (`npm run dev`)
- 同时启动NestJS后端 (端口57988) 和React前端 (端口5174)
- 支持热重载
- 自动代理API请求
- 打开开发者工具

### 生产模式 (`npm start`)
- 构建并启动Electron桌面应用
- NestJS服务器内嵌运行
- React前端由NestJS静态服务

### 仅后端模式 (`npm run start:prod`)
- 只启动NestJS服务器
- 适合API开发和测试

## 🌐 访问地址

- **React前端**: http://localhost:5174 (开发模式)
- **NestJS后端**: http://localhost:57988
- **API文档**: http://localhost:57988/api/docs
- **桌面应用**: 自动启动Electron窗口

## 📁 重要目录

```
jazz-nestjs/
├── src/           # NestJS后端源码
├── react/         # React前端源码
├── electron/      # Electron主进程
├── dist/          # 后端构建输出
├── react/dist/    # 前端构建输出
└── user_data/     # 用户数据目录
    ├── config.toml      # 配置文件
    ├── localmanus.db    # SQLite数据库
    ├── workspace/       # 工作空间文件
    └── conversations/   # 聊天记录
```

## 🔧 配置设置

首次运行时，应用会创建默认配置文件 `user_data/config.toml`:

```toml
[ollama]
url = "http://localhost:11434"

[openai]
api_key = ""
base_url = "https://api.openai.com/v1"

[anthropic]
api_key = ""
```

### 配置AI模型

1. **Ollama** (本地模型)
   - 安装Ollama: https://ollama.ai
   - 拉取模型: `ollama pull llama3.2`
   - 确保Ollama运行在11434端口

2. **OpenAI**
   - 获取API密钥: https://platform.openai.com
   - 在配置文件中设置 `api_key`

3. **Anthropic**
   - 获取API密钥: https://console.anthropic.com
   - 在配置文件中设置 `api_key`

## 🛠️ 开发命令

```bash
# 后端开发
npm run start:dev      # 启动NestJS开发服务器
npm run build          # 构建后端
npm run test           # 运行测试

# 前端开发
cd react
npm run dev            # 启动Vite开发服务器
npm run build          # 构建前端
npm run preview        # 预览构建结果

# Electron开发
npm run dev:electron   # 启动Electron开发模式
npm run build:electron # 构建Electron应用
```

## 🐛 常见问题

### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :57988
lsof -i :5174

# 杀死占用进程
kill -9 <PID>
```

### 2. 依赖安装失败
```bash
# 清理缓存重新安装
rm -rf node_modules package-lock.json
npm install

# 前端依赖
cd react
rm -rf node_modules package-lock.json
npm install
```

### 3. 构建失败
```bash
# 检查TypeScript错误
npx tsc --noEmit

# 重新构建
npm run build
```

### 4. 数据库问题
```bash
# 删除数据库重新初始化
rm user_data/localmanus.db
# 重启应用会自动创建新数据库
```

## 📚 更多信息

- 📖 [完整文档](README.md)
- 🔄 [迁移总结](MIGRATION_SUMMARY.md)
- 🐛 [问题反馈](https://github.com/your-repo/issues)

## 🎉 开始使用

启动成功后，您可以：

1. **聊天对话** - 与AI模型进行对话
2. **图像生成** - 使用AI生成和编辑图像
3. **画布设计** - 在画布上进行设计工作
4. **文件管理** - 管理工作空间文件
5. **设置配置** - 自定义应用设置

享受使用Jazz NestJS！🎵
