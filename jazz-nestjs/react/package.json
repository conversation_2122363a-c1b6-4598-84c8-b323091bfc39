{"name": "jazz-react", "private": true, "version": "1.0.14", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@codemirror/autocomplete": "^6.18.1", "@codemirror/commands": "^6.7.1", "@codemirror/lang-angular": "^0.1.3", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-go": "^6.0.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-less": "^6.0.2", "@codemirror/lang-liquid": "^6.2.1", "@codemirror/lang-markdown": "^6.3.1", "@codemirror/lang-php": "^6.0.1", "@codemirror/lang-python": "^6.1.6", "@codemirror/lang-rust": "^6.0.1", "@codemirror/lang-sass": "^6.0.2", "@codemirror/lang-sql": "^6.8.0", "@codemirror/lang-vue": "^0.1.3", "@codemirror/lang-wast": "^6.0.2", "@codemirror/lang-xml": "^6.1.0", "@codemirror/lang-yaml": "^6.1.1", "@codemirror/language": "^6.10.3", "@codemirror/language-data": "^6.5.1", "@codemirror/legacy-modes": "^6.4.1", "@codemirror/lint": "^6.8.2", "@codemirror/merge": "^6.7.2", "@codemirror/search": "^6.5.7", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.34.2", "@codesandbox/sandpack-react": "^2.19.8", "@excalidraw/excalidraw": "^0.17.6", "@lexical/react": "^0.21.0", "@mdxeditor/editor": "^3.11.5", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-form": "^0.1.0", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-toolbar": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@tabler/icons-react": "^3.19.0", "@tanstack/react-query": "^5.59.16", "@tanstack/react-router": "^1.77.4", "@tanstack/router-devtools": "^1.77.4", "@tanstack/router-plugin": "^1.77.4", "@tiptap/core": "^2.8.0", "@tiptap/extension-blockquote": "^2.8.0", "@tiptap/extension-bold": "^2.8.0", "@tiptap/extension-bubble-menu": "^2.8.0", "@tiptap/extension-bullet-list": "^2.8.0", "@tiptap/extension-code": "^2.8.0", "@tiptap/extension-code-block": "^2.8.0", "@tiptap/extension-document": "^2.8.0", "@tiptap/extension-dropcursor": "^2.8.0", "@tiptap/extension-floating-menu": "^2.8.0", "@tiptap/extension-gapcursor": "^2.8.0", "@tiptap/extension-hard-break": "^2.8.0", "@tiptap/extension-heading": "^2.8.0", "@tiptap/extension-highlight": "^2.8.0", "@tiptap/extension-history": "^2.8.0", "@tiptap/extension-horizontal-rule": "^2.8.0", "@tiptap/extension-italic": "^2.8.0", "@tiptap/extension-link": "^2.8.0", "@tiptap/extension-list-item": "^2.8.0", "@tiptap/extension-ordered-list": "^2.8.0", "@tiptap/extension-paragraph": "^2.8.0", "@tiptap/extension-strike": "^2.8.0", "@tiptap/extension-text": "^2.8.0", "@tiptap/extension-text-style": "^2.8.0", "@tiptap/react": "^2.8.0", "@tiptap/starter-kit": "^2.8.0", "@tldraw/editor": "^2.4.0", "@tldraw/state": "^2.4.0", "@tldraw/state-react": "^2.4.0", "@tldraw/store": "^2.4.0", "@tldraw/tlschema": "^2.4.0", "@tldraw/utils": "^2.4.0", "@tldraw/validate": "^2.4.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@xyflow/react": "^12.3.2", "ahooks": "^3.8.1", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cm6-theme-basic-light": "^0.2.0", "codemirror": "^6.0.1", "lexical": "^0.21.0", "react": "^19.0.0", "react-dom": "^19.0.0", "socket.io-client": "^4.8.1", "sonner": "^1.7.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "tldraw": "^2.4.0", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@tailwindcss/vite": "^4.0.0-alpha.30", "@types/node": "^22.8.6", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "@vitejs/plugin-react": "^4.3.3", "cross-env": "^7.0.3", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "globals": "^15.11.0", "sass": "^1.80.6", "tailwindcss": "^4.0.0-alpha.30", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}}