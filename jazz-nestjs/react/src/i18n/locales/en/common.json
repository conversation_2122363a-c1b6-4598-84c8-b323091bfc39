{"languages": {"en": "English", "zh-CN": "简体中文"}, "buttons": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "submit": "Submit", "loading": "Loading...", "retry": "Retry"}, "common": {"yes": "Yes", "no": "No", "ok": "OK", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information"}, "messages": {"success": "Operation completed successfully", "error": "An error occurred", "loading": "Loading, please wait...", "noData": "No data available", "confirmDelete": "Are you sure you want to delete this item?", "saved": "Saved successfully", "deleted": "Deleted successfully"}, "notifications": {"title": "Notifications", "clear": "Clear", "markAllAsRead": "Mark all as read", "allRead": "All read", "newImageGenerated": "New image generated"}, "navigation": {"home": "Home", "canvas": "<PERSON><PERSON>", "settings": "Settings", "back": "Back"}, "update": {"title": "🎉 New Version Available", "description": "New version v{{version}} is available. Would you like to install it now?", "installNote": "Click \"Install Update\" to restart the app and install the new version.", "laterButton": "Re<PERSON> Later", "installButton": "Install Update", "installing": "Installing..."}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "loginToJaaz": "Login to Jaaz", "myAccount": "My Account", "loginDescription": "Login to use cloud AI models", "startLogin": "Start Login", "preparingLogin": "Preparing login...", "cancel": "Cancel", "loginSuccessMessage": "Login successful!", "authExpiredMessage": "Authorization expired, please login again", "authErrorMessage": "Error occurred while checking authorization status", "waitingForBrowser": "Waiting for login completion in browser...", "pollErrorMessage": "Failed to check login status, please try again", "preparingLoginMessage": "Preparing login...", "browserLoginMessage": "Please complete login in browser", "loginRequestFailed": "Login request failed, please try again", "logoutSuccessMessage": "Successfully logged out", "notLoggedIn": "Not logged in", "balance": "Balance", "recharge": "Recharge"}, "socket": {"connectionError": "Connection error ({{current}}/{{max}}): {{error}}", "maxRetriesReached": "Connection failed, please restart the application"}}