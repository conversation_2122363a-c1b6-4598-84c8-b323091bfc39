<!-- Please do not edit this file. Edit the `blah` field in the `package.json` instead. If in doubt, open an issue. -->


















# anser

 [![Support me on Patreon][badge_patreon]][patreon] [![Buy me a book][badge_amazon]][amazon] [![PayPal][badge_paypal_donate]][paypal-donations] [![Ask me anything](https://img.shields.io/badge/ask%20me-anything-1abc9c.svg)](https://github.com/IonicaBizau/ama) [![Travis](https://img.shields.io/travis/IonicaBizau/anser.svg)](https://travis-ci.org/IonicaBizau/anser/) [![Version](https://img.shields.io/npm/v/anser.svg)](https://www.npmjs.com/package/anser) [![Downloads](https://img.shields.io/npm/dt/anser.svg)](https://www.npmjs.com/package/anser) [![Get help on Codementor](https://cdn.codementor.io/badges/get_help_github.svg)](https://www.codementor.io/@johnnyb?utm_source=github&utm_medium=button&utm_term=johnnyb&utm_campaign=github)

<a href="https://www.buymeacoffee.com/H96WwChMy" target="_blank"><img src="https://www.buymeacoffee.com/assets/img/custom_images/yellow_img.png" alt="Buy Me A Coffee"></a>







> A low level parser for ANSI sequences.






## :rocket: Features


 - Converts text containing [ANSI color escape codes](http://en.wikipedia.org/wiki/ANSI_escape_code#Colors) into equivalent HTML elements.
 - Allows converting the input into JSON output.
 - HTML escaping
 - Converts links into HTML elements
 - Friendly APIs to use with  virtual dom libraries













## :cloud: Installation

```sh
# Using npm
npm install --save anser

# Using yarn
yarn add anser
```













## :clipboard: Example



```js
const Anser = require("anser");

const txt = "\u001b[38;5;196mHello\u001b[39m \u001b[48;5;226mWorld\u001b[49m";

console.log(Anser.ansiToHtml(txt));
// <span style="color:rgb(255, 0, 0)">Hello</span> <span style="background-color:rgb(255, 255, 0)">World</span>

console.log(Anser.ansiToHtml(txt, { use_classes: true }));
// <span class="ansi-palette-196-fg">Hello</span> <span class="ansi-palette-226-bg">World</span>

console.log(Anser.ansiToJson(txt));
// [ { content: '',
//     fg: null,
//     bg: null,
//     fg_truecolor: null,
//     bg_truecolor: null,
//     clearLine: undefined,
//     decoration: null,
//     was_processed: false,
//     isEmpty: [Function: isEmpty] },
//   { content: 'Hello',
//     fg: '255, 0, 0',
//     bg: null,
//     fg_truecolor: null,
//     bg_truecolor: null,
//     clearLine: false,
//     decoration: null,
//     was_processed: true,
//     isEmpty: [Function: isEmpty] },
//   { content: ' ',
//     fg: null,
//     bg: null,
//     fg_truecolor: null,
//     bg_truecolor: null,
//     clearLine: false,
//     decoration: null,
//     was_processed: false,
//     isEmpty: [Function: isEmpty] },
//   { content: 'World',
//     fg: null,
//     bg: '255, 255, 0',
//     fg_truecolor: null,
//     bg_truecolor: null,
//     clearLine: false,
//     decoration: null,
//     was_processed: true,
//     isEmpty: [Function: isEmpty] },
//   { content: '',
//     fg: null,
//     bg: null,
//     fg_truecolor: null,
//     bg_truecolor: null,
//     clearLine: false,
//     decoration: null,
//     was_processed: false,
//     isEmpty: [Function: isEmpty] } ]
```






When using **TypeScript** without --esModuleInterop enabled you can do the following:
```ts
import Anser = require('anser');
const txt = "\u001b[38;5;196mHello\u001b[39m \u001b[48;5;226mWorld\u001b[49m";
console.log(Anser.ansiToHtml(txt));
// <span style="color:rgb(255, 0, 0)">Hello</span> <span style="background-color:rgb(255, 255, 0)">World</span>
```

Or with --esModuleInterop enabled you can do the following:
```ts
import Anser from 'anser';
const txt = "\u001b[38;5;196mHello\u001b[39m \u001b[48;5;226mWorld\u001b[49m";
console.log(Anser.ansiToHtml(txt));
// <span style="color:rgb(255, 0, 0)">Hello</span> <span style="background-color:rgb(255, 255, 0)">World</span>
```








## :question: Get Help

There are few ways to get help:



 1. Please [post questions on Stack Overflow](https://stackoverflow.com/questions/ask). You can open issues with questions, as long you add a link to your Stack Overflow question.
 2. For bug reports and feature requests, open issues. :bug:
 3. For direct and quick help, you can [use Codementor](https://www.codementor.io/johnnyb). :rocket:







## :memo: Documentation


Add several widely used style codes

### `processChunk(text, options, markup)`
Processes the current chunk of text.

#### Params

- **String** `text`: The input text.
- **Object** `options`: An object containing the following fields:
 - `json` (Boolean): If `true`, the result will be an object.
 - `use_classes` (Boolean): If `true`, HTML classes will be appended to the HTML output.
- **Boolean** `markup`: If false, the colors will not be parsed.

#### Return
- **Object|String** The result (object if `json` is wanted back or string otherwise).














## :yum: How to contribute
Have an idea? Found a bug? See [how to contribute][contributing].


## :sparkling_heart: Support my projects
I open-source almost everything I can, and I try to reply to everyone needing help using these projects. Obviously,
this takes time. You can integrate and use these projects in your applications *for free*! You can even change the source code and redistribute (even resell it).

However, if you get some profit from this or just want to encourage me to continue creating stuff, there are few ways you can do it:


 - Starring and sharing the projects you like :rocket:
 - [![Buy me a book][badge_amazon]][amazon]—I love books! I will remember you after years if you buy me one. :grin: :book:
 - [![PayPal][badge_paypal]][paypal-donations]—You can make one-time donations via PayPal. I'll probably buy a ~~coffee~~ tea. :tea:
 - [![Support me on Patreon][badge_patreon]][patreon]—Set up a recurring monthly donation and you will get interesting news about what I'm doing (things that I don't share with everyone).
 - **Bitcoin**—You can send me bitcoins at this address (or scanning the code below): `**********************************`

    ![](https://i.imgur.com/z6OQI95.png)


Thanks! :heart:









## :cake: Thanks
This project is highly based on [`ansi_up`](https://github.com/drudru/ansi_up), by [@drudru](https://github.com/drudru/). Thanks! :cake:








## :dizzy: Where is this library used?
If you are using this library in one of your projects, add it in this list. :sparkles:

 - `@10102004tan/react-native-select-dropdown-v2`
 - `@_nomtek/react-native-shimmer-animation`
 - `@adatechnology/react-native-android-getnet-pos`
 - `@adembacaj/react-native-google-pay`
 - `@affinidi/affinidi-auth-sdk-kernel`
 - `@aibulat/naser`
 - `@ajaykewat/myboilerplate`
 - `@ali5049/react-native-buttons`
 - `@alloc/react-error-overlay`
 - `@apardellass/react-native-audio-stream`
 - `@ascorbic/gatsby`
 - `@assystant/firebase`
 - `@atom-ide-community/nuclide-commons-ui`
 - `@axio/react-dev-utils`
 - `@aysea/react-native-ui-library`
 - `@azalpacir/react-native-dhp-printer`
 - `@baloochat/react-native-svg-uri`
 - `@blkmarketco/components-library`
 - `@brantalikp/rn-resize`
 - `@callstack/react-native-visionos`
 - `@classflow/react-dev-utils`
 - `@classyllama/code-style`
 - `@cocalc/ansi-to-react`
 - `@codedown/ansi-to-react`
 - `@codesandbox/sandpack-react`
 - `@codewars/jest-reporter`
 - `@con-test/react-native-concent-common`
 - `@corelmax/react-native-my2c2p-sdk`
 - `@cs6/react-native-test-native-view-library`
 - `@cubejs-client/playground`
 - `@curvenote/ansi-to-react`
 - `@damian.lnc/core`
 - `@damruravihara/react-native-testing-package`
 - `@davidmccabe/sandpack-react`
 - `@deix/paganini-ui`
 - `@devpodio/console`
 - `@digibear/socket-bridge`
 - `@distasy/react-native-fix`
 - `@ehyland-org/react-error-overlay`
 - `@enkeledi/react-native-week-month-date-picker`
 - `@expensify/react-native`
 - `@felipesimmi/react-native-datalogic-module`
 - `@geeky-apo/react-native-advanced-clipboard`
 - `@hbglobal/react-native-actions-shortcuts`
 - `@hemith/react-native-tnk`
 - `@heora/cubejs-client-playground`
 - `@hjopel/react-native`
 - `@idas1/ui-component-lib`
 - `@jfilipe-sparta/react-native-module_2`
 - `@kloudlite/design-system`
 - `@ko-developerhong/react-native-multiple-image-picker`
 - `@kui-shell/plugin-client-common`
 - `@labiebhn_/react-native-multiplier`
 - `@lehuyaa/my-assets`
 - `@leonchabbey/react-native-tvos`
 - `@logisticinfotech/react-native-geocoding-reversegeocoding`
 - `@navabi/react-native-ssl-pinning`
 - `@naze/error`
 - `@ndjoe/react-native-bluetooth-escpos-printer`
 - `@next/react-dev-overlay`
 - `@nonfx/flow-log`
 - `@office-iss/react-native-win32`
 - `@oiti/rn-liveness2d`
 - `@ollion/flow-log`
 - `@opensumi/ide-debug`
 - `@orgbluetooth/react-native-arunpayupayment`
 - `@plansys/react-dev-utils`
 - `@popbot/react-native-macos`
 - `@praella/localisationist`
 - `@prague-digi/react-error-overlay`
 - `@prozilla-os/terminal`
 - `@react-native-ui-design/button`
 - `@redux-devtools/inspector-monitor-trace-tab`
 - `@routebuddies/react-native-mapbox-navigation`
 - `@routeslk/react-native-eva-icons`
 - `@saad27/react-native-bottom-tab-tour`
 - `@sebastien_rts/react-native-tvos`
 - `@sephriot/react-native-persistable-uri`
 - `@shuvi/error-overlay`
 - `@sidghimire/react-native-mapbox-navigation`
 - `@soyjak/utils`
 - `@sridharetikala/react-native-rn-lib-custom-components`
 - `@status-im/react-native-transparent-video`
 - `@suen/react-native-tvos`
 - `@taingo97/react-native-bluetooth-escpos-printer`
 - `@taingo97/react-native-bluetooth-xprinter`
 - `@taingo97/react-native-expo-key-rsa-kt`
 - `@taingo97/react-native-generate-key-rsa`
 - `@taingo97/react-native-key-rsa`
 - `@taingo97/react-native-print-xprinter`
 - `@taingo97/react-native-printer-imin`
 - `@taingo97/react-native-rsa-expo`
 - `@taingo97/react-native-sunmi-printer`
 - `@taingo97/react-native-telpo-printer`
 - `@tasumaniadiabori/react-native-draggable-flatlist`
 - `@th3rdwave/react-native`
 - `@theia/console`
 - `@throttleup/gatsby`
 - `@tlgeo/react-native-gdal`
 - `@trackier/react-native-trackier`
 - `@unforgiven/react-native`
 - `@viankakrisna/react-dev-utils`
 - `@viankakrisna/react-error-overlay`
 - `@vidit-me/react-native-wheely`
 - `@zotasys/native`
 - `act_mvvm_shop_cart`
 - `ada-pack`
 - `adyen-rn`
 - `ansi-html-themed`
 - `ansi-log-renderer`
 - `ansi-to-json`
 - `ansi-to-react`
 - `ansi-to-react-with-options`
 - `ansi-to-vue3`
 - `ansicolor-utils`
 - `archlibrary`
 - `awesome-module-kd`
 - `biometric-st`
 - `birken-react-native-community-image-editor`
 - `bn-react-native`
 - `callstack-task-react-dev-utils`
 - `callstack-task-react-error-overlay`
 - `checkbox-component`
 - `cycle-dev-utils`
 - `dogandev-simple-toast`
 - `dulcet-error-overlay`
 - `emg-api`
 - `fawaterak-online-payment`
 - `fluent.adflow.reactnativesdk`
 - `fluent.adflow.reactnativesdk-alpha`
 - `fmsl`
 - `framework_test_library_sixdee`
 - `framework_test_library_sixdee_new`
 - `framework_test_library_sixdee_new_new`
 - `gamification-integration-new`
 - `gatsby`
 - `gaurav-react-native-loop`
 - `genz-native-elements`
 - `ipynb2html`
 - `ipynb2html-fix`
 - `jesh-calculation`
 - `jnf-accesscontrol-rnttl`
 - `jrennsoh88-react-native-scroll-indicator`
 - `khaled-salem-custom-components`
 - `kunai`
 - `lambda-dev-utils`
 - `linklog`
 - `mesh-devtool`
 - `multi-term`
 - `native-apple-login`
 - `native-date-picker-module`
 - `native-google-login`
 - `native-kakao-login`
 - `native-modal-damage-vehicle`
 - `new-awesome-4321`
 - `nove-repository`
 - `npm_one_12_34_1_`
 - `npm_one_1_2_3`
 - `npm_one_2_2`
 - `npm_qwerty`
 - `nuclide`
 - `nuclide-commons-ui`
 - `payutesting`
 - `pnm-yph-react-native-custom-components`
 - `procrealms-client`
 - `raact-native-arunramya151`
 - `reac-native-arun-ramya-test`
 - `react-ansi`
 - `react-ansi-eighteen`
 - `react-dev-utils-custom-hmr`
 - `react-dev-utils-extra`
 - `react-dev-utils-sebfl-update`
 - `react-error-guard`
 - `react-error-overlay-canary`
 - `react-error-overlay-dangerous`
 - `react-ipynb-renderer-katex`
 - `react-native`
 - `react-native-adarsh_react_native_video_player`
 - `react-native-addition`
 - `react-native-android-native-view`
 - `react-native-android-scoped-storage`
 - `react-native-android-video-player-view`
 - `react-native-animate-text`
 - `react-native-ansi-view`
 - `react-native-app-bubble`
 - `react-native-app-integrity-checksum`
 - `react-native-arps-authorize-net`
 - `react-native-arun-ramya-test`
 - `react-native-arunjeyam1987`
 - `react-native-arunmeena1987`
 - `react-native-arunramya151`
 - `react-native-auth-service-client`
 - `react-native-aventonfacetec-aventon`
 - `react-native-avinash_jha_test_plugin`
 - `react-native-awesome-android-123`
 - `react-native-awesome-android-123-zeotap`
 - `react-native-awesome-module-dharmesh`
 - `react-native-awesome-module-latest`
 - `react-native-awesome-module-two`
 - `react-native-azure-communication-services`
 - `react-native-badge-control`
 - `react-native-basic-app`
 - `react-native-basic-screen`
 - `react-native-biometric-authenticate`
 - `react-native-bleccs-components`
 - `react-native-bluetooth-device-detect`
 - `react-native-bottom-tab-designs`
 - `react-native-bridge-package`
 - `react-native-bubble-chart`
 - `react-native-build-vesion-getter`
 - `react-native-check-component`
 - `react-native-chenaar`
 - `react-native-components-design`
 - `react-native-conekta-card-tokenizer`
 - `react-native-contact-list`
 - `react-native-coocaatv`
 - `react-native-cplus`
 - `react-native-create-video-thumbnail`
 - `react-native-ctp-odp`
 - `react-native-custom-poccomponent`
 - `react-native-custom-poccomponent-next`
 - `react-native-datacapture-core`
 - `react-native-dff-components-demo`
 - `react-native-dhp-printer`
 - `react-native-dimensions-layout`
 - `react-native-dish`
 - `react-native-dm-player`
 - `react-native-dsphoto-module`
 - `react-native-dummy-view`
 - `react-native-escape`
 - `react-native-expendable-view`
 - `react-native-expo-read-sms-ts-kf`
 - `react-native-fedlight-dsm`
 - `react-native-firebase-messaging`
 - `react-native-flyy`
 - `react-native-get-countries`
 - `react-native-ghn-ekyc`
 - `react-native-hlf-wrapper`
 - `react-native-innity-2`
 - `react-native-innity-remaster`
 - `react-native-input-library`
 - `react-native-is7`
 - `react-native-jsi-device-info`
 - `react-native-kakao-maps`
 - `react-native-klarify-ios`
 - `react-native-klarify-ui`
 - `react-native-klc`
 - `react-native-lib-test-rn-1`
 - `react-native-library-testing-422522`
 - `react-native-line-login-android`
 - `react-native-login-demo-test`
 - `react-native-lowlatency`
 - `react-native-loyalty-platforms`
 - `react-native-macos`
 - `react-native-manh-test`
 - `react-native-manual-ios-sdk`
 - `react-native-meon-edoc`
 - `react-native-meonsdk`
 - `react-native-meontool`
 - `react-native-modal-progress-bar`
 - `react-native-module-for-testing`
 - `react-native-multi-bluetooth-printer`
 - `react-native-multiplier-altroncoso`
 - `react-native-multiplier-component`
 - `react-native-multiplier-demo`
 - `react-native-multiplier2`
 - `react-native-multiply`
 - `react-native-multiply-component`
 - `react-native-multiselector`
 - `react-native-mun-kit`
 - `react-native-my-first-try-arun-ramya`
 - `react-native-mybuttonlibrary`
 - `react-native-mymap`
 - `react-native-native-audio-engine`
 - `react-native-native-ios-test1`
 - `react-native-nativewind`
 - `react-native-nghia-sharering`
 - `react-native-nice-learning`
 - `react-native-nyx-printer`
 - `react-native-offline-notice`
 - `react-native-okhttp-fork`
 - `react-native-onramp`
 - `react-native-otp-custom-library`
 - `react-native-patch-java`
 - `react-native-paynow-generator`
 - `react-native-payu-payment`
 - `react-native-payu-payment-testing`
 - `react-native-plugpag-wrapper`
 - `react-native-pme-loading`
 - `react-native-priya-demo-button`
 - `react-native-progress-arrow`
 - `react-native-pulsator-native`
 - `react-native-rabbitmq-all`
 - `react-native-radio-bic-group-lib`
 - `react-native-reanimated-sortable-list`
 - `react-native-recent-framework-update`
 - `react-native-remote-clover`
 - `react-native-remote-update`
 - `react-native-responsive-helper`
 - `react-native-responsive-size`
 - `react-native-return-usb-data`
 - `react-native-reusableinputwithpaper`
 - `react-native-rn-common-components-example`
 - `react-native-rn-icons-library`
 - `react-native-rn-tolkaplayer`
 - `react-native-rom-components`
 - `react-native-rtn-ips-poslin-test`
 - `react-native-s-airlines`
 - `react-native-sandycomponent`
 - `react-native-savczuk-feature-library`
 - `react-native-sayhello-module`
 - `react-native-screen-idle-timer`
 - `react-native-scroll-tab-to-index`
 - `react-native-sf`
 - `react-native-shared-gesture`
 - `react-native-sharing-intent`
 - `react-native-simple-timeline`
 - `react-native-smartcall-videodemo`
 - `react-native-sp-test-common`
 - `react-native-sunmi-printer-hk`
 - `react-native-superapis-transbank-pos`
 - `react-native-swal`
 - `react-native-teads-sdk-module`
 - `react-native-tejab41097-sample-library`
 - `react-native-teknoctrl-components`
 - `react-native-test-comlibrary`
 - `react-native-test-module-hhh`
 - `react-native-test-multiplier-library`
 - `react-native-test-tooltip`
 - `react-native-test-view`
 - `react-native-testweb`
 - `react-native-ticker-tape`
 - `react-native-tone-framework`
 - `react-native-tone-test`
 - `react-native-tones`
 - `react-native-trackier`
 - `react-native-transtracker-library`
 - `react-native-tvos`
 - `react-native-tvos-nz`
 - `react-native-ui-components-library`
 - `react-native-uvc-camera-android`
 - `react-native-version-app`
 - `react-native-windows`
 - `react-native-withframework-check`
 - `react-native-wtf`
 - `react-native-xprinter-thermal-ble`
 - `react-native-ytximkit`
 - `react-webpack-build-helper`
 - `react_native_pandey_marquee`
 - `reactnatively`
 - `reat-native-multiplierkpr`
 - `redux-devtools-trace-monitor`
 - `renu-pack`
 - `rn-adyen-dropin`
 - `rn-check-btn`
 - `rn-circular-chart`
 - `rn-counter-demo`
 - `rn-currency-formatter`
 - `rn-flatlist-fork`
 - `rn-horizontal-list`
 - `rn-keyboard-avoiding-view`
 - `rn-my-library`
 - `rn-pay-sdk`
 - `rn-session-multiplier-demo`
 - `rn-tm-notify`
 - `rn-use-modal-hook`
 - `rn_unique_device_id`
 - `rnttlock`
 - `simple-library-react-native`
 - `singularityui-tailer`
 - `smart_one_connect`
 - `smile-ansi-to-react`
 - `stack-frame-overlay`
 - `test-haptik-lib`
 - `test-library-123`
 - `test-zeo-collect`
 - `timer-react-dev-utils`
 - `transformime`
 - `userfetch`
 - `uyun-react-dev-utils`
 - `vantiq-react`
 - `vision-camera-base64-resized`
 - `vision-camera-plugin-face-detector`
 - `vision-camera-plugin-scan-faces`
 - `webpack-isomorphic-dev-middleware`
 - `webpack-universal-compiler`
 - `wifi_configuration_package`
 - `wildev-react-native`
 - `yarn-react-hook-form`
 - `zc-react-dev-utils`
 - `zf-rn-testaa`











## :scroll: License

[MIT][license] © [Ionică Bizău][website]






[license]: /LICENSE
[website]: https://ionicabizau.net
[contributing]: /CONTRIBUTING.md
[docs]: /DOCUMENTATION.md
[badge_patreon]: https://ionicabizau.github.io/badges/patreon.svg
[badge_amazon]: https://ionicabizau.github.io/badges/amazon.svg
[badge_paypal]: https://ionicabizau.github.io/badges/paypal.svg
[badge_paypal_donate]: https://ionicabizau.github.io/badges/paypal_donate.svg
[patreon]: https://www.patreon.com/ionicabizau
[amazon]: http://amzn.eu/hRo9sIZ
[paypal-donations]: https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=RVXDDLKKLQRJW
