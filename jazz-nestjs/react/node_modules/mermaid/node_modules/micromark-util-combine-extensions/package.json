{"name": "micromark-util-combine-extensions", "version": "1.1.0", "description": "micromark utility to combine syntax or html extensions", "license": "MIT", "keywords": ["micromark", "util", "utility", "extension", "combine", "merge"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-combine-extensions", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js"], "exports": "./index.js", "dependencies": {"micromark-util-chunked": "^1.0.0", "micromark-util-types": "^1.0.0"}, "scripts": {}, "xo": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}