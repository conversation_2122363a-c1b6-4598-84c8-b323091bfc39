/**
 * Combine multiple syntax extensions into one.
 *
 * @param {Array<Extension>} extensions
 *   List of syntax extensions.
 * @returns {NormalizedExtension}
 *   A single combined extension.
 */
export function combineExtensions(
  extensions: Array<Extension>
): NormalizedExtension
/**
 * Combine multiple HTML extensions into one.
 *
 * @param {Array<HtmlExtension>} htmlExtensions
 *   List of HTML extensions.
 * @returns {HtmlExtension}
 *   A single combined HTML extension.
 */
export function combineHtmlExtensions(
  htmlExtensions: Array<HtmlExtension>
): HtmlExtension
export type Extension = import('micromark-util-types').Extension
export type Handles = import('micromark-util-types').Handles
export type HtmlExtension = import('micromark-util-types').HtmlExtension
export type NormalizedExtension =
  import('micromark-util-types').NormalizedExtension
