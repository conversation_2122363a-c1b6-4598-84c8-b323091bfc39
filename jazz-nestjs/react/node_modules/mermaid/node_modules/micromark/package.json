{"name": "micromark", "version": "3.2.0", "description": "small commonmark compliant markdown parser with positional info and concrete tokens", "license": "MIT", "keywords": ["commonmark", "compiler", "gfm", "html", "lexer", "markdown", "markup", "md", "unified", "parse", "parser", "plugin", "process", "remark", "render", "renderer", "token", "tokenizer"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "dev/index.d.ts", "files": ["dev/", "lib/", "index.d.ts", "index.js", "stream.d.ts", "stream.js"], "exports": {".": {"types": "./dev/index.d.ts", "development": "./dev/index.js", "default": "./index.js"}, "./stream": {"types": "./dev/stream.d.ts", "development": "./dev/stream.js", "default": "./stream.js"}, "./stream.js": {"types": "./dev/stream.d.ts", "development": "./dev/stream.js", "default": "./stream.js"}, "./lib/compile": {"types": "./dev/lib/compile.d.ts", "development": "./dev/lib/compile.js", "default": "./lib/compile.js"}, "./lib/compile.js": {"types": "./dev/lib/compile.d.ts", "development": "./dev/lib/compile.js", "default": "./lib/compile.js"}, "./lib/parse": {"types": "./dev/lib/parse.d.ts", "development": "./dev/lib/parse.js", "default": "./lib/parse.js"}, "./lib/parse.js": {"types": "./dev/lib/parse.d.ts", "development": "./dev/lib/parse.js", "default": "./lib/parse.js"}, "./lib/postprocess": {"types": "./dev/lib/postprocess.d.ts", "development": "./dev/lib/postprocess.js", "default": "./lib/postprocess.js"}, "./lib/postprocess.js": {"types": "./dev/lib/postprocess.d.ts", "development": "./dev/lib/postprocess.js", "default": "./lib/postprocess.js"}, "./lib/preprocess": {"types": "./dev/lib/preprocess.d.ts", "development": "./dev/lib/preprocess.js", "default": "./lib/preprocess.js"}, "./lib/preprocess.js": {"types": "./dev/lib/preprocess.d.ts", "development": "./dev/lib/preprocess.js", "default": "./lib/preprocess.js"}}, "dependencies": {"@types/debug": "^4.0.0", "debug": "^4.0.0", "micromark-core-commonmark": "^1.0.1", "micromark-factory-space": "^1.0.0", "micromark-util-character": "^1.0.0", "micromark-util-chunked": "^1.0.0", "micromark-util-combine-extensions": "^1.0.0", "micromark-util-decode-numeric-character-reference": "^1.0.0", "micromark-util-encode": "^1.0.0", "micromark-util-normalize-identifier": "^1.0.0", "micromark-util-resolve-all": "^1.0.0", "micromark-util-sanitize-uri": "^1.0.0", "micromark-util-subtokenize": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.1", "decode-named-character-reference": "^1.0.0", "uvu": "^0.5.0"}, "scripts": {"build": "micromark-build"}, "xo": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}