/** @satisfies {Extension['document']} */
export const document: {
  42: import('micromark-util-types').Construct
  43: import('micromark-util-types').Construct
  45: import('micromark-util-types').Construct
  48: import('micromark-util-types').Construct
  49: import('micromark-util-types').Construct
  50: import('micromark-util-types').Construct
  51: import('micromark-util-types').Construct
  52: import('micromark-util-types').Construct
  53: import('micromark-util-types').Construct
  54: import('micromark-util-types').Construct
  55: import('micromark-util-types').Construct
  56: import('micromark-util-types').Construct
  57: import('micromark-util-types').Construct
  62: import('micromark-util-types').Construct
}
/** @satisfies {Extension['contentInitial']} */
export const contentInitial: {
  91: import('micromark-util-types').Construct
}
/** @satisfies {Extension['flowInitial']} */
export const flowInitial: {
  [-2]: import('micromark-util-types').Construct
  [-1]: import('micromark-util-types').Construct
  32: import('micromark-util-types').Construct
}
/** @satisfies {Extension['flow']} */
export const flow: {
  35: import('micromark-util-types').Construct
  42: import('micromark-util-types').Construct
  45: import('micromark-util-types').Construct[]
  60: import('micromark-util-types').Construct
  61: import('micromark-util-types').Construct
  95: import('micromark-util-types').Construct
  96: import('micromark-util-types').Construct
  126: import('micromark-util-types').Construct
}
/** @satisfies {Extension['string']} */
export const string: {
  38: import('micromark-util-types').Construct
  92: import('micromark-util-types').Construct
}
/** @satisfies {Extension['text']} */
export const text: {
  [-5]: import('micromark-util-types').Construct
  [-4]: import('micromark-util-types').Construct
  [-3]: import('micromark-util-types').Construct
  33: import('micromark-util-types').Construct
  38: import('micromark-util-types').Construct
  42: import('micromark-util-types').Construct
  60: import('micromark-util-types').Construct[]
  91: import('micromark-util-types').Construct
  92: import('micromark-util-types').Construct[]
  93: import('micromark-util-types').Construct
  95: import('micromark-util-types').Construct
  96: import('micromark-util-types').Construct
}
export namespace insideSpan {
  const _null: (
    | import('micromark-util-types').Construct
    | {
        resolveAll: import('micromark-util-types').Resolver
      }
  )[]
  export {_null as null}
}
export namespace attentionMarkers {
  const _null_1: (42 | 95)[]
  export {_null_1 as null}
}
export namespace disable {
  const _null_2: never[]
  export {_null_2 as null}
}
export type Extension = import('micromark-util-types').Extension
