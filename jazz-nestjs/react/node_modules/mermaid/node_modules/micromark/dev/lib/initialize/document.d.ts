/** @type {InitialConstruct} */
export const document: InitialConstruct
export type Construct = import('micromark-util-types').Construct
export type ContainerState = import('micromark-util-types').ContainerState
export type InitialConstruct = import('micromark-util-types').InitialConstruct
export type Initializer = import('micromark-util-types').Initializer
export type Point = import('micromark-util-types').Point
export type State = import('micromark-util-types').State
export type Token = import('micromark-util-types').Token
export type TokenizeContext = import('micromark-util-types').TokenizeContext
export type Tokenizer = import('micromark-util-types').Tokenizer
export type StackItem = [Construct, ContainerState]
