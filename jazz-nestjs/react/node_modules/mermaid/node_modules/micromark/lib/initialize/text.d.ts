export namespace resolver {
  const resolveAll: import('micromark-util-types').Resolver
}
export const string: import('micromark-util-types').InitialConstruct
export const text: import('micromark-util-types').InitialConstruct
export type Code = import('micromark-util-types').Code
export type InitialConstruct = import('micromark-util-types').InitialConstruct
export type Initializer = import('micromark-util-types').Initializer
export type Resolver = import('micromark-util-types').Resolver
export type State = import('micromark-util-types').State
export type TokenizeContext = import('micromark-util-types').TokenizeContext
