/**
 * @param {ParseOptions | null | undefined} [options]
 * @returns {ParseContext}
 */
export function parse(options?: ParseOptions | null | undefined): ParseContext
export type Create = import('micromark-util-types').Create
export type FullNormalizedExtension =
  import('micromark-util-types').FullNormalizedExtension
export type InitialConstruct = import('micromark-util-types').InitialConstruct
export type ParseContext = import('micromark-util-types').ParseContext
export type ParseOptions = import('micromark-util-types').ParseOptions
