export namespace values {
  const ht: '\t'
  const lf: '\n'
  const cr: '\r'
  const space: ' '
  const exclamationMark: '!'
  const quotationMark: '"'
  const numberSign: '#'
  const dollarSign: '$'
  const percentSign: '%'
  const ampersand: '&'
  const apostrophe: "'"
  const leftParenthesis: '('
  const rightParenthesis: ')'
  const asterisk: '*'
  const plusSign: '+'
  const comma: ','
  const dash: '-'
  const dot: '.'
  const slash: '/'
  const digit0: '0'
  const digit1: '1'
  const digit2: '2'
  const digit3: '3'
  const digit4: '4'
  const digit5: '5'
  const digit6: '6'
  const digit7: '7'
  const digit8: '8'
  const digit9: '9'
  const colon: ':'
  const semicolon: ';'
  const lessThan: '<'
  const equalsTo: '='
  const greaterThan: '>'
  const questionMark: '?'
  const atSign: '@'
  const uppercaseA: 'A'
  const uppercaseB: 'B'
  const uppercaseC: 'C'
  const uppercaseD: 'D'
  const uppercaseE: 'E'
  const uppercaseF: 'F'
  const uppercaseG: 'G'
  const uppercaseH: 'H'
  const uppercaseI: 'I'
  const uppercaseJ: 'J'
  const uppercaseK: 'K'
  const uppercaseL: 'L'
  const uppercaseM: 'M'
  const uppercaseN: 'N'
  const uppercaseO: 'O'
  const uppercaseP: 'P'
  const uppercaseQ: 'Q'
  const uppercaseR: 'R'
  const uppercaseS: 'S'
  const uppercaseT: 'T'
  const uppercaseU: 'U'
  const uppercaseV: 'V'
  const uppercaseW: 'W'
  const uppercaseX: 'X'
  const uppercaseY: 'Y'
  const uppercaseZ: 'Z'
  const leftSquareBracket: '['
  const backslash: '\\'
  const rightSquareBracket: ']'
  const caret: '^'
  const underscore: '_'
  const graveAccent: '`'
  const lowercaseA: 'a'
  const lowercaseB: 'b'
  const lowercaseC: 'c'
  const lowercaseD: 'd'
  const lowercaseE: 'e'
  const lowercaseF: 'f'
  const lowercaseG: 'g'
  const lowercaseH: 'h'
  const lowercaseI: 'i'
  const lowercaseJ: 'j'
  const lowercaseK: 'k'
  const lowercaseL: 'l'
  const lowercaseM: 'm'
  const lowercaseN: 'n'
  const lowercaseO: 'o'
  const lowercaseP: 'p'
  const lowercaseQ: 'q'
  const lowercaseR: 'r'
  const lowercaseS: 's'
  const lowercaseT: 't'
  const lowercaseU: 'u'
  const lowercaseV: 'v'
  const lowercaseW: 'w'
  const lowercaseX: 'x'
  const lowercaseY: 'y'
  const lowercaseZ: 'z'
  const leftCurlyBrace: '{'
  const verticalBar: '|'
  const rightCurlyBrace: '}'
  const tilde: '~'
  const replacementCharacter: '�'
}
