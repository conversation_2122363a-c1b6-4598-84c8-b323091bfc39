export namespace types {
  const data: 'data'
  const whitespace: 'whitespace'
  const lineEnding: 'lineEnding'
  const lineEndingBlank: 'lineEndingBlank'
  const linePrefix: 'linePrefix'
  const lineSuffix: 'lineSuffix'
  const atxHeading: 'atxHeading'
  const atxHeadingSequence: 'atxHeadingSequence'
  const atxHeadingText: 'atxHeadingText'
  const autolink: 'autolink'
  const autolinkEmail: 'autolinkEmail'
  const autolinkMarker: 'autolinkMarker'
  const autolinkProtocol: 'autolinkProtocol'
  const characterEscape: 'characterEscape'
  const characterEscapeValue: 'characterEscapeValue'
  const characterReference: 'characterReference'
  const characterReferenceMarker: 'characterReferenceMarker'
  const characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric'
  const characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal'
  const characterReferenceValue: 'characterReferenceValue'
  const codeFenced: 'codeFenced'
  const codeFencedFence: 'codeFencedFence'
  const codeFencedFenceSequence: 'codeFencedFenceSequence'
  const codeFencedFenceInfo: 'codeFencedFenceInfo'
  const codeFencedFenceMeta: 'codeFencedFenceMeta'
  const codeFlowValue: 'codeFlowValue'
  const codeIndented: 'codeIndented'
  const codeText: 'codeText'
  const codeTextData: 'codeTextData'
  const codeTextPadding: 'codeTextPadding'
  const codeTextSequence: 'codeTextSequence'
  const content: 'content'
  const definition: 'definition'
  const definitionDestination: 'definitionDestination'
  const definitionDestinationLiteral: 'definitionDestinationLiteral'
  const definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker'
  const definitionDestinationRaw: 'definitionDestinationRaw'
  const definitionDestinationString: 'definitionDestinationString'
  const definitionLabel: 'definitionLabel'
  const definitionLabelMarker: 'definitionLabelMarker'
  const definitionLabelString: 'definitionLabelString'
  const definitionMarker: 'definitionMarker'
  const definitionTitle: 'definitionTitle'
  const definitionTitleMarker: 'definitionTitleMarker'
  const definitionTitleString: 'definitionTitleString'
  const emphasis: 'emphasis'
  const emphasisSequence: 'emphasisSequence'
  const emphasisText: 'emphasisText'
  const escapeMarker: 'escapeMarker'
  const hardBreakEscape: 'hardBreakEscape'
  const hardBreakTrailing: 'hardBreakTrailing'
  const htmlFlow: 'htmlFlow'
  const htmlFlowData: 'htmlFlowData'
  const htmlText: 'htmlText'
  const htmlTextData: 'htmlTextData'
  const image: 'image'
  const label: 'label'
  const labelText: 'labelText'
  const labelLink: 'labelLink'
  const labelImage: 'labelImage'
  const labelMarker: 'labelMarker'
  const labelImageMarker: 'labelImageMarker'
  const labelEnd: 'labelEnd'
  const link: 'link'
  const paragraph: 'paragraph'
  const reference: 'reference'
  const referenceMarker: 'referenceMarker'
  const referenceString: 'referenceString'
  const resource: 'resource'
  const resourceDestination: 'resourceDestination'
  const resourceDestinationLiteral: 'resourceDestinationLiteral'
  const resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker'
  const resourceDestinationRaw: 'resourceDestinationRaw'
  const resourceDestinationString: 'resourceDestinationString'
  const resourceMarker: 'resourceMarker'
  const resourceTitle: 'resourceTitle'
  const resourceTitleMarker: 'resourceTitleMarker'
  const resourceTitleString: 'resourceTitleString'
  const setextHeading: 'setextHeading'
  const setextHeadingText: 'setextHeadingText'
  const setextHeadingLine: 'setextHeadingLine'
  const setextHeadingLineSequence: 'setextHeadingLineSequence'
  const strong: 'strong'
  const strongSequence: 'strongSequence'
  const strongText: 'strongText'
  const thematicBreak: 'thematicBreak'
  const thematicBreakSequence: 'thematicBreakSequence'
  const blockQuote: 'blockQuote'
  const blockQuotePrefix: 'blockQuotePrefix'
  const blockQuoteMarker: 'blockQuoteMarker'
  const blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace'
  const listOrdered: 'listOrdered'
  const listUnordered: 'listUnordered'
  const listItemIndent: 'listItemIndent'
  const listItemMarker: 'listItemMarker'
  const listItemPrefix: 'listItemPrefix'
  const listItemPrefixWhitespace: 'listItemPrefixWhitespace'
  const listItemValue: 'listItemValue'
  const chunkDocument: 'chunkDocument'
  const chunkContent: 'chunkContent'
  const chunkFlow: 'chunkFlow'
  const chunkText: 'chunkText'
  const chunkString: 'chunkString'
}
