/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconMultiplier1x = createReactComponent("outline", "multiplier-1x", "IconMultiplier1x", [["path", { "d": "M9 16v-8l-2 2", "key": "svg-0" }], ["path", { "d": "M13 16l4 -4", "key": "svg-1" }], ["path", { "d": "M17 16l-4 -4", "key": "svg-2" }]]);

export { IconMultiplier1x as default };
//# sourceMappingURL=IconMultiplier1x.mjs.map
