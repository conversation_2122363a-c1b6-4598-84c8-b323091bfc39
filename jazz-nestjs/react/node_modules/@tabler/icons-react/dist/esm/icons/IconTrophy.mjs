/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconTrophy = createReactComponent("outline", "trophy", "IconTrophy", [["path", { "d": "M8 21l8 0", "key": "svg-0" }], ["path", { "d": "M12 17l0 4", "key": "svg-1" }], ["path", { "d": "M7 4l10 0", "key": "svg-2" }], ["path", { "d": "M17 4v8a5 5 0 0 1 -10 0v-8", "key": "svg-3" }], ["path", { "d": "M5 9m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-4" }], ["path", { "d": "M19 9m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-5" }]]);

export { IconTrophy as default };
//# sourceMappingURL=IconTrophy.mjs.map
