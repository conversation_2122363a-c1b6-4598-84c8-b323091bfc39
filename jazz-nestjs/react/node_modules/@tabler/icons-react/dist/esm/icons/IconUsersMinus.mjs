/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconUsersMinus = createReactComponent("outline", "users-minus", "IconUsersMinus", [["path", { "d": "M5 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0", "key": "svg-0" }], ["path", { "d": "M3 21v-2a4 4 0 0 1 4 -4h4c.948 0 1.818 .33 2.504 .88", "key": "svg-1" }], ["path", { "d": "M16 3.13a4 4 0 0 1 0 7.75", "key": "svg-2" }], ["path", { "d": "M16 19h6", "key": "svg-3" }]]);

export { IconUsersMinus as default };
//# sourceMappingURL=IconUsersMinus.mjs.map
