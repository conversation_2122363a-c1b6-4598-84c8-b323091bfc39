/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconSpaces = createReactComponent("outline", "spaces", "IconSpaces", [["path", { "d": "M6.045 9.777a6 6 0 1 0 5.951 .023", "key": "svg-0" }], ["path", { "d": "M11.997 20.196a6 6 0 1 0 -2.948 -5.97", "key": "svg-1" }], ["path", { "d": "M17.95 9.785q .05 -.386 .05 -.785a6 6 0 1 0 -3.056 5.23", "key": "svg-2" }]]);

export { IconSpaces as default };
//# sourceMappingURL=IconSpaces.mjs.map
