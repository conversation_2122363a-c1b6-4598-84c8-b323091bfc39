/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconPointerCode = createReactComponent("outline", "pointer-code", "IconPointerCode", [["path", { "d": "M15.76 13.203l-.982 -.981l3.113 -2.09a1.2 1.2 0 0 0 -.309 -2.228l-13.582 -3.904l3.904 13.563a1.2 1.2 0 0 0 2.228 .308l2.09 -3.093l.67 .67", "key": "svg-0" }], ["path", { "d": "M20 21l2 -2l-2 -2", "key": "svg-1" }], ["path", { "d": "M17 17l-2 2l2 2", "key": "svg-2" }]]);

export { IconPointerCode as default };
//# sourceMappingURL=IconPointerCode.mjs.map
