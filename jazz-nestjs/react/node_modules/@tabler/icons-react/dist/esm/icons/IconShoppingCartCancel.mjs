/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconShoppingCartCancel = createReactComponent("outline", "shopping-cart-cancel", "IconShoppingCartCancel", [["path", { "d": "M4 19a2 2 0 1 0 4 0a2 2 0 0 0 -4 0", "key": "svg-0" }], ["path", { "d": "M12 17h-6v-14h-2", "key": "svg-1" }], ["path", { "d": "M6 5l14 1l-.857 5.998m-3.643 1.002h-9.5", "key": "svg-2" }], ["path", { "d": "M19 19m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-3" }], ["path", { "d": "M17 21l4 -4", "key": "svg-4" }]]);

export { IconShoppingCartCancel as default };
//# sourceMappingURL=IconShoppingCartCancel.mjs.map
