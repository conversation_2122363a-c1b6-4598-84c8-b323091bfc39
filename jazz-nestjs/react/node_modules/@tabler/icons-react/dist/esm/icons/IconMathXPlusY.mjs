/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconMathXPlusY = createReactComponent("outline", "math-x-plus-y", "IconMathXPlusY", [["path", { "d": "M16 9l3 5.063", "key": "svg-0" }], ["path", { "d": "M2 9l6 6", "key": "svg-1" }], ["path", { "d": "M2 15l6 -6", "key": "svg-2" }], ["path", { "d": "M22 9l-4.8 9", "key": "svg-3" }], ["path", { "d": "M10 12h4", "key": "svg-4" }], ["path", { "d": "M12 10v4", "key": "svg-5" }]]);

export { IconMathXPlusY as default };
//# sourceMappingURL=IconMathXPlusY.mjs.map
