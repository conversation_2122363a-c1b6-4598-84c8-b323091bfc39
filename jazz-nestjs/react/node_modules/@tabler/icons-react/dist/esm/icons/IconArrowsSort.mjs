/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconArrowsSort = createReactComponent("outline", "arrows-sort", "IconArrowsSort", [["path", { "d": "M3 9l4 -4l4 4m-4 -4v14", "key": "svg-0" }], ["path", { "d": "M21 15l-4 4l-4 -4m4 4v-14", "key": "svg-1" }]]);

export { IconArrowsSort as default };
//# sourceMappingURL=IconArrowsSort.mjs.map
