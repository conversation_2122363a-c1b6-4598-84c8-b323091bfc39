/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconAntennaBars4 = createReactComponent("outline", "antenna-bars-4", "IconAntennaBars4", [["path", { "d": "M6 18l0 -3", "key": "svg-0" }], ["path", { "d": "M10 18l0 -6", "key": "svg-1" }], ["path", { "d": "M14 18l0 -9", "key": "svg-2" }], ["path", { "d": "M18 18l0 .01", "key": "svg-3" }]]);

export { IconAntennaBars4 as default };
//# sourceMappingURL=IconAntennaBars4.mjs.map
