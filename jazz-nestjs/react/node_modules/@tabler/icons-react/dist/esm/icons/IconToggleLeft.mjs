/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconToggleLeft = createReactComponent("outline", "toggle-left", "IconToggleLeft", [["path", { "d": "M8 12m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M2 6m0 6a6 6 0 0 1 6 -6h8a6 6 0 0 1 6 6v0a6 6 0 0 1 -6 6h-8a6 6 0 0 1 -6 -6z", "key": "svg-1" }]]);

export { IconToggleLeft as default };
//# sourceMappingURL=IconToggleLeft.mjs.map
