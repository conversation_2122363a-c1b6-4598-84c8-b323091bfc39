/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconUmbrellaOff = createReactComponent("outline", "umbrella-off", "IconUmbrellaOff", [["path", { "d": "M12 12h-8c0 -2.209 .895 -4.208 2.342 -5.656m2.382 -1.645a8 8 0 0 1 11.276 7.301l-4 0", "key": "svg-0" }], ["path", { "d": "M12 12v6a2 2 0 1 0 4 0", "key": "svg-1" }], ["path", { "d": "M3 3l18 18", "key": "svg-2" }]]);

export { IconUmbrellaOff as default };
//# sourceMappingURL=IconUmbrellaOff.mjs.map
