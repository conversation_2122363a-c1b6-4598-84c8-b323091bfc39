/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconShieldShare = createReactComponent("outline", "shield-share", "IconShieldShare", [["path", { "d": "M12 21a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3a12 12 0 0 0 8.5 3a12 12 0 0 1 .193 6.025", "key": "svg-0" }], ["path", { "d": "M16 22l5 -5", "key": "svg-1" }], ["path", { "d": "M21 21.5v-4.5h-4.5", "key": "svg-2" }]]);

export { IconShieldShare as default };
//# sourceMappingURL=IconShieldShare.mjs.map
