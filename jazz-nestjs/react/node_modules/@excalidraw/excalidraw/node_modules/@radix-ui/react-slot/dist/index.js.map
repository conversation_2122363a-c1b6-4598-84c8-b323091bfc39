{"version": 3, "sources": ["../src/index.ts", "../src/slot.tsx"], "sourcesContent": ["export {\n  Slot,\n  Slottable,\n  //\n  Root,\n} from './slot';\nexport type { SlotProps } from './slot';\n", "import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\nconst Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n\n  if (slottable) {\n    // the new element to render is the one passed as a child of `Slottable`\n    const newElement = slottable.props.children;\n\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        // because the new element will be the one rendered, we are only interested\n        // in grabbing its children (`newElement.props.children`)\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement)\n          ? (newElement.props as { children: React.ReactNode }).children\n          : null;\n      } else {\n        return child;\n      }\n    });\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {React.isValidElement(newElement)\n          ? React.cloneElement(newElement, undefined, newChildren)\n          : null}\n      </SlotClone>\n    );\n  }\n\n  return (\n    <SlotClone {...slotProps} ref={forwardedRef}>\n      {children}\n    </SlotClone>\n  );\n});\n\nSlot.displayName = 'Slot';\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\nconst SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n\n  if (React.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    const props = mergeProps(slotProps, children.props as AnyProps);\n    // do not pass ref to React.Fragment for React 19 compatibility\n    if (children.type !== React.Fragment) {\n      props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n    }\n    return React.cloneElement(children, props);\n  }\n\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\n\nSlotClone.displayName = 'SlotClone';\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst Slottable = ({ children }: { children: React.ReactNode }) => {\n  return <>{children}</>;\n};\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<React.ComponentProps<typeof Slottable>, typeof Slottable> {\n  return React.isValidElement(child) && child.type === Slottable;\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nconst Root = Slot;\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Root,\n};\nexport type { SlotProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,gCAA4B;AAiCtB;AAvBN,IAAM,OAAa,iBAAmC,CAAC,OAAO,iBAAiB;AAC7E,QAAM,EAAE,UAAU,GAAG,UAAU,IAAI;AACnC,QAAM,gBAAsB,eAAS,QAAQ,QAAQ;AACrD,QAAM,YAAY,cAAc,KAAK,WAAW;AAEhD,MAAI,WAAW;AAEb,UAAM,aAAa,UAAU,MAAM;AAEnC,UAAM,cAAc,cAAc,IAAI,CAAC,UAAU;AAC/C,UAAI,UAAU,WAAW;AAGvB,YAAU,eAAS,MAAM,UAAU,IAAI,EAAG,QAAa,eAAS,KAAK,IAAI;AACzE,eAAa,qBAAe,UAAU,IACjC,WAAW,MAAwC,WACpD;AAAA,MACN,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAED,WACE,4CAAC,aAAW,GAAG,WAAW,KAAK,cAC5B,UAAM,qBAAe,UAAU,IACtB,mBAAa,YAAY,QAAW,WAAW,IACrD,MACN;AAAA,EAEJ;AAEA,SACE,4CAAC,aAAW,GAAG,WAAW,KAAK,cAC5B,UACH;AAEJ,CAAC;AAED,KAAK,cAAc;AAUnB,IAAM,YAAkB,iBAAgC,CAAC,OAAO,iBAAiB;AAC/E,QAAM,EAAE,UAAU,GAAG,UAAU,IAAI;AAEnC,MAAU,qBAAe,QAAQ,GAAG;AAClC,UAAM,cAAc,cAAc,QAAQ;AAC1C,UAAMA,SAAQ,WAAW,WAAW,SAAS,KAAiB;AAE9D,QAAI,SAAS,SAAe,gBAAU;AACpC,MAAAA,OAAM,MAAM,mBAAe,uCAAY,cAAc,WAAW,IAAI;AAAA,IACtE;AACA,WAAa,mBAAa,UAAUA,MAAK;AAAA,EAC3C;AAEA,SAAa,eAAS,MAAM,QAAQ,IAAI,IAAU,eAAS,KAAK,IAAI,IAAI;AAC1E,CAAC;AAED,UAAU,cAAc;AAMxB,IAAM,YAAY,CAAC,EAAE,SAAS,MAAqC;AACjE,SAAO,2EAAG,UAAS;AACrB;AAMA,SAAS,YACP,OACuF;AACvF,SAAa,qBAAe,KAAK,KAAK,MAAM,SAAS;AACvD;AAEA,SAAS,WAAW,WAAqB,YAAsB;AAE7D,QAAM,gBAAgB,EAAE,GAAG,WAAW;AAEtC,aAAW,YAAY,YAAY;AACjC,UAAM,gBAAgB,UAAU,QAAQ;AACxC,UAAM,iBAAiB,WAAW,QAAQ;AAE1C,UAAM,YAAY,WAAW,KAAK,QAAQ;AAC1C,QAAI,WAAW;AAEb,UAAI,iBAAiB,gBAAgB;AACnC,sBAAc,QAAQ,IAAI,IAAI,SAAoB;AAChD,yBAAe,GAAG,IAAI;AACtB,wBAAc,GAAG,IAAI;AAAA,QACvB;AAAA,MACF,WAES,eAAe;AACtB,sBAAc,QAAQ,IAAI;AAAA,MAC5B;AAAA,IACF,WAES,aAAa,SAAS;AAC7B,oBAAc,QAAQ,IAAI,EAAE,GAAG,eAAe,GAAG,eAAe;AAAA,IAClE,WAAW,aAAa,aAAa;AACnC,oBAAc,QAAQ,IAAI,CAAC,eAAe,cAAc,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAAA,IACpF;AAAA,EACF;AAEA,SAAO,EAAE,GAAG,WAAW,GAAG,cAAc;AAC1C;AAOA,SAAS,cAAc,SAA6B;AAElD,MAAI,SAAS,OAAO,yBAAyB,QAAQ,OAAO,KAAK,GAAG;AACpE,MAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,MAAI,SAAS;AACX,WAAQ,QAAgB;AAAA,EAC1B;AAGA,WAAS,OAAO,yBAAyB,SAAS,KAAK,GAAG;AAC1D,YAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,MAAI,SAAS;AACX,WAAQ,QAAQ,MAAuC;AAAA,EACzD;AAGA,SAAQ,QAAQ,MAAuC,OAAQ,QAAgB;AACjF;AAEA,IAAM,OAAO;", "names": ["props"]}