{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;A;;;;;;;;;;;ACcA;;oGAEA,CAEA,MAAMmB,+BAAS,GAAG,MAAlB,AAAA;AAGA,MAAM,CAACC,uCAAD,EAAoBpB,yCAApB,CAAA,GAAuCW,6CAAkB,CAACQ,+BAAD,EAAY;IACzEP,0DADyE;CAAZ,CAA/D,AAAA;AAGA,MAAMS,8CAAwB,GAAGT,0DAA2B,EAA5D,AAAA;AAWA,MAAM,CAACU,kCAAD,EAAeC,oCAAf,CAAA,GAAiCH,uCAAiB,CAAmBD,+BAAnB,CAAxD,AAAA;AA6BA,MAAMlB,yCAAI,GAAA,aAAGQ,CAAAA,uBAAA,CACX,CAACgB,KAAD,EAAgCC,YAAhC,GAAiD;IAC/C,MAAM,E,aACJC,WADI,CAAA,EAEJC,KAAK,EAAEC,SAFH,CAAA,E,eAGJC,aAHI,CAAA,E,cAIJC,YAJI,CAAA,eAKJC,WAAW,GAAG,YALV,G,KAMJC,GANI,CAAA,kBAOJC,cAAc,GAAG,WAPb,GAQJ,GAAGC,SAAH,EARI,GASFV,KATJ,AAAM;IAUN,MAAMW,SAAS,GAAGpB,yCAAY,CAACiB,GAAD,CAA9B,AAAA;IACA,MAAM,CAACL,KAAD,EAAQS,QAAR,CAAA,GAAoBpB,4DAAoB,CAAC;QAC7CqB,IAAI,EAAET,SADuC;QAE7CU,QAAQ,EAAET,aAFmC;QAG7CU,WAAW,EAAET,YAAbS;KAH4C,CAA9C,AAA+C;IAM/C,OAAA,aACE,CAAA,0BAAA,CAAC,kCAAD,EADF;QAEI,KAAK,EAAEb,WADT;QAEE,MAAM,EAAET,2BAAK,EAFf;QAGE,KAAK,EAAEU,KAHT;QAIE,aAAa,EAAES,QAJjB;QAKE,WAAW,EAAEL,WALf;QAME,GAAG,EAAEI,SANP;QAOE,cAAc,EAAEF,cAAhB;KAPF,EAAA,aASE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EATF,2DAAA,CAAA;QAUI,GAAG,EAAEE,SADP;QAEE,kBAAA,EAAkBJ,WAAlB;KAFF,EAGMG,SAHN,EAAA;QAIE,GAAG,EAAET,YAAL;KAJF,CAAA,CATF,CADF,CAUI;CA7BK,CAAb,AAqCG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,+BAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMe,mCAAa,GAAG,UAAtB,AAAA;AAOA,MAAMvC,yCAAQ,GAAA,aAAGO,CAAAA,uBAAA,CACf,CAACgB,KAAD,EAAoCC,YAApC,GAAqD;IACnD,MAAM,E,aAAEC,WAAF,CAAA,QAAee,IAAI,GAAG,IAAtB,GAA4B,GAAGC,SAAH,EAA5B,GAA6ClB,KAAnD,AAAM;IACN,MAAMmB,OAAO,GAAGrB,oCAAc,CAACkB,mCAAD,EAAgBd,WAAhB,CAA9B,AAAA;IACA,MAAMkB,qBAAqB,GAAGxB,8CAAwB,CAACM,WAAD,CAAtD,AAAA;IACA,OAAA,aACE,CAAA,0BAAA,CAAC,mCAAD,EADF,2DAAA,CAAA;QAEI,OAAO,EAAP,IAAA;KADF,EAEMkB,qBAFN,EAAA;QAGE,WAAW,EAAED,OAAO,CAACZ,WAHvB;QAIE,GAAG,EAAEY,OAAO,CAACX,GAJf;QAKE,IAAI,EAAES,IAAN;KALF,CAAA,EAAA,aAOE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EAPF,2DAAA,CAAA;QAQI,IAAI,EAAC,SADP;QAEE,kBAAA,EAAkBE,OAAO,CAACZ,WAA1B;KAFF,EAGMW,SAHN,EAAA;QAIE,GAAG,EAAEjB,YAAL;KAJF,CAAA,CAPF,CADF,CAQI;CAbS,CAAjB,AAqBG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,mCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMoB,kCAAY,GAAG,aAArB,AAAA;AAQA,MAAM3C,yCAAW,GAAA,aAAGM,CAAAA,uBAAA,CAClB,CAACgB,KAAD,EAAuCC,YAAvC,GAAwD;IACtD,MAAM,E,aAAEC,WAAF,CAAA,E,OAAeC,KAAf,CAAA,YAAsBmB,QAAQ,GAAG,KAAjC,GAAwC,GAAGC,YAAH,EAAxC,GAA4DvB,KAAlE,AAAM;IACN,MAAMmB,OAAO,GAAGrB,oCAAc,CAACuB,kCAAD,EAAenB,WAAf,CAA9B,AAAA;IACA,MAAMkB,qBAAqB,GAAGxB,8CAAwB,CAACM,WAAD,CAAtD,AAAA;IACA,MAAMsB,SAAS,GAAGC,mCAAa,CAACN,OAAO,CAACO,MAAT,EAAiBvB,KAAjB,CAA/B,AAAA;IACA,MAAMwB,SAAS,GAAGC,mCAAa,CAACT,OAAO,CAACO,MAAT,EAAiBvB,KAAjB,CAA/B,AAAA;IACA,MAAM0B,UAAU,GAAG1B,KAAK,KAAKgB,OAAO,CAAChB,KAArC,AAAA;IACA,OAAA,aACE,CAAA,0BAAA,CAAC,mCAAD,EADF,2DAAA,CAAA;QAEI,OAAO,EAAP,IAAA;KADF,EAEMiB,qBAFN,EAAA;QAGE,SAAS,EAAE,CAACE,QAHd;QAIE,MAAM,EAAEO,UAAR;KAJF,CAAA,EAAA,aAME,CAAA,0BAAA,CAAC,sCAAD,CAAW,MAAX,EANF,2DAAA,CAAA;QAOI,IAAI,EAAC,QADP;QAEE,IAAI,EAAC,KAFP;QAGE,eAAA,EAAeA,UAHjB;QAIE,eAAA,EAAeF,SAJjB;QAKE,YAAA,EAAYE,UAAU,GAAG,QAAH,GAAc,UALtC;QAME,eAAA,EAAeP,QAAQ,GAAG,EAAH,GAAQQ,SANjC;QAOE,QAAQ,EAAER,QAPZ;QAQE,EAAE,EAAEE,SAAJ;KARF,EASMD,YATN,EAAA;QAUE,GAAG,EAAEtB,YAVP;QAWE,WAAW,EAAEhB,4CAAoB,CAACe,KAAK,CAAC+B,WAAP,EAAqBC,CAAAA,KAAD,GAAW;YAC9D,4FAAA;YACA,uEAAA;YACA,IAAI,CAACV,QAAD,IAAaU,KAAK,CAACC,MAAN,KAAiB,CAA9B,IAAmCD,KAAK,CAACE,OAAN,KAAkB,KAAzD,EACEf,OAAO,CAACd,aAAR,CAAsBF,KAAtB,CAAAgB,CAAAA;iBAEA,+CAAA;YACAa,KAAK,CAACG,cAAN,EAAAH,CAAAA;SAP6B,CAXnC;QAqBE,SAAS,EAAE/C,4CAAoB,CAACe,KAAK,CAACoC,SAAP,EAAmBJ,CAAAA,KAAD,GAAW;YAC1D,IAAI;gBAAC,GAAD;gBAAM,OAAN;aAAA,CAAeK,QAAf,CAAwBL,KAAK,CAACM,GAA9B,CAAJ,EAAwCnB,OAAO,CAACd,aAAR,CAAsBF,KAAtB,CAAxC,CAAA;SAD6B,CArBjC;QAwBE,OAAO,EAAElB,4CAAoB,CAACe,KAAK,CAACuC,OAAP,EAAgB,IAAM;YACjD,6CAAA;YACA,mCAAA;YACA,MAAMC,qBAAqB,GAAGrB,OAAO,CAACV,cAAR,KAA2B,QAAzD,AAAA;YACA,IAAI,CAACoB,UAAD,IAAe,CAACP,QAAhB,IAA4BkB,qBAAhC,EACErB,OAAO,CAACd,aAAR,CAAsBF,KAAtB,CAAAgB,CAAAA;SALyB,CAO5B;KA/BH,CAAA,CANF,CADF,CAOI;CAfY,CAApB,AAkDG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMsB,kCAAY,GAAG,aAArB,AAAA;AAaA,MAAM9D,yCAAW,GAAA,aAAGK,CAAAA,uBAAA,CAClB,CAACgB,KAAD,EAAuCC,YAAvC,GAAwD;IACtD,MAAM,E,aAAEC,WAAF,CAAA,E,OAAeC,KAAf,CAAA,E,YAAsBuC,UAAtB,CAAA,E,UAAkCC,QAAlC,CAAA,EAA4C,GAAGC,YAAH,EAA5C,GAAgE5C,KAAtE,AAAM;IACN,MAAMmB,OAAO,GAAGrB,oCAAc,CAAC2C,kCAAD,EAAevC,WAAf,CAA9B,AAAA;IACA,MAAMsB,SAAS,GAAGC,mCAAa,CAACN,OAAO,CAACO,MAAT,EAAiBvB,KAAjB,CAA/B,AAAA;IACA,MAAMwB,SAAS,GAAGC,mCAAa,CAACT,OAAO,CAACO,MAAT,EAAiBvB,KAAjB,CAA/B,AAAA;IACA,MAAM0B,UAAU,GAAG1B,KAAK,KAAKgB,OAAO,CAAChB,KAArC,AAAA;IACA,MAAM0C,4BAA4B,GAAG7D,mBAAA,CAAa6C,UAAb,CAArC,AAAA;IAEA7C,sBAAA,CAAgB,IAAM;QACpB,MAAMgE,GAAG,GAAGC,qBAAqB,CAAC,IAAOJ,4BAA4B,CAACK,OAA7B,GAAuC,KAA/C;QAAA,CAAjC,AAAA;QACA,OAAO,IAAMC,oBAAoB,CAACH,GAAD,CAAjC;QAAA,CAAA;KAFF,EAGG,EAHH,CAGC,CAAA;IAED,OAAA,aACE,CAAA,0BAAA,CAAC,oCAAD,EADF;QACY,OAAO,EAAEN,UAAU,IAAIb,UAAvB;KAAV,EACG,CAAC,E,SAAEuB,OAAAA,CAAAA,EAAH,GAAA,aACC,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EADA,2DAAA,CAAA;YAEE,YAAA,EAAYvB,UAAU,GAAG,QAAH,GAAc,UADtC;YAEE,kBAAA,EAAkBV,OAAO,CAACZ,WAF5B;YAGE,IAAI,EAAC,UAHP;YAIE,iBAAA,EAAiBiB,SAJnB;YAKE,MAAM,EAAE,CAAC4B,OALX;YAME,EAAE,EAAEzB,SANN;YAOE,QAAQ,EAAE,CAAV;SAPF,EAQMiB,YARN,EAAA;YASE,GAAG,EAAE3C,YATP;YAUE,KAAK,EAAE;gBACL,GAAGD,KAAK,CAACqD,KADJ;gBAELC,iBAAiB,EAAET,4BAA4B,CAACK,OAA7B,GAAuC,IAAvC,GAA8CpB,SAAjEwB;aAFK;SAVT,CAAA,EAeGF,OAAO,IAAIT,QAfd,CAFJ;IAAA,CADF,CAGM;CAjBU,CAApB,AAqCG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,SAASlB,mCAAT,CAAuBC,MAAvB,EAAuCvB,KAAvC,EAAsD;IACpD,OAAQ,CAAA,EAAEuB,MAAO,CAAA,SAAA,EAAWvB,KAAM,CAAA,CAAlC,CAAA;CACD;AAED,SAASyB,mCAAT,CAAuBF,MAAvB,EAAuCvB,KAAvC,EAAsD;IACpD,OAAQ,CAAA,EAAEuB,MAAO,CAAA,SAAA,EAAWvB,KAAM,CAAA,CAAlC,CAAA;CACD;AAED,MAAMvB,yCAAI,GAAGJ,yCAAb,AAAA;AACA,MAAMK,yCAAI,GAAGJ,yCAAb,AAAA;AACA,MAAMK,yCAAO,GAAGJ,yCAAhB,AAAA;AACA,MAAMK,yCAAO,GAAGJ,yCAAhB,AAAA;;AD1RA", "sources": ["packages/react/tabs/src/index.ts", "packages/react/tabs/src/Tabs.tsx"], "sourcesContent": ["export {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n} from './Tabs';\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps } from './Tabs';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value?: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ElementRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = Radix.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ElementRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ElementRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n"], "names": ["createTabsScope", "Tabs", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Root", "List", "<PERSON><PERSON>", "Content", "React", "composeEventHandlers", "createContextScope", "createRovingFocusGroupScope", "Presence", "Primitive", "RovingFocusGroup", "useDirection", "useControllableState", "useId", "TABS_NAME", "createTabsContext", "useRovingFocusGroupScope", "TabsProvider", "useTabsContext", "forwardRef", "props", "forwardedRef", "__scopeTabs", "value", "valueProp", "onValueChange", "defaultValue", "orientation", "dir", "activationMode", "tabsProps", "direction", "setValue", "prop", "onChange", "defaultProp", "TAB_LIST_NAME", "loop", "listProps", "context", "rovingFocusGroupScope", "TRIGGER_NAME", "disabled", "triggerProps", "triggerId", "makeTriggerId", "baseId", "contentId", "makeContentId", "isSelected", "undefined", "onMouseDown", "event", "button", "ctrl<PERSON>ey", "preventDefault", "onKeyDown", "includes", "key", "onFocus", "isAutomaticActivation", "CONTENT_NAME", "forceMount", "children", "contentProps", "isMountAnimationPreventedRef", "useRef", "useEffect", "rAF", "requestAnimationFrame", "current", "cancelAnimationFrame", "present", "style", "animationDuration"], "version": 3, "file": "index.js.map"}