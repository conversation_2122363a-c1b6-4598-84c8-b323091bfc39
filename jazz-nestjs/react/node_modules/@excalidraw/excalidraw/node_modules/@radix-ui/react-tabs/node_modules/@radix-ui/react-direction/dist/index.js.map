{"mappings": ";;;;;;;;;ACAA;AAGA,MAAMI,sCAAgB,GAAA,aAAGD,CAAAA,0BAAA,CAA2CG,SAA3C,CAAzB,AAAA;AAEA;;oGAEA,CAMA,MAAMJ,yCAAmD,GAAIK,CAAAA,KAAD,GAAW;IACrE,MAAM,EAdR,KAcUC,GAAF,CAAA,EAdR,UAceC,QAAAA,CAAAA,EAAP,GAAoBF,KAA1B,AAAM;IACN,OAAA,aAAO,CAAA,0BAAA,CAAC,sCAAD,CAAkB,QAAlB,EAAP;QAAkC,KAAK,EAAEC,GAAP;KAA3B,EAAwCC,QAAxC,CAAP,CAAO;CAFT,AAGC;AAED,oGAAA,CAEA,SAAST,yCAAT,CAAsBU,QAAtB,EAA4C;IAC1C,MAAMC,SAAS,GAAGR,uBAAA,CAAiBC,sCAAjB,CAAlB,AAAA;IACA,OAAOM,QAAQ,IAAIC,SAAZ,IAAyB,KAAhC,CAAA;CACD;AAED,MAAMV,yCAAQ,GAAGC,yCAAjB,AAAA;;ADzBA", "sources": ["packages/react/direction/src/index.ts", "packages/react/direction/src/Direction.tsx"], "sourcesContent": ["export {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n} from './Direction';\n", "import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n"], "names": ["useDirection", "Provider", "DirectionProvider", "React", "DirectionContext", "createContext", "undefined", "props", "dir", "children", "localDir", "globalDir", "useContext"], "version": 3, "file": "index.js.map"}