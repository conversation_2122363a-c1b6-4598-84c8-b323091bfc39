{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;A;;;;;;;;;;;ACcA,MAAMe,iCAAW,GAAG,+BAApB,AAAA;AACA,MAAMC,mCAAa,GAAG;IAAEC,OAAO,EAAE,KAAX;IAAkBC,UAAU,EAAE,IAAZA;CAAxC,AAAsB;AAEtB;;oGAEA,CAEA,MAAMC,gCAAU,GAAG,kBAAnB,AAAA;AAGA,MAAM,CAACC,gCAAD,EAAaC,mCAAb,EAA4BC,2CAA5B,CAAA,GAAqDf,8CAAgB,CAGzEY,gCAHyE,CAA3E,AAAA;AAMA,MAAM,CAACI,mDAAD,EAAgCvB,wCAAhC,CAAA,GAA+DS,6CAAkB,CACrFU,gCADqF,EAErF;IAACG,2CAAD;CAFqF,CAAvF,AAAA;AAiCA,MAAM,CAACE,yCAAD,EAAsBC,2CAAtB,CAAA,GACJF,mDAA6B,CAAqBJ,gCAArB,CAD/B,AAAA;AAMA,MAAMlB,yCAAgB,GAAA,aAAGI,CAAAA,uBAAA,CACvB,CAACsB,KAAD,EAA4CC,YAA5C,GAA6D;IAC3D,OAAA,aACE,CAAA,0BAAA,CAAC,gCAAD,CAAY,QAAZ,EADF;QACuB,KAAK,EAAED,KAAK,CAACE,uBAAb;KAArB,EAAA,aACE,CAAA,0BAAA,CAAC,gCAAD,CAAY,IAAZ,EADF;QACmB,KAAK,EAAEF,KAAK,CAACE,uBAAb;KAAjB,EAAA,aACE,CAAA,0BAAA,CAAC,0CAAD,EAAA,2DAAA,CAAA,EAAA,EAA0BF,KAA1B,EADF;QACmC,GAAG,EAAEC,YAAL;KAAjC,CAAA,CADF,CADF,CADF,CAGM;CALe,CAAzB,AASG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,gCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAaA,MAAME,0CAAoB,GAAA,aAAGzB,CAAAA,uBAAA,CAG3B,CAACsB,KAAD,EAAgDC,YAAhD,GAAiE;IACjE,MAAM,E,yBACJC,uBADI,CAAA,E,aAEJE,WAFI,CAAA,QAGJC,IAAI,GAAG,KAHH,G,KAIJC,GAJI,CAAA,EAKJC,gBAAgB,EAAEC,oBALd,CAAA,E,yBAMJC,uBANI,CAAA,E,0BAOJC,wBAPI,CAAA,E,cAQJC,YARI,CAAA,EASJ,GAAGC,UAAH,EATI,GAUFZ,KAVJ,AAAM;IAWN,MAAMa,GAAG,GAAGnC,mBAAA,CAA0C,IAA1C,CAAZ,AAAA;IACA,MAAMqC,YAAY,GAAGlC,8CAAe,CAACoB,YAAD,EAAeY,GAAf,CAApC,AAAA;IACA,MAAMG,SAAS,GAAG7B,yCAAY,CAACmB,GAAD,CAA9B,AAAA;IACA,MAAM,CAACC,gBAAgB,GAAG,IAApB,EAA0BU,mBAA1B,CAAA,GAAiD/B,4DAAoB,CAAC;QAC1EgC,IAAI,EAAEV,oBADoE;QAE1EW,WAAW,EAAEV,uBAF6D;QAG1EW,QAAQ,EAAEV,wBAAVU;KAHyE,CAA3E,AAA4E;IAK5E,MAAM,CAACC,gBAAD,EAAmBC,mBAAnB,CAAA,GAA0C5C,qBAAA,CAAe,KAAf,CAAhD,AAAA;IACA,MAAM8C,gBAAgB,GAAGvC,gDAAc,CAAC0B,YAAD,CAAvC,AAAA;IACA,MAAMc,QAAQ,GAAG/B,mCAAa,CAACQ,uBAAD,CAA9B,AAAA;IACA,MAAMwB,eAAe,GAAGhD,mBAAA,CAAa,KAAb,CAAxB,AAAA;IACA,MAAM,CAACiD,mBAAD,EAAsBC,sBAAtB,CAAA,GAAgDlD,qBAAA,CAAe,CAAf,CAAtD,AAAA;IAEAA,sBAAA,CAAgB,IAAM;QACpB,MAAMoD,IAAI,GAAGjB,GAAG,CAACkB,OAAjB,AAAA;QACA,IAAID,IAAJ,EAAU;YACRA,IAAI,CAACE,gBAAL,CAAsB5C,iCAAtB,EAAmCoC,gBAAnC,CAAAM,CAAAA;YACA,OAAO,IAAMA,IAAI,CAACG,mBAAL,CAAyB7C,iCAAzB,EAAsCoC,gBAAtC,CAAb;YAAA,CAAA;SACD;KALH,EAMG;QAACA,gBAAD;KANH,CAMC,CAAA;IAED,OAAA,aACE,CAAA,0BAAA,CAAC,yCAAD,EADF;QAEI,KAAK,EAAEtB,uBADT;QAEE,WAAW,EAAEE,WAFf;QAGE,GAAG,EAAEY,SAHP;QAIE,IAAI,EAAEX,IAJR;QAKE,gBAAgB,EAAEE,gBALpB;QAME,WAAW,EAAE7B,wBAAA,CACVyD,CAAAA,SAAD,GAAelB,mBAAmB,CAACkB,SAAD,CADvB;QAAA,EAEX;YAAClB,mBAAD;SAFW,CANf;QAUE,cAAc,EAAEvC,wBAAA,CAAkB,IAAM4C,mBAAmB,CAAC,IAAD,CAA3C;QAAA,EAAmD,EAAnD,CAVlB;QAWE,kBAAkB,EAAE5C,wBAAA,CAClB,IAAMkD,sBAAsB,CAAEQ,CAAAA,SAAD,GAAeA,SAAS,GAAG,CAA5B;YAAA,CADV;QAAA,EAElB,EAFkB,CAXtB;QAeE,qBAAqB,EAAE1D,wBAAA,CACrB,IAAMkD,sBAAsB,CAAEQ,CAAAA,SAAD,GAAeA,SAAS,GAAG,CAA5B;YAAA,CADP;QAAA,EAErB,EAFqB,CAAvB;KAfF,EAAA,aAoBE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EApBF,2DAAA,CAAA;QAqBI,QAAQ,EAAEf,gBAAgB,IAAIM,mBAAmB,KAAK,CAA5C,GAAgD,EAAhD,GAAqD,CADjE;QAEE,kBAAA,EAAkBvB,WAAlB;KAFF,EAGMQ,UAHN,EAAA;QAIE,GAAG,EAAEG,YAJP;QAKE,KAAK,EAAE;YAAEsB,OAAO,EAAE,MAAX;YAAmB,GAAGrC,KAAK,CAACsC,KAAT;SAL5B;QAME,WAAW,EAAE3D,4CAAoB,CAACqB,KAAK,CAACuC,WAAP,EAAoB,IAAM;YACzDb,eAAe,CAACK,OAAhB,GAA0B,IAA1B,CAAAL;SAD+B,CANnC;QASE,OAAO,EAAE/C,4CAAoB,CAACqB,KAAK,CAACwC,OAAP,EAAiBC,CAAAA,KAAD,GAAW;YACtD,iEAAA;YACA,kEAAA;YACA,oEAAA;YACA,0EAAA;YACA,MAAMC,eAAe,GAAG,CAAChB,eAAe,CAACK,OAAzC,AAAA;YAEA,IAAIU,KAAK,CAACE,MAAN,KAAiBF,KAAK,CAACG,aAAvB,IAAwCF,eAAxC,IAA2D,CAACrB,gBAAhE,EAAkF;gBAChF,MAAMwB,eAAe,GAAG,IAAIC,WAAJ,CAAgB1D,iCAAhB,EAA6BC,mCAA7B,CAAxB,AAAA;gBACAoD,KAAK,CAACG,aAAN,CAAoBG,aAApB,CAAkCF,eAAlC,CAAAJ,CAAAA;gBAEA,IAAI,CAACI,eAAe,CAACG,gBAArB,EAAuC;oBACrC,MAAMC,KAAK,GAAGxB,QAAQ,EAAA,CAAGyB,MAAX,CAAmBC,CAAAA,IAAD,GAAUA,IAAI,CAACC,SAAjC;oBAAA,CAAd,AAAA;oBACA,MAAMC,UAAU,GAAGJ,KAAK,CAACK,IAAN,CAAYH,CAAAA,IAAD,GAAUA,IAAI,CAACI,MAA1B;oBAAA,CAAnB,AAAA;oBACA,MAAMC,WAAW,GAAGP,KAAK,CAACK,IAAN,CAAYH,CAAAA,IAAD,GAAUA,IAAI,CAACM,EAAL,KAAYlD,gBAAjC;oBAAA,CAApB,AAAA;oBACA,MAAMmD,cAAc,GAAG;wBAACL,UAAD;wBAAaG,WAAb;2BAA6BP,KAA7B;qBAAA,CAAoCC,MAApC,CACrBS,OADqB,CAAvB,AAAA;oBAGA,MAAMC,cAAc,GAAGF,cAAc,CAACG,GAAf,CAAoBV,CAAAA,IAAD,GAAUA,IAAI,CAACtC,GAAL,CAASkB,OAAtC;oBAAA,CAAvB,AAAA;oBACA+B,gCAAU,CAACF,cAAD,CAAV,CAAAE;iBACD;aACF;YAEDpC,eAAe,CAACK,OAAhB,GAA0B,KAA1B,CAAAL;SAvB2B,CAT/B;QAkCE,MAAM,EAAE/C,4CAAoB,CAACqB,KAAK,CAAC+D,MAAP,EAAe,IAAMzC,mBAAmB,CAAC,KAAD,CAAxC;QAAA,CAA5B;KAlCF,CAAA,CApBF,CADF,CAqBI;CA1DuB,CAA7B,AAgGC;AAED;;oGAEA,CAEA,MAAM0C,+BAAS,GAAG,sBAAlB,AAAA;AAUA,MAAMzF,wCAAoB,GAAA,aAAGG,CAAAA,uBAAA,CAC3B,CAACsB,KAAD,EAA2CC,YAA3C,GAA4D;IAC1D,MAAM,E,yBACJC,uBADI,CAAA,aAEJkD,SAAS,GAAG,IAFR,WAGJG,MAAM,GAAG,KAHL,G,WAIJpB,SAJI,CAAA,EAKJ,GAAG8B,SAAH,EALI,GAMFjE,KANJ,AAAM;IAON,MAAMkE,MAAM,GAAGnF,2BAAK,EAApB,AAAA;IACA,MAAM0E,EAAE,GAAGtB,SAAS,IAAI+B,MAAxB,AAAA;IACA,MAAMC,OAAO,GAAGrE,2CAAqB,CAACkE,+BAAD,EAAY9D,uBAAZ,CAArC,AAAA;IACA,MAAMkE,gBAAgB,GAAGD,OAAO,CAAC5D,gBAAR,KAA6BkD,EAAtD,AAAA;IACA,MAAMhC,QAAQ,GAAG/B,mCAAa,CAACQ,uBAAD,CAA9B,AAAA;IAEA,MAAM,E,oBAAEmE,kBAAF,CAAA,E,uBAAsBC,qBAAAA,CAAAA,EAAtB,GAAgDH,OAAtD,AAAM;IAENzF,sBAAA,CAAgB,IAAM;QACpB,IAAI0E,SAAJ,EAAe;YACbiB,kBAAkB,EAAlBA,CAAAA;YACA,OAAO,IAAMC,qBAAqB,EAAlC;YAAA,CAAA;SACD;KAJH,EAKG;QAAClB,SAAD;QAAYiB,kBAAZ;QAAgCC,qBAAhC;KALH,CAKC,CAAA;IAED,OAAA,aACE,CAAA,0BAAA,CAAC,gCAAD,CAAY,QAAZ,EADF;QAEI,KAAK,EAAEpE,uBADT;QAEE,EAAE,EAAEuD,EAFN;QAGE,SAAS,EAAEL,SAHb;QAIE,MAAM,EAAEG,MAAR;KAJF,EAAA,aAME,CAAA,0BAAA,CAAC,sCAAD,CAAW,IAAX,EANF,2DAAA,CAAA;QAOI,QAAQ,EAAEa,gBAAgB,GAAG,CAAH,GAAO,EADnC;QAEE,kBAAA,EAAkBD,OAAO,CAAC/D,WAA1B;KAFF,EAGM6D,SAHN,EAAA;QAIE,GAAG,EAAEhE,YAJP;QAKE,WAAW,EAAEtB,4CAAoB,CAACqB,KAAK,CAACuC,WAAP,EAAqBE,CAAAA,KAAD,GAAW;YAC9D,0DAAA;YACA,wFAAA;YACA,IAAI,CAACW,SAAL,EAAgBX,KAAK,CAAC8B,cAAN,EAAA,CAAhB,CACA,mFADA;iBAEKJ,OAAO,CAACK,WAAR,CAAoBf,EAApB,CAFL,CAAA;SAH+B,CALnC;QAYE,OAAO,EAAE9E,4CAAoB,CAACqB,KAAK,CAACwC,OAAP,EAAgB,IAAM2B,OAAO,CAACK,WAAR,CAAoBf,EAApB,CAAtB;QAAA,CAZ/B;QAaE,SAAS,EAAE9E,4CAAoB,CAACqB,KAAK,CAACyE,SAAP,EAAmBhC,CAAAA,KAAD,GAAW;YAC1D,IAAIA,KAAK,CAACiC,GAAN,KAAc,KAAd,IAAuBjC,KAAK,CAACkC,QAAjC,EAA2C;gBACzCR,OAAO,CAACS,cAAR,EAAAT,CAAAA;gBACA,OAAA;aACD;YAED,IAAI1B,KAAK,CAACE,MAAN,KAAiBF,KAAK,CAACG,aAA3B,EAA0C,OAA1C;YAEA,MAAMiC,WAAW,GAAGC,oCAAc,CAACrC,KAAD,EAAQ0B,OAAO,CAAC/D,WAAhB,EAA6B+D,OAAO,CAAC7D,GAArC,CAAlC,AAAA;YAEA,IAAIuE,WAAW,KAAKE,SAApB,EAA+B;gBAC7BtC,KAAK,CAAC8B,cAAN,EAAA9B,CAAAA;gBACA,MAAMQ,KAAK,GAAGxB,QAAQ,EAAA,CAAGyB,MAAX,CAAmBC,CAAAA,IAAD,GAAUA,IAAI,CAACC,SAAjC;gBAAA,CAAd,AAAA;gBACA,IAAIQ,cAAc,GAAGX,KAAK,CAACY,GAAN,CAAWV,CAAAA,IAAD,GAAUA,IAAI,CAACtC,GAAL,CAASkB,OAA7B;gBAAA,CAArB,AAAA;gBAEA,IAAI8C,WAAW,KAAK,MAApB,EAA4BjB,cAAc,CAACoB,OAAf,EAAA,CAA5B;qBACK,IAAIH,WAAW,KAAK,MAAhB,IAA0BA,WAAW,KAAK,MAA9C,EAAsD;oBACzD,IAAIA,WAAW,KAAK,MAApB,EAA4BjB,cAAc,CAACoB,OAAf,EAA5B,CAAA;oBACA,MAAMC,YAAY,GAAGrB,cAAc,CAACsB,OAAf,CAAuBzC,KAAK,CAACG,aAA7B,CAArB,AAAA;oBACAgB,cAAc,GAAGO,OAAO,CAAC9D,IAAR,GACb8E,+BAAS,CAACvB,cAAD,EAAiBqB,YAAY,GAAG,CAAhC,CADI,GAEbrB,cAAc,CAACwB,KAAf,CAAqBH,YAAY,GAAG,CAApC,CAFJ,CAAArB;iBAGD;gBAED;;;WAGd,CACcyB,UAAU,CAAC,IAAMvB,gCAAU,CAACF,cAAD,CAAjB;gBAAA,CAAV,CAAAyB;aACD;SA7B4B,CA8B9B;KA3CH,CAAA,CANF,CADF,CAOI;CA/BqB,CAA7B,AA8EG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,wCAAA,EAAA;IAAA,WAAA,EAAA,+BAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,kBAAA;AACA,MAAMC,6CAAoD,GAAG;IAC3DC,SAAS,EAAE,MADgD;IACxCC,OAAO,EAAE,MAD+B;IAE3DC,UAAU,EAAE,MAF+C;IAEvCC,SAAS,EAAE,MAF4B;IAG3DC,MAAM,EAAE,OAHmD;IAG1CC,IAAI,EAAE,OAHoC;IAI3DC,QAAQ,EAAE,MAJiD;IAIzCC,GAAG,EAAE,MAALA;CAJpB,AAA6D;AAO7D,SAASC,0CAAT,CAA8BrB,GAA9B,EAA2CpE,GAA3C,EAA4D;IAC1D,IAAIA,GAAG,KAAK,KAAZ,EAAmB,OAAOoE,GAAP,CAAnB;IACA,OAAOA,GAAG,KAAK,WAAR,GAAsB,YAAtB,GAAqCA,GAAG,KAAK,YAAR,GAAuB,WAAvB,GAAqCA,GAAjF,CAAA;CACD;AAID,SAASI,oCAAT,CAAwBrC,KAAxB,EAAoDrC,WAApD,EAA+EE,GAA/E,EAAgG;IAC9F,MAAMoE,GAAG,GAAGqB,0CAAoB,CAACtD,KAAK,CAACiC,GAAP,EAAYpE,GAAZ,CAAhC,AAAA;IACA,IAAIF,WAAW,KAAK,UAAhB,IAA8B;QAAC,WAAD;QAAc,YAAd;KAAA,CAA4B4F,QAA5B,CAAqCtB,GAArC,CAAlC,EAA6E,OAAOK,SAAP,CAA7E;IACA,IAAI3E,WAAW,KAAK,YAAhB,IAAgC;QAAC,SAAD;QAAY,WAAZ;KAAA,CAAyB4F,QAAzB,CAAkCtB,GAAlC,CAApC,EAA4E,OAAOK,SAAP,CAA5E;IACA,OAAOO,6CAAuB,CAACZ,GAAD,CAA9B,CAAA;CACD;AAED,SAASZ,gCAAT,CAAoBmC,UAApB,EAA+C;IAC7C,MAAMC,0BAA0B,GAAGC,QAAQ,CAACC,aAA5C,AAAA;IACA,KAAK,MAAMC,SAAX,IAAwBJ,UAAxB,CAAoC;QAClC,8FAAA;QACA,IAAII,SAAS,KAAKH,0BAAlB,EAA8C,OAA9C;QACAG,SAAS,CAACC,KAAV,EAAAD,CAAAA;QACA,IAAIF,QAAQ,CAACC,aAAT,KAA2BF,0BAA/B,EAA2D,OAA3D;KACD;CACF;AAED;;;GAGA,CACA,SAASf,+BAAT,CAAsBoB,KAAtB,EAAkCC,UAAlC,EAAsD;IACpD,OAAOD,KAAK,CAAC1C,GAAN,CAAU,CAAC4C,CAAD,EAAIC,KAAJ,GAAcH,KAAK,CAAC,AAACC,CAAAA,UAAU,GAAGE,KAAd,CAAA,GAAuBH,KAAK,CAACI,MAA9B,CAA7B;IAAA,CAAP,CAAA;CACD;AAED,MAAMnI,yCAAI,GAAGF,yCAAb,AAAA;AACA,MAAMG,yCAAI,GAAGF,wCAAb,AAAA;;AD9UA", "sources": ["packages/react/roving-focus/src/index.ts", "packages/react/roving-focus/src/RovingFocusGroup.tsx"], "sourcesContent": ["export {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n} from './RovingFocusGroup';\nexport type { RovingFocusGroupProps, RovingFocusItemProps } from './RovingFocusGroup';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId = null, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId,\n    onChange: onCurrentTabStopIdChange,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends PrimitiveSpanProps {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        />\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n"], "names": ["createRovingFocusGroupScope", "RovingFocusGroup", "RovingFocusGroupItem", "Root", "<PERSON><PERSON>", "React", "composeEventHandlers", "createCollection", "useComposedRefs", "createContextScope", "useId", "Primitive", "useCallbackRef", "useControllableState", "useDirection", "ENTRY_FOCUS", "EVENT_OPTIONS", "bubbles", "cancelable", "GROUP_NAME", "Collection", "useCollection", "createCollectionScope", "createRovingFocusGroupContext", "RovingFocus<PERSON><PERSON>ider", "useRovingFocusContext", "forwardRef", "props", "forwardedRef", "__scopeRovingFocusGroup", "RovingFocusGroupImpl", "orientation", "loop", "dir", "currentTabStopId", "currentTabStopIdProp", "defaultCurrentTabStopId", "onCurrentTabStopIdChange", "onEntryFocus", "groupProps", "ref", "useRef", "composedRefs", "direction", "setCurrentTabStopId", "prop", "defaultProp", "onChange", "isTabbingBackOut", "setIsTabbingBackOut", "useState", "handleEntryFocus", "getItems", "isClickFocusRef", "focusableItemsCount", "setFocusableItemsCount", "useEffect", "node", "current", "addEventListener", "removeEventListener", "useCallback", "tabStopId", "prevCount", "outline", "style", "onMouseDown", "onFocus", "event", "isKeyboardFocus", "target", "currentTarget", "entryFocusEvent", "CustomEvent", "dispatchEvent", "defaultPrevented", "items", "filter", "item", "focusable", "activeItem", "find", "active", "currentItem", "id", "candidate<PERSON><PERSON>s", "Boolean", "candidateNodes", "map", "focusFirst", "onBlur", "ITEM_NAME", "itemProps", "autoId", "context", "isCurrentTabStop", "onFocusableItemAdd", "onFocusableItemRemove", "preventDefault", "onItemFocus", "onKeyDown", "key", "shift<PERSON>ey", "onItemShiftTab", "focusIntent", "getFocusIntent", "undefined", "reverse", "currentIndex", "indexOf", "wrapArray", "slice", "setTimeout", "MAP_KEY_TO_FOCUS_INTENT", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "PageUp", "Home", "PageDown", "End", "getDirectionAwareKey", "includes", "candidates", "PREVIOUSLY_FOCUSED_ELEMENT", "document", "activeElement", "candidate", "focus", "array", "startIndex", "_", "index", "length"], "version": 3, "file": "index.js.map"}