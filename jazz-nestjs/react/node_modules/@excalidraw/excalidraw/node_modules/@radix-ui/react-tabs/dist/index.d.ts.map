{"mappings": ";;;;AAqBA,OAAA,sFAEE,CAAC;AAeH,6BAA6B,MAAM,wBAAwB,CAAC,OAAO,iBAAiB,IAAI,CAAC,CAAC;AAC1F,yBAAyB,MAAM,wBAAwB,CAAC,OAAO,UAAU,GAAG,CAAC,CAAC;AAC9E,0BAAoB,SAAQ,iBAAiB;IAC3C,oDAAoD;IACpD,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,iEAAiE;IACjE,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,mDAAmD;IACnD,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IACxC;;;;OAIG;IACH,WAAW,CAAC,EAAE,qBAAqB,CAAC,aAAa,CAAC,CAAC;IACnD;;OAEG;IACH,GAAG,CAAC,EAAE,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACnC;;;SAGK;IACL,cAAc,CAAC,EAAE,WAAW,GAAG,QAAQ,CAAC;CACzC;AAED,OAAA,MAAM,sFAsCL,CAAC;AAWF,8BAAwB,SAAQ,iBAAiB;IAC/C,IAAI,CAAC,EAAE,qBAAqB,CAAC,MAAM,CAAC,CAAC;CACtC;AAED,OAAA,MAAM,8FAsBL,CAAC;AAWF,4BAA4B,MAAM,wBAAwB,CAAC,OAAO,UAAU,MAAM,CAAC,CAAC;AACpF,iCAA2B,SAAQ,oBAAoB;IACrD,KAAK,EAAE,MAAM,CAAC;CACf;AAED,OAAA,MAAM,uGAmDL,CAAC;AAWF,iCAA2B,SAAQ,iBAAiB;IAClD,KAAK,EAAE,MAAM,CAAC;IAEd;;;OAGG;IACH,UAAU,CAAC,EAAE,IAAI,CAAC;CACnB;AAED,OAAA,MAAM,oGAsCL,CAAC;AAcF,OAAA,MAAM,sFAAW,CAAC;AAClB,OAAA,MAAM,0FAAe,CAAC;AACtB,OAAA,MAAM,mGAAqB,CAAC;AAC5B,OAAA,MAAM,gGAAqB,CAAC", "sources": ["packages/react/tabs/src/packages/react/tabs/src/Tabs.tsx", "packages/react/tabs/src/packages/react/tabs/src/index.ts", "packages/react/tabs/src/index.ts"], "sourcesContent": [null, null, "export {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n} from './Tabs';\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps } from './Tabs';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}