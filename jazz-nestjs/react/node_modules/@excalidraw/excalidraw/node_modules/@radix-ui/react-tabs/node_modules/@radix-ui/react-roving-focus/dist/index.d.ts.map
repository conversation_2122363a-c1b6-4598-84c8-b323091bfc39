{"mappings": ";;;AA8BA,OAAA,kGAGC,CAAC;AAEF,mBAAmB,MAAM,cAAc,CAAC,kBAAkB,CAAC,CAAC;AAC5D,iBAAiB,KAAK,GAAG,KAAK,CAAC;AAE/B;IACE;;;OAGG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B;;OAEG;IACH,GAAG,CAAC,EAAE,SAAS,CAAC;IAChB;;;OAGG;IACH,IAAI,CAAC,EAAE,OAAO,CAAC;CAChB;AAcD,sCAAgC,SAAQ,yBAAyB;CAAG;AAEpE,OAAA,MAAM,8GAUL,CAAC;AAOF,yBAAyB,MAAM,wBAAwB,CAAC,OAAO,UAAU,GAAG,CAAC,CAAC;AAC9E,mCACE,SAAQ,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,EACpC,uBAAuB;IACzB,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACjC,uBAAuB,CAAC,EAAE,MAAM,CAAC;IACjC,wBAAwB,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC;IAC9D,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;CACvC;AA2GD,0BAA0B,MAAM,wBAAwB,CAAC,OAAO,UAAU,IAAI,CAAC,CAAC;AAChF,qCAA+B,SAAQ,kBAAkB;IACvD,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,OAAA,MAAM,kHA+EL,CAAC;AA8CF,OAAA,MAAM,kGAAuB,CAAC;AAC9B,OAAA,MAAM,kGAA2B,CAAC", "sources": ["packages/react/roving-focus/src/packages/react/roving-focus/src/RovingFocusGroup.tsx", "packages/react/roving-focus/src/packages/react/roving-focus/src/index.ts", "packages/react/roving-focus/src/index.ts"], "sourcesContent": [null, null, "export {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n} from './RovingFocusGroup';\nexport type { RovingFocusGroupProps, RovingFocusItemProps } from './RovingFocusGroup';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}