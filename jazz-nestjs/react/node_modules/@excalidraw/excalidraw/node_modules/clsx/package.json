{"name": "clsx", "version": "1.1.1", "repository": "lukeed/clsx", "description": "A tiny (228B) utility for constructing className strings conditionally.", "module": "dist/clsx.m.js", "unpkg": "dist/clsx.min.js", "main": "dist/clsx.js", "types": "clsx.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=6"}, "scripts": {"build": "bundt", "pretest": "npm run build", "test": "tape -r esm test/*.js | tap-spec"}, "files": ["*.d.ts", "dist"], "keywords": ["classes", "classname", "classnames"], "devDependencies": {"bundt": "1.0.1", "esm": "3.2.25", "tap-spec": "5.0.0", "tape": "4.9.1"}}