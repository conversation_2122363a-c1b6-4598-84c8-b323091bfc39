{"version": 3, "sources": ["../../../locales/ru-RU.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Вставить\",\n    \"pasteAsPlaintext\": \"Вставить как обычный текст\",\n    \"pasteCharts\": \"Вставить диаграммы\",\n    \"selectAll\": \"Выбрать всё\",\n    \"multiSelect\": \"Добавить элемент в выделенный фрагмент\",\n    \"moveCanvas\": \"Переместить холст\",\n    \"cut\": \"Вырезать\",\n    \"copy\": \"Копировать\",\n    \"copyAsPng\": \"Скопировать в буфер обмена как PNG\",\n    \"copyAsSvg\": \"Скопировать в буфер обмена как SVG\",\n    \"copyText\": \"Скопировать в буфер обмена как текст\",\n    \"copySource\": \"Копировать источник в буфер обмена\",\n    \"convertToCode\": \"Преобразовать в код\",\n    \"bringForward\": \"Переместить вперед\",\n    \"sendToBack\": \"На задний план\",\n    \"bringToFront\": \"На передний план\",\n    \"sendBackward\": \"Переместить назад\",\n    \"delete\": \"Удалить\",\n    \"copyStyles\": \"Скопировать стили\",\n    \"pasteStyles\": \"Вставить стили\",\n    \"stroke\": \"Обводка\",\n    \"background\": \"Фон\",\n    \"fill\": \"Заливка\",\n    \"strokeWidth\": \"Толщина штриха\",\n    \"strokeStyle\": \"Стиль обводки\",\n    \"strokeStyle_solid\": \"Сплошная\",\n    \"strokeStyle_dashed\": \"Пунктирная\",\n    \"strokeStyle_dotted\": \"Точечная\",\n    \"sloppiness\": \"Стиль обводки\",\n    \"opacity\": \"Непрозрачность\",\n    \"textAlign\": \"Выравнивание текста\",\n    \"edges\": \"Края\",\n    \"sharp\": \"Острые\",\n    \"round\": \"Скругленные\",\n    \"arrowheads\": \"Стрелка\",\n    \"arrowhead_none\": \"Нет\",\n    \"arrowhead_arrow\": \"Cтрелка\",\n    \"arrowhead_bar\": \"Черта\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Треугольник\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Размер шрифта\",\n    \"fontFamily\": \"Семейство шрифтов\",\n    \"addWatermark\": \"Добавить «Создано в Excalidraw»\",\n    \"handDrawn\": \"От руки\",\n    \"normal\": \"Обычный\",\n    \"code\": \"Код\",\n    \"small\": \"Малый\",\n    \"medium\": \"Средний\",\n    \"large\": \"Большой\",\n    \"veryLarge\": \"Очень большой\",\n    \"solid\": \"Однотонная\",\n    \"hachure\": \"Штрихованная\",\n    \"zigzag\": \"Зигзаг\",\n    \"crossHatch\": \"Перекрестная\",\n    \"thin\": \"Тонкая\",\n    \"bold\": \"Жирная\",\n    \"left\": \"Слева\",\n    \"center\": \"Центр\",\n    \"right\": \"Справа\",\n    \"extraBold\": \"Очень жирная\",\n    \"architect\": \"Архитектор\",\n    \"artist\": \"Художник\",\n    \"cartoonist\": \"Карикатурист\",\n    \"fileTitle\": \"Имя файла\",\n    \"colorPicker\": \"Выбор цвета\",\n    \"canvasColors\": \"Используется на холсте\",\n    \"canvasBackground\": \"Фон холста\",\n    \"drawingCanvas\": \"Полотно\",\n    \"layers\": \"Слои\",\n    \"actions\": \"Действия\",\n    \"language\": \"Язык\",\n    \"liveCollaboration\": \"Онлайн взаимодействие...\",\n    \"duplicateSelection\": \"Дубликат\",\n    \"untitled\": \"Безымянный\",\n    \"name\": \"Имя\",\n    \"yourName\": \"Ваше имя\",\n    \"madeWithExcalidraw\": \"Сделано в Excalidraw\",\n    \"group\": \"Сгруппировать выделение\",\n    \"ungroup\": \"Разделить выделение\",\n    \"collaborators\": \"Участники\",\n    \"showGrid\": \"Показать сетку\",\n    \"addToLibrary\": \"Добавить в библиотеку\",\n    \"removeFromLibrary\": \"Удалить из библиотеки\",\n    \"libraryLoadingMessage\": \"Загрузка библиотеки…\",\n    \"libraries\": \"Просмотреть библиотеки\",\n    \"loadingScene\": \"Загрузка сцены…\",\n    \"align\": \"Выровнять\",\n    \"alignTop\": \"Выровнять по верхнему краю\",\n    \"alignBottom\": \"Выровнять по нижнему краю\",\n    \"alignLeft\": \"Выровнять по левому краю\",\n    \"alignRight\": \"Выровнять по правому краю\",\n    \"centerVertically\": \"Центрировать по вертикали\",\n    \"centerHorizontally\": \"Центрировать по горизонтали\",\n    \"distributeHorizontally\": \"Распределить по горизонтали\",\n    \"distributeVertically\": \"Распределить по вертикали\",\n    \"flipHorizontal\": \"Переворот по горизонтали\",\n    \"flipVertical\": \"Переворот по вертикали\",\n    \"viewMode\": \"Вид\",\n    \"share\": \"Поделиться\",\n    \"showStroke\": \"Показать выбор цвета обводки\",\n    \"showBackground\": \"Показать выбор цвета фона\",\n    \"toggleTheme\": \"Переключить тему\",\n    \"personalLib\": \"Личная библиотека\",\n    \"excalidrawLib\": \"Библиотека Excalidraw\",\n    \"decreaseFontSize\": \"Уменьшить шрифт\",\n    \"increaseFontSize\": \"Увеличить шрифт\",\n    \"unbindText\": \"Отвязать текст\",\n    \"bindText\": \"Привязать текст к контейнеру\",\n    \"createContainerFromText\": \"Поместить текст в контейнер\",\n    \"link\": {\n      \"edit\": \"Редактировать ссылку\",\n      \"editEmbed\": \"\",\n      \"create\": \"Создать ссылку\",\n      \"createEmbed\": \"\",\n      \"label\": \"Ссылка\",\n      \"labelEmbed\": \"\",\n      \"empty\": \"\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Редактирование строки\",\n      \"exit\": \"Выход из редактора строки\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Блокировать\",\n      \"unlock\": \"Разблокировать\",\n      \"lockAll\": \"Заблокировать все\",\n      \"unlockAll\": \"Разблокировать все\"\n    },\n    \"statusPublished\": \"Опубликовано\",\n    \"sidebarLock\": \"Держать боковую панель открытой\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"Взять образец цвета с холста\",\n    \"textToDiagram\": \"Текст в диаграмму\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Пока ничего не добавлено...\",\n    \"hint_emptyLibrary\": \"Выберите объект на холсте, чтобы добавить его сюда, или установите библиотеку из публичного репозитория ниже.\",\n    \"hint_emptyPrivateLibrary\": \"Выберите объект на холсте, чтобы добавить его сюда.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Очистить холст и сбросить цвет фона\",\n    \"exportJSON\": \"Сохранить в\",\n    \"exportImage\": \"Экспортировать изображение...\",\n    \"export\": \"Сохранить как...\",\n    \"copyToClipboard\": \"Скопировать в буфер обмена\",\n    \"save\": \"Сохранить в текущий файл\",\n    \"saveAs\": \"Сохранить как\",\n    \"load\": \"Открыть\",\n    \"getShareableLink\": \"Получить доступ по ссылке\",\n    \"close\": \"Закрыть\",\n    \"selectLanguage\": \"Выбрать язык\",\n    \"scrollBackToContent\": \"Вернуться к содержимому\",\n    \"zoomIn\": \"Увеличить\",\n    \"zoomOut\": \"Уменьшить\",\n    \"resetZoom\": \"Сбросить масштаб\",\n    \"menu\": \"Меню\",\n    \"done\": \"Готово\",\n    \"edit\": \"Изменить\",\n    \"undo\": \"Шаг назад\",\n    \"redo\": \"Шаг вперед\",\n    \"resetLibrary\": \"Сброс библиотеки\",\n    \"createNewRoom\": \"Создать новую комнату\",\n    \"fullScreen\": \"Полный экран\",\n    \"darkMode\": \"Темная тема\",\n    \"lightMode\": \"Светлая тема\",\n    \"zenMode\": \"Режим Дзен\",\n    \"objectsSnapMode\": \"Привязка к объектам\",\n    \"exitZenMode\": \"Выключить режим концентрации внимания\",\n    \"cancel\": \"Отменить\",\n    \"clear\": \"Очистить\",\n    \"remove\": \"Удалить\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Опубликовать\",\n    \"submit\": \"Отправить\",\n    \"confirm\": \"Подтвердить\",\n    \"embeddableInteractionButton\": \"\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Это очистит весь холст. Вы уверены?\",\n    \"couldNotCreateShareableLink\": \"Не удалось создать общедоступную ссылку.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Нельзя создать ссылку, чтобы поделиться. Сцена слишком большая\",\n    \"couldNotLoadInvalidFile\": \"Не удалось загрузить недопустимый файл\",\n    \"importBackendFailed\": \"Не удалось импортировать из бэкэнда.\",\n    \"cannotExportEmptyCanvas\": \"Не может экспортировать пустой холст.\",\n    \"couldNotCopyToClipboard\": \"Не удалось скопировать в буфер обмена.\",\n    \"decryptFailed\": \"Не удалось расшифровать данные.\",\n    \"uploadedSecurly\": \"Загружаемые данные защищена сквозным шифрованием, что означает, что сервер Excalidraw и третьи стороны не могут прочитать содержимое.\",\n    \"loadSceneOverridePrompt\": \"Загрузка рисунка приведёт к замене имеющегося содержимого. Вы хотите продолжить?\",\n    \"collabStopOverridePrompt\": \"Остановка сессии перезапишет ваш предыдущий, локально сохранённый рисунок. Вы уверены? \\n\\n(Если вы хотите оставить ваш локальный рисунок, просто закройте вкладку браузера)\",\n    \"errorAddingToLibrary\": \"Не удалось добавить объект в библиотеку\",\n    \"errorRemovingFromLibrary\": \"Не удалось удалить объект из библиотеки\",\n    \"confirmAddLibrary\": \"Будет добавлено {{numShapes}} фигур в вашу библиотеку. Продолжить?\",\n    \"imageDoesNotContainScene\": \"Это изображение не содержит данных сцены. Вы включили встраивание сцены во время экспорта?\",\n    \"cannotRestoreFromImage\": \"Сцена не может быть восстановлена из этого изображения\",\n    \"invalidSceneUrl\": \"Невозможно импортировать сцену с предоставленного URL. Неверный формат, или не содержит верных Excalidraw JSON данных.\",\n    \"resetLibrary\": \"Это очистит вашу библиотеку. Вы уверены?\",\n    \"removeItemsFromsLibrary\": \"Удалить {{count}} объект(ов) из библиотеки?\",\n    \"invalidEncryptionKey\": \"Ключ шифрования должен состоять из 22 символов. Одновременное редактирование отключено.\",\n    \"collabOfflineWarning\": \"Отсутствует интернет-соединение.\\nВаши изменения не будут сохранены!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Неподдерживаемый тип файла.\",\n    \"imageInsertError\": \"Не удалось вставить изображение. Попробуйте позже...\",\n    \"fileTooBig\": \"Очень большой файл. Максимально разрешенный размер {{maxSize}}.\",\n    \"svgImageInsertError\": \"Не удалось вставить изображение SVG. Разметка SVG выглядит недействительной.\",\n    \"failedToFetchImage\": \"Не удалось получить изображение.\",\n    \"invalidSVGString\": \"Некорректный SVG.\",\n    \"cannotResolveCollabServer\": \"Не удалось подключиться к серверу совместного редактирования. Перезагрузите страницу и повторите попытку.\",\n    \"importLibraryError\": \"Не удалось загрузить библиотеку\",\n    \"collabSaveFailed\": \"Не удалось сохранить в базу данных. Если проблема повторится, нужно будет сохранить файл локально, чтобы быть уверенным, что вы не потеряете вашу работу.\",\n    \"collabSaveFailed_sizeExceeded\": \"Не удалось сохранить в базу данных. Похоже, что холст слишком большой. Нужно сохранить файл локально, чтобы быть уверенным, что вы не потеряете вашу работу.\",\n    \"imageToolNotSupported\": \"Изображения отключены.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Похоже, вы используете браузер Brave с включенной опцией <bold>Агрессивно блокировать отслеживание</bold>.\",\n      \"line2\": \"Это может привести к поломке <bold>Текстовых объектов</bold> на рисунке.\",\n      \"line3\": \"Мы настоятельно рекомендуем отключить эту настройку. Для этого нужно выполнить <link>эти шаги</link>.\",\n      \"line4\": \"Если отключение этой настройки не исправит отображение текстовых объектов, создайте <issueLink>issue</issueLink> на нашем GitHub или напишите нам в <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"Элементы IFrame не могут быть добавлены в библиотеку.\",\n      \"image\": \"\"\n    },\n    \"asyncPasteFailedOnRead\": \"Не удалось вставить (невозможно прочитать из системного буфера обмена).\",\n    \"asyncPasteFailedOnParse\": \"Не удалось вставить.\",\n    \"copyToSystemClipboardFailed\": \"Не удалось скопировать в буфер обмена.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Выделение области\",\n    \"image\": \"Вставить изображение\",\n    \"rectangle\": \"Прямоугольник\",\n    \"diamond\": \"Ромб\",\n    \"ellipse\": \"Эллипс\",\n    \"arrow\": \"Cтрелка\",\n    \"line\": \"Линия\",\n    \"freedraw\": \"Чертить\",\n    \"text\": \"Текст\",\n    \"library\": \"Библиотека\",\n    \"lock\": \"Сохранять выбранный инструмент активным после рисования\",\n    \"penMode\": \"Режим пера - предотвращение касания\",\n    \"link\": \"Добавить/обновить ссылку для выбранной фигуры\",\n    \"eraser\": \"Ластик\",\n    \"frame\": \"\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"\",\n    \"laser\": \"Лазерная указка\",\n    \"hand\": \"Рука (перемещение холста)\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"Из Mermaid в Excalidraw\",\n    \"magicSettings\": \"Параметры AI\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Операции холста\",\n    \"selectedShapeActions\": \"Операции выбранной фигуры\",\n    \"shapes\": \"Фигуры\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Чтобы двигать холст, удерживайте колесо мыши или пробел во время перетаскивания, или используйте инструмент \\\"Рука\\\"\",\n    \"linearElement\": \"Нажмите, чтобы начать несколько точек, перетащите для одной линии\",\n    \"freeDraw\": \"Нажмите и перетаскивайте, отпустите по завершении\",\n    \"text\": \"Совет: при выбранном инструменте выделения дважды щёлкните в любом месте, чтобы добавить текст\",\n    \"embeddable\": \"\",\n    \"text_selected\": \"Дважды щелкните мышью или нажмите ENTER, чтобы редактировать текст\",\n    \"text_editing\": \"Нажмите Escape либо Ctrl или Cmd + ENTER для завершения редактирования\",\n    \"linearElementMulti\": \"Кликните на последней точке или нажмите Escape или Enter чтобы закончить\",\n    \"lockAngle\": \"Вы можете ограничить угол удерживая SHIFT\",\n    \"resize\": \"Вы можете ограничить пропорции, удерживая SHIFT во время изменения размеров,\\nудерживайте ALT чтобы изменить размер из центра\",\n    \"resizeImage\": \"Вы можете свободно изменять размеры, удерживая кнопку SHIFT,\\nудерживайте кнопку ALT, чтобы изменять размер относительно центра\",\n    \"rotate\": \"Вы можете ограничить углы, удерживая SHIFT во время вращения\",\n    \"lineEditor_info\": \"Удерживайте CtrlOrCmd и дважды кликните или нажмите CtrlOrCmd + Enter для редактирования точек\",\n    \"lineEditor_pointSelected\": \"Нажмите Delete для удаления точки (точек),\\nCtrl+D или Cmd+D для дублирования, перетащите для перемещения\",\n    \"lineEditor_nothingSelected\": \"Выберите точку для редактирования (удерживайте SHIFT выбора нескольких точек),\\nили удерживайте Alt и кликните для добавления новых точек\",\n    \"placeImage\": \"Щелкните, чтобы разместить изображение, или нажмите и перетащите, чтобы установить его размер вручную\",\n    \"publishLibrary\": \"Опубликовать свою собственную библиотеку\",\n    \"bindTextToElement\": \"Нажмите Enter для добавления текста\",\n    \"deepBoxSelect\": \"Удерживайте Ctrl или Cmd для глубокого выделения, чтобы предотвратить перетаскивание\",\n    \"eraserRevert\": \"Удерживайте Alt, чтобы вернуть элементы, отмеченные для удаления\",\n    \"firefox_clipboard_write\": \"Эта функция может быть включена при изменении значения флага \\\"dom.events.asyncClipboard.clipboardItem\\\" на \\\"true\\\". Чтобы изменить флаги браузера в Firefox, посетите страницу \\\"about:config\\\".\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Не удается отобразить предпросмотр\",\n    \"canvasTooBig\": \"Сцена слишком большая.\",\n    \"canvasTooBigTip\": \"Совет: попробуйте сблизить элементы рисунка.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Возникла ошибка. Попробуйте <button>перезагрузить страницу.</button>\",\n    \"clearCanvasMessage\": \"Если перезагрузка страницы не помогла, попробуйте <button>очистить холст.</button>\",\n    \"clearCanvasCaveat\": \" Текущая работа будет утеряна \",\n    \"trackedToSentry\": \"Ошибка с идентификатором {{eventId}} отслеживается в нашей системе.\",\n    \"openIssueMessage\": \"Для безопасности информация о вашей сцене не включена в ошибку. Если в сцене нет ничего конфиденциального, пожалуйста следуйте нашим <button>баг трекере.</button> Пожалуйста, приложите информацию ниже, скопировав и вставив её, в issue GitHub.\",\n    \"sceneContent\": \"Содержание сцены:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Вы можете пригласить людей в текущую сцену для совместной работы.\",\n    \"desc_privacy\": \"Не беспокойтесь — во время сеанса используется сквозное шифрование. Всё, что вы нарисуете, останется конфиденциальным и не будет доступно даже нашему серверу.\",\n    \"button_startSession\": \"Начать сеанс\",\n    \"button_stopSession\": \"Завершить сеанс\",\n    \"desc_inProgressIntro\": \"Сеанс совместной работы запущен.\",\n    \"desc_shareLink\": \"Поделитесь этой ссылкой со всеми участниками:\",\n    \"desc_exitSession\": \"Завершив сеанс, вы выйдете из комнаты, но сможете продолжить работать с документом локально. Это не повлияет на работу других пользователей — они смогут продолжить совместную работу с их версией документа.\",\n    \"shareTitle\": \"Присоединиться к активной совместной сессии на Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Ошибка\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Сохранить на диск\",\n    \"disk_details\": \"Экспортировать данные сцены в файл, из которого можно импортировать позже.\",\n    \"disk_button\": \"Сохранить в файл\",\n    \"link_title\": \"Поделитесь ссылкой\",\n    \"link_details\": \"Экспорт ссылки только для чтения.\",\n    \"link_button\": \"Экспорт в ссылку\",\n    \"excalidrawplus_description\": \"Сохраните сцену в ваше рабочее пространство Excalidraw+.\",\n    \"excalidrawplus_button\": \"Экспорт\",\n    \"excalidrawplus_exportError\": \"Не удалось экспортировать в Excalidraw+ на данный момент...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Прочитайте наш блог\",\n    \"click\": \"нажать\",\n    \"deepSelect\": \"Глубокое выделение\",\n    \"deepBoxSelect\": \"Глубокое выделение рамкой, и предотвращение перетаскивания\",\n    \"curvedArrow\": \"Изогнутая стрелка\",\n    \"curvedLine\": \"Изогнутая линия\",\n    \"documentation\": \"Документация\",\n    \"doubleClick\": \"двойной клик\",\n    \"drag\": \"перетащить\",\n    \"editor\": \"Редактор\",\n    \"editLineArrowPoints\": \"Редактировать концы линий/стрелок\",\n    \"editText\": \"Редактировать текст / добавить метку\",\n    \"github\": \"Нашли проблему? Отправьте\",\n    \"howto\": \"Следуйте нашим инструкциям\",\n    \"or\": \"или\",\n    \"preventBinding\": \"Предотвращать привязку стрелок\",\n    \"tools\": \"Инструменты\",\n    \"shortcuts\": \"Горячие клавиши\",\n    \"textFinish\": \"Закончить редактирование (текстовый редактор)\",\n    \"textNewLine\": \"Добавить новую строку (текстовый редактор)\",\n    \"title\": \"Помощь\",\n    \"view\": \"Просмотр\",\n    \"zoomToFit\": \"Отмастштабировать, чтобы поместились все элементы\",\n    \"zoomToSelection\": \"Увеличить до выделенного\",\n    \"toggleElementLock\": \"Заблокировать/разблокировать выделение\",\n    \"movePageUpDown\": \"Сдвинуть страницу вверх/вниз\",\n    \"movePageLeftRight\": \"Сдвинуть страницу вправо/влево\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Очистить холст\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Опубликовать библиотеку\",\n    \"itemName\": \"Название объекта\",\n    \"authorName\": \"Имя автора\",\n    \"githubUsername\": \"Имя пользователя GitHub\",\n    \"twitterUsername\": \"Имя пользователя в Twitter\",\n    \"libraryName\": \"Название библиотеки\",\n    \"libraryDesc\": \"Описание библиотеки\",\n    \"website\": \"Веб-сайт\",\n    \"placeholder\": {\n      \"authorName\": \"Ваше имя или имя пользователя\",\n      \"libraryName\": \"Название вашей библиотеки\",\n      \"libraryDesc\": \"Описание вашей библиотеки, которое поможет людям понять её назначение\",\n      \"githubHandle\": \"Имя пользователя GitHub (необязательно), чтобы вы смогли редактировать библиотеку после её отправки на проверку\",\n      \"twitterHandle\": \"Имя пользователя в Twitter (необязательно), чтобы мы знали, кого упомянуть при продвижении в Twitter\",\n      \"website\": \"Ссылка на ваш личный или какой-то другой сайт (необязательно)\"\n    },\n    \"errors\": {\n      \"required\": \"Обязательно\",\n      \"website\": \"Введите допустимый URL-адрес\"\n    },\n    \"noteDescription\": \"Отправить вашу библиотеку для включения в <link>хранилище публичных библиотек</link>, чтобы другие люди могли использовать объекты из вашей библиотеки в своих рисунках.\",\n    \"noteGuidelines\": \"Библиотека должна быть подтверждена вручную. Пожалуйста, прочтите <link>рекомендации</link> перед отправкой. Вам понадобится учетная запись GitHub, чтобы общаться и вносить изменения при необходимости, но это не обязательно.\",\n    \"noteLicense\": \"Выполняя отправку, вы соглашаетесь с тем, что библиотека будет опубликована под <link>лицензией MIT, </link>, что, вкратце, означает, что каждый может использовать её без ограничений.\",\n    \"noteItems\": \"Каждый объект в библиотеке должен иметь свое собственное имя, чтобы по нему можно было фильтровать. Следующие объекты библиотеки будут включены:\",\n    \"atleastOneLibItem\": \"Пожалуйста, выберите хотя бы один объект в библиотеке, чтобы начать\",\n    \"republishWarning\": \"Примечание: некоторые из выбранных элементов помечены как уже опубликованные/отправленные. Вы должны повторно отправить элементы только при обновлении существующей библиотеки или сдаче работы.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Библиотека отправлена\",\n    \"content\": \"Благодарим вас, {{authorName}}. Ваша библиотека была отправлена на проверку. Вы можете отслеживать статус<link>здесь</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Сброс библиотеки\",\n    \"removeItemsFromLib\": \"Удалить выбранные объекты из библиотеки\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Экспортировать изображение\",\n    \"label\": {\n      \"withBackground\": \"Фон\",\n      \"onlySelected\": \"Только выделенное\",\n      \"darkMode\": \"Темная тема\",\n      \"embedScene\": \"Встроить сцену\",\n      \"scale\": \"Масштаб\",\n      \"padding\": \"Отступ\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Сцена будет сохранена в PNG/SVG файл так, чтобы всю сцену можно будет восстановить из этого файла. Это увеличит размер файла.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Экспорт в PNG\",\n      \"exportToSvg\": \"Экспорт в SVG\",\n      \"copyPngToClipboard\": \"Скопировать PNG в буфер обмена\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Скопировать в буфер обмена\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Ваши данные защищены сквозным (End-to-end) шифрованием. Серверы Excalidraw никогда не получат доступ к ним.\",\n    \"link\": \"Запись блога о сквозном шифровании в Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Угол\",\n    \"element\": \"Элемент\",\n    \"elements\": \"Элементы\",\n    \"height\": \"Высота\",\n    \"scene\": \"Сцены\",\n    \"selected\": \"Выбран\",\n    \"storage\": \"Хранилище\",\n    \"title\": \"Статистика для ботаников\",\n    \"total\": \"Всего\",\n    \"version\": \"Версия\",\n    \"versionCopy\": \"Копировать\",\n    \"versionNotAvailable\": \"Версия не доступна\",\n    \"width\": \"Ширина\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Добавлено в библиотеку\",\n    \"copyStyles\": \"Скопированы стили.\",\n    \"copyToClipboard\": \"Скопировано в буфер обмена.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} скопировано как PNG ({{exportColorScheme}})\",\n    \"fileSaved\": \"Файл сохранён.\",\n    \"fileSavedToFilename\": \"Сохранено в {filename}\",\n    \"canvas\": \"холст\",\n    \"selection\": \"выделение\",\n    \"pasteAsSingleElement\": \"Используйте {{shortcut}}, чтобы вставить один объект,\\nили вставьте в существующий текстовый редактор\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Прозрачный\",\n    \"black\": \"Чёрный\",\n    \"white\": \"Белый\",\n    \"red\": \"Красный\",\n    \"pink\": \"Розовый\",\n    \"grape\": \"Виноградный\",\n    \"violet\": \"Фиолетовый\",\n    \"gray\": \"Серый\",\n    \"blue\": \"Синий\",\n    \"cyan\": \"Голубой\",\n    \"teal\": \"Бирюзовый\",\n    \"green\": \"Зелёный\",\n    \"yellow\": \"Жёлтый\",\n    \"orange\": \"Оранжевый\",\n    \"bronze\": \"Бронзовый\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Все ваши данные сохраняются локально в вашем браузере.\",\n      \"center_heading_plus\": \"Хотите перейти на Excalidraw+?\",\n      \"menuHint\": \"Экспорт, настройки, языки, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Экспорт, настройки и другое...\",\n      \"center_heading\": \"Диаграммы. Просто.\",\n      \"toolbarHint\": \"Выберите инструмент и начните рисовать!\",\n      \"helpHint\": \"Сочетания клавиш и помощь\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Часто используемые пользовательские цвета\",\n    \"colors\": \"Цвета\",\n    \"shades\": \"Оттенки\",\n    \"hexCode\": \"Шестнадцатеричный код\",\n    \"noShades\": \"Нет доступных оттенков для этого цвета\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Экспортировать как изображение\",\n        \"button\": \"Экспортировать как изображение\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Сохранить на диск\",\n        \"button\": \"Сохранить на диск\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Экспорт в Excalidraw+\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Загрузить из файла\",\n        \"button\": \"Загрузить из файла\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Загрузить по ссылке\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Из Mermaid в Excalidraw\",\n    \"button\": \"Вставить\",\n    \"description\": \"\",\n    \"syntax\": \"Синтаксис Mermaid\",\n    \"preview\": \"Предпросмотр\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}