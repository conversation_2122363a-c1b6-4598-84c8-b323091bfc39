{"version": 3, "sources": ["../../../locales/sk-SK.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n    \"pasteAsPlaintext\": \"V<PERSON>žiť ako obyčajný text\",\n    \"pasteCharts\": \"Vložiť grafy\",\n    \"selectAll\": \"<PERSON>ybra<PERSON> všetko\",\n    \"multiSelect\": \"<PERSON>ridať prvok do výberu\",\n    \"moveCanvas\": \"Pohyb plátna\",\n    \"cut\": \"Vystrihnúť\",\n    \"copy\": \"Kop<PERSON>rovať\",\n    \"copyAsPng\": \"Kop<PERSON>rovať do schránky ako PNG\",\n    \"copyAsSvg\": \"Kopírovať do schránky ako SVG\",\n    \"copyText\": \"Kop<PERSON>rovať do schránky ako text\",\n    \"copySource\": \"<PERSON>p<PERSON>rovať kód do schránky\",\n    \"convertToCode\": \"Konvertovať na kód\",\n    \"bringForward\": \"Presunúť o úroveň dopredu\",\n    \"sendToBack\": \"Presunúť dozadu\",\n    \"bringToFront\": \"Presunúť dopredu\",\n    \"sendBackward\": \"Presunúť o úroveň dozadu\",\n    \"delete\": \"<PERSON><PERSON>az<PERSON><PERSON>\",\n    \"copyStyles\": \"<PERSON>p<PERSON>rovať štýly\",\n    \"pasteStyles\": \"Vložiť štýly\",\n    \"stroke\": \"Obrys\",\n    \"background\": \"Pozadie\",\n    \"fill\": \"Výplň\",\n    \"strokeWidth\": \"Hrúbka obrysu\",\n    \"strokeStyle\": \"Štýl obrysu\",\n    \"strokeStyle_solid\": \"Plný\",\n    \"strokeStyle_dashed\": \"Čiarkovaný\",\n    \"strokeStyle_dotted\": \"Bodkovaný\",\n    \"sloppiness\": \"Štylizácia\",\n    \"opacity\": \"Priehľadnosť\",\n    \"textAlign\": \"Zarovnanie textu\",\n    \"edges\": \"Okraje\",\n    \"sharp\": \"Ostré\",\n    \"round\": \"Zaokrúhlené\",\n    \"arrowheads\": \"Zakončenie šípky\",\n    \"arrowhead_none\": \"Žiadne\",\n    \"arrowhead_arrow\": \"Šípka\",\n    \"arrowhead_bar\": \"Čiara\",\n    \"arrowhead_circle\": \"Kruh\",\n    \"arrowhead_circle_outline\": \"Kruh (obrys)\",\n    \"arrowhead_triangle\": \"Trojuholník\",\n    \"arrowhead_triangle_outline\": \"Trojuholník (obrys)\",\n    \"arrowhead_diamond\": \"Diamant\",\n    \"arrowhead_diamond_outline\": \"Diamant (obrys)\",\n    \"fontSize\": \"Veľkosť písma\",\n    \"fontFamily\": \"Písmo\",\n    \"addWatermark\": \"Pridať \\\"Vytvorené s Excalidraw\\\"\",\n    \"handDrawn\": \"Ručne písané\",\n    \"normal\": \"Normálne\",\n    \"code\": \"Kód\",\n    \"small\": \"Malé\",\n    \"medium\": \"Stredné\",\n    \"large\": \"Veľké\",\n    \"veryLarge\": \"Veľmi veľké\",\n    \"solid\": \"Plná\",\n    \"hachure\": \"Šrafovaná\",\n    \"zigzag\": \"Cik-cak\",\n    \"crossHatch\": \"Mriežkovaná\",\n    \"thin\": \"Tenká\",\n    \"bold\": \"Hrubá\",\n    \"left\": \"Doľava\",\n    \"center\": \"Na stred\",\n    \"right\": \"Doprava\",\n    \"extraBold\": \"Veľmi hrubá\",\n    \"architect\": \"Architekt\",\n    \"artist\": \"Umelec\",\n    \"cartoonist\": \"Ilustrátor\",\n    \"fileTitle\": \"Názov súboru\",\n    \"colorPicker\": \"Výber farby\",\n    \"canvasColors\": \"Použité na plátne\",\n    \"canvasBackground\": \"Pozadie plátna\",\n    \"drawingCanvas\": \"Kresliace plátno\",\n    \"layers\": \"Vrstvy\",\n    \"actions\": \"Akcie\",\n    \"language\": \"Jazyk\",\n    \"liveCollaboration\": \"Živá spolupráca...\",\n    \"duplicateSelection\": \"Duplikovať\",\n    \"untitled\": \"Bez názvu\",\n    \"name\": \"Meno\",\n    \"yourName\": \"Vaše meno\",\n    \"madeWithExcalidraw\": \"Vytvorené s Excalidraw\",\n    \"group\": \"Zoskupiť\",\n    \"ungroup\": \"Zrušiť zoskupenie\",\n    \"collaborators\": \"Spolupracovníci\",\n    \"showGrid\": \"Zobraziť mriežku\",\n    \"addToLibrary\": \"Pridať do knižnice\",\n    \"removeFromLibrary\": \"Odstrániť z knižnice\",\n    \"libraryLoadingMessage\": \"Načítavanie knižnice…\",\n    \"libraries\": \"Prehliadať knižnice\",\n    \"loadingScene\": \"Načítavanie scény…\",\n    \"align\": \"Zarovnanie\",\n    \"alignTop\": \"Zarovnať nahor\",\n    \"alignBottom\": \"Zarovnať nadol\",\n    \"alignLeft\": \"Zarovnať doľava\",\n    \"alignRight\": \"Zarovnať doprava\",\n    \"centerVertically\": \"Zarovnať zvislo na stred\",\n    \"centerHorizontally\": \"Zarovnať vodorovne na stred\",\n    \"distributeHorizontally\": \"Rozmiestniť vodorovne\",\n    \"distributeVertically\": \"Rozmiestniť zvisle\",\n    \"flipHorizontal\": \"Prevrátiť vodorovne\",\n    \"flipVertical\": \"Prevrátiť zvislo\",\n    \"viewMode\": \"Režim zobrazenia\",\n    \"share\": \"Zdieľať\",\n    \"showStroke\": \"Zobraziť výber farby pre obrys\",\n    \"showBackground\": \"Zobraziť výber farby pre pozadie\",\n    \"toggleTheme\": \"Prepnúť tému\",\n    \"personalLib\": \"Moja knižnica\",\n    \"excalidrawLib\": \"Excalidraw knižnica\",\n    \"decreaseFontSize\": \"Zmenšiť veľkosť písma\",\n    \"increaseFontSize\": \"Zväčšiť veľkosť písma\",\n    \"unbindText\": \"Zrušiť previazanie textu\",\n    \"bindText\": \"Previazať text s kontajnerom\",\n    \"createContainerFromText\": \"Zabaliť text do kontajneru\",\n    \"link\": {\n      \"edit\": \"Upraviť odkaz\",\n      \"editEmbed\": \"Editovať a zapustiť odkaz\",\n      \"create\": \"Vytvoriť odkaz\",\n      \"createEmbed\": \"Vytvoriť a zapustiť odkaz\",\n      \"label\": \"Odkaz\",\n      \"labelEmbed\": \"Zapustiť odkaz\",\n      \"empty\": \"Nie je nastavený žiaden odkaz\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Upraviť čiaru\",\n      \"exit\": \"Ukončiť editovanie čiary\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Zamknúť\",\n      \"unlock\": \"Odomknúť\",\n      \"lockAll\": \"Zamknúť všetko\",\n      \"unlockAll\": \"Odomknúť všetko\"\n    },\n    \"statusPublished\": \"Zverejnené\",\n    \"sidebarLock\": \"Nechať bočný panel otvorený\",\n    \"selectAllElementsInFrame\": \"Vybrať všetky prvky v ráme\",\n    \"removeAllElementsFromFrame\": \"Odstrániť všetky prvky z rámu\",\n    \"eyeDropper\": \"Vybrať farbu z plátna\",\n    \"textToDiagram\": \"Text na diagram\",\n    \"prompt\": \"Inštrukcia\"\n  },\n  \"library\": {\n    \"noItems\": \"Zatiaľ neboli pridané žiadne položky...\",\n    \"hint_emptyLibrary\": \"Vyberte položku z plátna pre jej pridanie do knižnice alebo použite knižnicu z verejného zoznamu knižníc nižšie.\",\n    \"hint_emptyPrivateLibrary\": \"Vyberte položku z plátna pre jej pridanie do knižnice.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Obnoviť plátno\",\n    \"exportJSON\": \"Exportovať do súboru\",\n    \"exportImage\": \"Exportovať obrázok...\",\n    \"export\": \"Uložiť do...\",\n    \"copyToClipboard\": \"Kopírovať do schránky\",\n    \"save\": \"Uložiť do aktuálneho súboru\",\n    \"saveAs\": \"Uložiť ako\",\n    \"load\": \"Otvoriť\",\n    \"getShareableLink\": \"Získať odkaz na zdieľanie\",\n    \"close\": \"Zavrieť\",\n    \"selectLanguage\": \"Zvoliť jazyk\",\n    \"scrollBackToContent\": \"Vrátiť sa späť na obsah\",\n    \"zoomIn\": \"Priblížiť\",\n    \"zoomOut\": \"Oddialiť\",\n    \"resetZoom\": \"Obnoviť priblíženie\",\n    \"menu\": \"Ponuka\",\n    \"done\": \"Hotovo\",\n    \"edit\": \"Upraviť\",\n    \"undo\": \"Späť\",\n    \"redo\": \"Znova\",\n    \"resetLibrary\": \"Obnoviť knižnicu\",\n    \"createNewRoom\": \"Vytvoriť novú miestnosť\",\n    \"fullScreen\": \"Celá obrazovka\",\n    \"darkMode\": \"Tmavý režim\",\n    \"lightMode\": \"Svetlý režim\",\n    \"zenMode\": \"Režim zen\",\n    \"objectsSnapMode\": \"Prichytiť k objektom\",\n    \"exitZenMode\": \"Zrušiť režim zen\",\n    \"cancel\": \"Zrušiť\",\n    \"clear\": \"Vymazať\",\n    \"remove\": \"Odstrániť\",\n    \"embed\": \"Prepnúť zapustenie\",\n    \"publishLibrary\": \"Uverejniť\",\n    \"submit\": \"Potvrdiť\",\n    \"confirm\": \"Potvrdiť\",\n    \"embeddableInteractionButton\": \"Kliknite pre interakciu\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Týmto sa vyčistí celé plátno. Ste si istí?\",\n    \"couldNotCreateShareableLink\": \"Nepodarilo sa vytvoriť odkaz na zdieľanie.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Nepodarilo sa vytvoriť odkaz na zdieľanie: scéna je príliš veľká\",\n    \"couldNotLoadInvalidFile\": \"Nepodarilo sa načítať nevalidný súbor\",\n    \"importBackendFailed\": \"Nepdarilo sa importovanie zo serveru.\",\n    \"cannotExportEmptyCanvas\": \"Nie je možné exportovať prázdne plátno.\",\n    \"couldNotCopyToClipboard\": \"Kopírovanie do schránky sa nepodarilo.\",\n    \"decryptFailed\": \"Nepodarilo sa rozšifrovať údaje.\",\n    \"uploadedSecurly\": \"Nahratie je zabezpečené end-to-end šifrovaním, takže Excalidraw server a tretie strany nedokážu prečítať jeho obsah.\",\n    \"loadSceneOverridePrompt\": \"Nahratie externej kresby nahradí existujúci obsah. Prajete si pokračovať?\",\n    \"collabStopOverridePrompt\": \"Ukončenie schôdze nahradí vašu predchádzajúcu lokálne uloženú scénu. Ste si istý?\\n\\n(Ak si chcete ponechať lokálnu scénu, jednoducho iba zavrite kartu prehliadača.)\",\n    \"errorAddingToLibrary\": \"Nepodarilo sa pridať položku do knižnice\",\n    \"errorRemovingFromLibrary\": \"Nepodarilo sa odstrániť položku z knižnice\",\n    \"confirmAddLibrary\": \"Týmto sa pridá {{numShapes}} tvar(ov) do vašej knižnice. Ste si istí?\",\n    \"imageDoesNotContainScene\": \"Tento obrázok neobsahuje žiadne údaje scény. Zvolili ste možnosť zahrnúť scénu počas exportu?\",\n    \"cannotRestoreFromImage\": \"Nepodarilo sa obnoviť scénu z tohto obrázkového súboru\",\n    \"invalidSceneUrl\": \"Nepodarilo sa načítať scénu z poskytnutej URL. Je nevalidná alebo neobsahuje žiadne validné Excalidraw JSON dáta.\",\n    \"resetLibrary\": \"Týmto vyprázdnite vašu knižnicu. Ste si istý?\",\n    \"removeItemsFromsLibrary\": \"Odstrániť {{count}} položiek z knižnice?\",\n    \"invalidEncryptionKey\": \"Šifrovací kľúč musí mať 22 znakov. Živá spolupráca je vypnutá.\",\n    \"collabOfflineWarning\": \"Internetové pripojenie nie je dostupné.\\nVaše zmeny nebudú uložené!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Nepodporovaný typ súboru.\",\n    \"imageInsertError\": \"Nepodarilo sa vložiť obrázok. Skúste to znova neskôr...\",\n    \"fileTooBig\": \"Súbor je príliš veľký. Maximálna povolená veľkosť je {{maxSize}}.\",\n    \"svgImageInsertError\": \"Nepodarilo sa vložiť SVG obrázok. SVG formát je pravdepodobne nevalidný.\",\n    \"failedToFetchImage\": \"Načítanie obrázka zlyhalo.\",\n    \"invalidSVGString\": \"Nevalidné SVG.\",\n    \"cannotResolveCollabServer\": \"Nepodarilo sa pripojiť ku kolaboračnému serveru. Prosím obnovte stránku a skúste to znovu.\",\n    \"importLibraryError\": \"Nepodarilo sa načítať knižnicu\",\n    \"collabSaveFailed\": \"Uloženie do databázy sa nepodarilo. Ak tento problém pretrváva uložte si váš súbor lokálne aby ste nestratili vašu prácu.\",\n    \"collabSaveFailed_sizeExceeded\": \"Uloženie do databázy sa nepodarilo, pretože veľkosť plátna je príliš veľká. Uložte si váš súbor lokálne aby ste nestratili vašu prácu.\",\n    \"imageToolNotSupported\": \"Obrázky sú vypnuté.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Vyzerá to, že používate prehliadač Brave so zapnutým nastavením pre <bold>agresívne blokovanie</bold>.\",\n      \"line2\": \"To môže spôsobiť nesprávne zobrazenie <bold>textových prvkov</bold> vo vašej kresbe.\",\n      \"line3\": \"Dôrazne odporúčame vypnutie toho nastavenia. Môžete tak spraviť vykonaním <link>týchto krokov</link>.\",\n      \"line4\": \"Ak vypnutie toho nastavenia nevyrieši problém so zobrazením textových prvkov, prosím ohláste <issueLink>problém</issueLink> na našom GitHub-e alebo nám napíšte na náš <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Zapustené prvky nie je možné pridať do knižnice.\",\n      \"iframe\": \"Vložené rámce IFrame nie je možné pridať do knižnice.\",\n      \"image\": \"Podpora pre pridávanie obrázkov do knižnice bude dostupná už čoskoro!\"\n    },\n    \"asyncPasteFailedOnRead\": \"Vloženie sa nepodarilo (nebolo možné prečítať obsah schránky).\",\n    \"asyncPasteFailedOnParse\": \"Vloženie sa nepodarilo.\",\n    \"copyToSystemClipboardFailed\": \"Kopírovanie do schránky sa nepodarilo.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Výber\",\n    \"image\": \"Vložiť obrázok\",\n    \"rectangle\": \"Obdĺžnik\",\n    \"diamond\": \"Diamant\",\n    \"ellipse\": \"Elipsa\",\n    \"arrow\": \"Šípka\",\n    \"line\": \"Čiara\",\n    \"freedraw\": \"Kresliť\",\n    \"text\": \"Text\",\n    \"library\": \"Knižnica\",\n    \"lock\": \"Nechať zvolený nástroj aktívny po skončení kreslenia\",\n    \"penMode\": \"Režim pera – zabrániť dotyku\",\n    \"link\": \"Pridať/ Upraviť odkaz pre vybraný tvar\",\n    \"eraser\": \"Guma\",\n    \"frame\": \"Nástroj rám\",\n    \"magicframe\": \"Drôtený model na kód\",\n    \"embeddable\": \"Web Embed\",\n    \"laser\": \"Laserový ukazovateľ\",\n    \"hand\": \"Ruka (nástroj pre pohyb plátna)\",\n    \"extraTools\": \"Ďalšie nástroje\",\n    \"mermaidToExcalidraw\": \"Mermaid do Excalidraw\",\n    \"magicSettings\": \"AI nastavenia\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Akcie plátna\",\n    \"selectedShapeActions\": \"Akcie tvarov z výberu\",\n    \"shapes\": \"Tvary\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Pre pohyb plátna podržte koliesko myši alebo medzerník počas ťahania, alebo použite nástroj ruka\",\n    \"linearElement\": \"Kliknite na vloženie viacerých bodov, potiahnite na vytvorenie jednej priamky\",\n    \"freeDraw\": \"Kliknite a ťahajte, pustite na ukončenie\",\n    \"text\": \"Tip: text môžete pridať aj dvojklikom kdekoľvek, ak je zvolený nástroj výber\",\n    \"embeddable\": \"Kliknite a ťahajte pre zapustenie webovej stránky\",\n    \"text_selected\": \"Použite dvojklik alebo stlačte Enter na editáciu textu\",\n    \"text_editing\": \"Stlačte Escape alebo CtrlOrCmd+ENTER na ukončenie editovania\",\n    \"linearElementMulti\": \"Kliknite na počiatočný bod alebo stlačte Escape alebo Enter na ukončenie\",\n    \"lockAngle\": \"Počas rotácie obmedzíte uhol podržaním SHIFT\",\n    \"resize\": \"Počas zmeny veľkosti zachováte proporcie podržaním SHIFT,\\\\npodržaním ALT meníte veľkosť so zachovaním stredu\",\n    \"resizeImage\": \"Podržte SHIFT pre voľnú zmenu veľkosti, podržte ALT pre zmenu veľkosti od stredu\",\n    \"rotate\": \"Počas rotácie obmedzíte uhol podržaním SHIFT\",\n    \"lineEditor_info\": \"Podržte CtrlOrCmd a kliknite dva krát alebo stlačte CtrlOrCmd + Enter pre editáciu bodov\",\n    \"lineEditor_pointSelected\": \"Stačte Delete na vymazanie bodu (bodov), CtrlOrCmd+D na duplikovanie, alebo potiahnite na presunutie\",\n    \"lineEditor_nothingSelected\": \"Zvoľte bod na upravovanie (podržte SHIFT pre zvolenie viacerých bodov) alebo podržte Alt a kliknite na pridanie nového bodu\",\n    \"placeImage\": \"Kliknite pre umiestnenie obrázka alebo kliknite a ťahajte pre zmenu jeho veľkosti\",\n    \"publishLibrary\": \"Uverejniť vašu knižnicu\",\n    \"bindTextToElement\": \"Stlačte enter na pridanie textu\",\n    \"deepBoxSelect\": \"Podržte CtrlOrCmd na výber v skupine alebo zamedzeniu poťiahnutia\",\n    \"eraserRevert\": \"Podržte Alt pre prehodenie položiek určených na vymazanie\",\n    \"firefox_clipboard_write\": \"Táto sa funkcionalita sa dá zapnúť nastavením \\\"dom.events.asyncClipboard.clipboardItem\\\" na \\\"true\\\". Pre zmenu nastavení vo Firefox-e otvorte stránku \\\"about:config\\\".\",\n    \"disableSnapping\": \"Podržte CtrlOrCmd pre vypnutie prichytávania\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Nie je možné zobraziť náhľad plátna\",\n    \"canvasTooBig\": \"Plátno je možno príliš veľké.\",\n    \"canvasTooBigTip\": \"Tip: skúste presunúť najvzdialenejšie prvky bližšie k sebe.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Nastala chyba. Vyskúšajte <button>obnoviť stránku.</button>\",\n    \"clearCanvasMessage\": \"Ak obnovenie stránky nepomáha, vyskúšajte <button>vyčistiť plátno.</button>\",\n    \"clearCanvasCaveat\": \" To bude mať za následok stratu práce \",\n    \"trackedToSentry\": \"Chyba s identifikátorom {{eventId}} bola zaznamenaná v našom systéme.\",\n    \"openIssueMessage\": \"Boli sme veľmi opatrní, aby informácie vašej scény neboli v chybe zaznamenané. Ak vaša scéna nie je súkromná, prosím zvážte pokračovanie na naše <button>hlásenie chýb.</button> Prosím zahrňte informácie nižšie pomocou kopírovania a prilepenia do GitHub issue.\",\n    \"sceneContent\": \"Obsah scény:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Pozvite niekoho do svojej aktuálnej scény a pracujte spoločne.\",\n    \"desc_privacy\": \"Nemajte obavy, schôdza používa end-to-end šifrovanie, takže všetko čo nakreslíte je súkromné. Dokonca, ani náš server dedokáže prečítať, čo ste vytvorili.\",\n    \"button_startSession\": \"Začať schôdzu\",\n    \"button_stopSession\": \"Ukončiť schôdzu\",\n    \"desc_inProgressIntro\": \"Práve prebieha živá schôdza.\",\n    \"desc_shareLink\": \"Zdieľajte tento odkaz s osobou, s ktorou chcete spolupracovať:\",\n    \"desc_exitSession\": \"Ukončenie schôdze vás odpojí z miestnosti, avšak naďalej budete môcť pokračovať v práci na scéne lokálne. Toto neovplyvní ostatných spolupracovníkov a stále budú môcť spolupracovať na ich verzii.\",\n    \"shareTitle\": \"Pripojiť sa k živej schôdzi na Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Chyba\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Uložiť na disk\",\n    \"disk_details\": \"Exportovať údaje scény do súboru, z ktorého môžu byť neskôr importované.\",\n    \"disk_button\": \"Uložiť do súboru\",\n    \"link_title\": \"Odkaz na zdieľanie\",\n    \"link_details\": \"Exportovať ako odkaz iba na čítanie.\",\n    \"link_button\": \"Exportovať ako odkaz\",\n    \"excalidrawplus_description\": \"Uložiť scénu do vášho Excalidraw+ pracovného priestoru.\",\n    \"excalidrawplus_button\": \"Exportovať\",\n    \"excalidrawplus_exportError\": \"Nepodarilo sa vykonať export do Excalidraw+...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Prečítajte si náš blog\",\n    \"click\": \"kliknutie\",\n    \"deepSelect\": \"Výber v skupine\",\n    \"deepBoxSelect\": \"Výber v skupine alebo zamedzenie poťiahnutia\",\n    \"curvedArrow\": \"Zakrivená šípka\",\n    \"curvedLine\": \"Zakrivená čiara\",\n    \"documentation\": \"Dokumentácia\",\n    \"doubleClick\": \"dvojklik\",\n    \"drag\": \"potiahnutie\",\n    \"editor\": \"Editovanie\",\n    \"editLineArrowPoints\": \"Editácia bodov čiary/šípky\",\n    \"editText\": \"Editácia textu / pridanie štítku\",\n    \"github\": \"Objavili ste problém? Nahláste ho\",\n    \"howto\": \"Postupujte podľa naších návodov\",\n    \"or\": \"alebo\",\n    \"preventBinding\": \"Zakázať pripájanie šípky\",\n    \"tools\": \"Nástroje\",\n    \"shortcuts\": \"Klávesové skratky\",\n    \"textFinish\": \"Ukončenie editovania (text editor)\",\n    \"textNewLine\": \"Vložiť nový riadok (text editor)\",\n    \"title\": \"Pomocník\",\n    \"view\": \"Zobrazenie\",\n    \"zoomToFit\": \"Priblížiť aby boli zahrnuté všetky prvky\",\n    \"zoomToSelection\": \"Priblížiť na výber\",\n    \"toggleElementLock\": \"Zamknúť/odomknúť vybrané\",\n    \"movePageUpDown\": \"Posunúť stranu hore/dole\",\n    \"movePageLeftRight\": \"Posunúť stranu doľava/doprava\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Vyčistiť plátno\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Uverejniť knižnicu\",\n    \"itemName\": \"Názov položky\",\n    \"authorName\": \"Meno autora\",\n    \"githubUsername\": \"Github užívateľské meno\",\n    \"twitterUsername\": \"Twitter užívateľské meno\",\n    \"libraryName\": \"Názov knižnice\",\n    \"libraryDesc\": \"Popis knižnice\",\n    \"website\": \"Webová stránka\",\n    \"placeholder\": {\n      \"authorName\": \"Vaše meno alebo užívateľské meno\",\n      \"libraryName\": \"Názov vašej knižnice\",\n      \"libraryDesc\": \"Popis vašej knižnice, ktorý ostatným pomôže porozumieť jej vhodnému použitiu\",\n      \"githubHandle\": \"GitHub užívateľské meno (nepovinné), aby ste mohli robiť úpravy po tom, čo bude knižnica uverejnená na schválenie\",\n      \"twitterHandle\": \"Twitter užívateľské meno (nepovinné), aby sme vedeli komu pripísať zásluhu pri propagovaní cez Twitter\",\n      \"website\": \"Odkaz na vašu osobnú webovú stránku alebo niekam inam (nepovinné)\"\n    },\n    \"errors\": {\n      \"required\": \"Povinné\",\n      \"website\": \"Zadajte platnú adresu URL\"\n    },\n    \"noteDescription\": \"Uverejnite vašu knižnicu vo <link>verejnom zozname knižníc</link>aby ju aj ostatní mohli použiť v ich náčrtoch.\",\n    \"noteGuidelines\": \"Knižnica musí byť najprv manuálne schválená. Prosím prečítajte si <link>pokyny</link> pred uverejnením. Budete potrebovať Github účet na komunikáciu a vykonanie zmien, ak budú potrebné, avšak nie je to úplne povinné.\",\n    \"noteLicense\": \"Potvrdením súhlasíte, že knižnica bude zverejnená s <link>MIT licenciou, </link>čo v skratke znamená, že ju môže použiť hocikto bez obmedzení.\",\n    \"noteItems\": \"Každá položka v knižnici musí mať svoje vlastné meno, aby sa dala vyhľadať. Súčasťou knižnice budú nasledujúce položky:\",\n    \"atleastOneLibItem\": \"Začnite prosím zvolením aspoň jednej položky z knižnice\",\n    \"republishWarning\": \"Poznámka: Niektoré z vybraných položiek sú už označené ako zverejnené. Ich znovu uverejnenie by ste mali vykovať iba vtedy ak aktualizujete už existujúcu knižnicu alebo požiadavku na uverejnenie.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Knižnica uverejnená\",\n    \"content\": \"Ďakujeme vám {{authorName}}. Vaša knižnica bola uverejnená na posúdenie. Stav môžete skontrolovať<link>tu</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Obnoviť knižnicu\",\n    \"removeItemsFromLib\": \"Odstrániť zvolené položky z knižnice\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Exportovať obrázok\",\n    \"label\": {\n      \"withBackground\": \"Pozadie\",\n      \"onlySelected\": \"Iba vybrané\",\n      \"darkMode\": \"Tmavý režim\",\n      \"embedScene\": \"Zahrnúť scénu\",\n      \"scale\": \"Mierka\",\n      \"padding\": \"Odsadenie\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Údaje scény budú uložené do exportovaného PNG/SVG súboru, takže scéna z neho môže byť opäť obnovená.\\nBude to mať za následok zvýšenie veľkosti súboru.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Exportovať do PNG\",\n      \"exportToSvg\": \"Exportovať do SVG\",\n      \"copyPngToClipboard\": \"Kopírovať PNG do schránky\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Kopírovať do schránky\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Vaše kresby používajú end-to-end šifrovanie, takže ich Excalidraw server nedokáže prečítať.\",\n    \"link\": \"Blog o end-to-end šifrovaní v Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Uhol\",\n    \"element\": \"Prvok\",\n    \"elements\": \"Prvky\",\n    \"height\": \"Výška\",\n    \"scene\": \"Scéna\",\n    \"selected\": \"Vybrané\",\n    \"storage\": \"Úložisko\",\n    \"title\": \"Štatistiky\",\n    \"total\": \"Celkom\",\n    \"version\": \"Verzia\",\n    \"versionCopy\": \"Kliknutím skopírujete\",\n    \"versionNotAvailable\": \"Verzia nie je k dispozícii\",\n    \"width\": \"Šírka\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Pridané do knižnice\",\n    \"copyStyles\": \"Štýly skopírované.\",\n    \"copyToClipboard\": \"Skopírované do schránky.\",\n    \"copyToClipboardAsPng\": \"Kopírovanie {{exportSelection}} do schránky ako PNG prebehlo úspešne\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Súbor uložený.\",\n    \"fileSavedToFilename\": \"Uložený ako {filename}\",\n    \"canvas\": \"plátna\",\n    \"selection\": \"výberu\",\n    \"pasteAsSingleElement\": \"Použitím {{shortcut}} vložte ako samostatný prvok alebo vložte do existujúceho editovaného textu\",\n    \"unableToEmbed\": \"Zapustenie tejto URL nie je povolené. Vytvorte issue na GitHub-e a požiadajte povolenie tejto URL\",\n    \"unrecognizedLinkFormat\": \"Odkaz, ktorý sa snažíte zapustiť nie je v očakávanom formáte. Prosím skúste vložiť 'odkaz na zdieľanie' poskytnutý zdrojovou webovou stránkou\"\n  },\n  \"colors\": {\n    \"transparent\": \"Priehľadná\",\n    \"black\": \"Čierna\",\n    \"white\": \"Biela\",\n    \"red\": \"Červená\",\n    \"pink\": \"Ružová\",\n    \"grape\": \"Hroznová fialová\",\n    \"violet\": \"Fialová\",\n    \"gray\": \"Sivá\",\n    \"blue\": \"Modrá\",\n    \"cyan\": \"Azúrová\",\n    \"teal\": \"Modrozelená\",\n    \"green\": \"Zelená\",\n    \"yellow\": \"Žltá\",\n    \"orange\": \"Oranžová\",\n    \"bronze\": \"Bronzová\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Všetky vaše dáta sú uložené lokálne vo vašom prehliadači.\",\n      \"center_heading_plus\": \"Chceli ste namiesto toho prejsť do Excalidraw+?\",\n      \"menuHint\": \"Exportovanie, nastavenia, jazyky, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Exportovanie, nastavenia a ďalšie...\",\n      \"center_heading\": \"Diagramy. Jednoducho.\",\n      \"toolbarHint\": \"Zvoľte nástroj a začnite kresliť!\",\n      \"helpHint\": \"Klávesové skratky a pomocník\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Najpoužívanejšie vlastné farby\",\n    \"colors\": \"Farby\",\n    \"shades\": \"Odtiene\",\n    \"hexCode\": \"Hex kód\",\n    \"noShades\": \"Pre túto farbu nie sú dostupné žiadne odtiene\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Exportovať ako obrázok\",\n        \"button\": \"Exportovať ako obrázok\",\n        \"description\": \"Exportovať údaje scény ako obrázok, z ktorého môžu byť neskôr importované.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Uložiť na disk\",\n        \"button\": \"Uložiť na disk\",\n        \"description\": \"Exportovať údaje scény do súboru, z ktorého môžu byť neskôr importované.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Exportovať ako Excalidraw+\",\n        \"description\": \"Uložiť scénu do vášho Excalidraw+ pracovného priestoru.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Načítať zo súboru\",\n        \"button\": \"Načítať zo súboru\",\n        \"description\": \"Načítanie zo súboru <bold>nahradí váš existujúci obsah</bold>.<br></br>Vašu kresbu môžete zálohovať jednou z nižšie uvedených možností.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Načítať z odkazu\",\n        \"button\": \"Nahradiť môj obsah\",\n        \"description\": \"Načítanie externej kresby <bold>nahradí váš existujúci obsah</bold>.<br></br>Vašu kresbu môžete zálohovať jednou z nižšie uvedených možností.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Mermaid do Excalidraw\",\n    \"button\": \"Vložiť\",\n    \"description\": \"Aktuálne sú podporované iba <flowchartLink>vývojové diagramy</flowchartLink>, <sequenceLink>sekvenčné diagramy</sequenceLink> a <classLink>diagramy tried</classLink>. Ostatné typy budú v Excalidraw vykreslené ako obrázky.\",\n    \"syntax\": \"Mermaid syntax\",\n    \"preview\": \"Ukážka\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}