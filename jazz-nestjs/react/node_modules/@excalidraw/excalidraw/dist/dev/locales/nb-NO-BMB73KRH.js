import "../chunk-XDFCUUT6.js";

// locales/nb-NO.json
var labels = {
  paste: "Lim inn",
  pasteAsPlaintext: "Lim inn som klartekst",
  pasteCharts: "Lim inn diagrammer",
  selectAll: "Velg alt",
  multiSelect: "Legg til element i utvalg",
  moveCanvas: "Flytt lerretet",
  cut: "Klipp ut",
  copy: "Kopier",
  copyAsPng: "Kopier til PNG",
  copyAsSvg: "Kopier til utklippstavlen som SVG",
  copyText: "Kopier til utklippstavlen som tekst",
  copySource: "",
  convertToCode: "",
  bringForward: "Flytt framover",
  sendToBack: "Send bakerst",
  bringToFront: "Flytt forrest",
  sendBackward: "Send bakover",
  delete: "Slett",
  copyStyles: "Kopier stiler",
  pasteStyles: "Lim inn stiler",
  stroke: "Strek",
  background: "<PERSON>kgrunn",
  fill: "Fyll",
  strokeWidth: "Strektykkelse",
  strokeStyle: "Strekstil",
  strokeStyle_solid: "Heltrukket",
  strokeStyle_dashed: "Stiplet",
  strokeStyle_dotted: "Prikket",
  sloppiness: "Ujevnhet",
  opacity: "Synlighet",
  textAlign: "Tekstjustering",
  edges: "Kanter",
  sharp: "Skarp",
  round: "Rund",
  arrowheads: "Pilspisser",
  arrowhead_none: "Ingen",
  arrowhead_arrow: "Pil",
  arrowhead_bar: "S\xF8yle",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "Trekant",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "Skriftst\xF8rrelse",
  fontFamily: "Fontfamilie",
  addWatermark: 'Legg til "Laget med Excalidraw"',
  handDrawn: "H\xE5ndtegnet",
  normal: "Normal",
  code: "Kode",
  small: "Liten",
  medium: "Medium",
  large: "Stor",
  veryLarge: "Sv\xE6rt stor",
  solid: "Helfarge",
  hachure: "Skravert",
  zigzag: "Sikk-sakk",
  crossHatch: "Krysskravert",
  thin: "Tynn",
  bold: "Tykk",
  left: "Venstre",
  center: "Midtstill",
  right: "H\xF8yre",
  extraBold: "Ekstra tykk",
  architect: "Arkitekt",
  artist: "Kunstner",
  cartoonist: "Tegner",
  fileTitle: "Filnavn",
  colorPicker: "Fargevelger",
  canvasColors: "Brukes p\xE5 lerretet",
  canvasBackground: "Lerretsbakgrunn",
  drawingCanvas: "Lerret",
  layers: "Lag",
  actions: "Handlinger",
  language: "Spr\xE5k",
  liveCollaboration: "Sanntids-samarbeid...",
  duplicateSelection: "Dupliser",
  untitled: "Uten navn",
  name: "Navn",
  yourName: "Ditt navn",
  madeWithExcalidraw: "Laget med Excalidraw",
  group: "Grupp\xE9r utvalg",
  ungroup: "Avgrupp\xE9r utvalg",
  collaborators: "Samarbeidspartnere",
  showGrid: "Vis rutenett",
  addToLibrary: "Legg til i bibliotek",
  removeFromLibrary: "Fjern fra bibliotek",
  libraryLoadingMessage: "Laster bibliotek\u2026",
  libraries: "Bla gjennom biblioteker",
  loadingScene: "Laster inn scene\u2026",
  align: "Juster",
  alignTop: "Juster \xF8verst",
  alignBottom: "Juster nederst",
  alignLeft: "Juster venstre",
  alignRight: "Juster h\xF8yre",
  centerVertically: "Midtstill vertikalt",
  centerHorizontally: "Midtstill horisontalt",
  distributeHorizontally: "Distribuer horisontalt",
  distributeVertically: "Distribuer vertikalt",
  flipHorizontal: "Snu horisontalt",
  flipVertical: "Snu vertikalt",
  viewMode: "Visningsmodus",
  share: "Del",
  showStroke: "Vis fargevelger for kantfarge",
  showBackground: "Vis fargevelger for bakgrunnsfarge",
  toggleTheme: "Veksle tema",
  personalLib: "Personlig bibliotek",
  excalidrawLib: "Excalidraw-bibliotek",
  decreaseFontSize: "Reduser skriftst\xF8rrelse",
  increaseFontSize: "\xD8k skriftst\xF8rrelse",
  unbindText: "Avbind tekst",
  bindText: "Bind tekst til beholderen",
  createContainerFromText: "La tekst flyte i en beholder",
  link: {
    edit: "Rediger lenke",
    editEmbed: "Rediger lenke og bygg inn",
    create: "Opprett lenke",
    createEmbed: "Opprett lenke og bygg inn",
    label: "Lenke",
    labelEmbed: "Lenk & bygg inn",
    empty: "Ingen lenke er valgt"
  },
  lineEditor: {
    edit: "Rediger linje",
    exit: "Avslutt linjeredigering"
  },
  elementLock: {
    lock: "L\xE5s",
    unlock: "L\xE5s opp",
    lockAll: "L\xE5s alle",
    unlockAll: "L\xE5s opp alle"
  },
  statusPublished: "Publisert",
  sidebarLock: "Holde sidemenyen \xE5pen",
  selectAllElementsInFrame: "Velg alle elementene i rammen",
  removeAllElementsFromFrame: "Fjern alle elementer fra rammen",
  eyeDropper: "Velg farge fra lerretet",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "Ingen elementer lagt til enn\xE5...",
  hint_emptyLibrary: "Velg et objekt p\xE5 lerretet for \xE5 legge det til her, eller installer et bibliotek fra den offentlige samlingen under.",
  hint_emptyPrivateLibrary: "Velg et objekt p\xE5 lerretet for \xE5 legge det til her."
};
var buttons = {
  clearReset: "T\xF8m lerretet og tilbakestill bakgrunnsfargen",
  exportJSON: "Eksporter til fil",
  exportImage: "Eksporter bilde...",
  export: "Lagre som...",
  copyToClipboard: "Kopier til utklippstavle",
  save: "Lagre til aktiv fil",
  saveAs: "Lagre som",
  load: "\xC5pne",
  getShareableLink: "F\xE5 delingslenke",
  close: "Lukk",
  selectLanguage: "Velg spr\xE5k",
  scrollBackToContent: "Skroll tilbake til innhold",
  zoomIn: "Zoom inn",
  zoomOut: "Zoom ut",
  resetZoom: "Nullstill zoom",
  menu: "Meny",
  done: "Ferdig",
  edit: "Rediger",
  undo: "Angre",
  redo: "Gj\xF8r om",
  resetLibrary: "Nullstill bibliotek",
  createNewRoom: "Opprett et nytt rom",
  fullScreen: "Fullskjerm",
  darkMode: "M\xF8rk modus",
  lightMode: "Lys modus",
  zenMode: "Zen-modus",
  objectsSnapMode: "",
  exitZenMode: "Avslutt zen-modus",
  cancel: "Avbryt",
  clear: "T\xF8m",
  remove: "Fjern",
  embed: "Sl\xE5 av/p\xE5 innebygging",
  publishLibrary: "Publiser",
  submit: "Send inn",
  confirm: "Bekreft",
  embeddableInteractionButton: "Klikk for \xE5 samhandle"
};
var alerts = {
  clearReset: "Dette vil t\xF8mme lerretet. Er du sikker?",
  couldNotCreateShareableLink: "Kunne ikke lage delbar lenke.",
  couldNotCreateShareableLinkTooBig: "Kunne ikke opprette lenke til deling: scenen er for stor",
  couldNotLoadInvalidFile: "Kunne ikke laste inn ugyldig fil",
  importBackendFailed: "Importering av backend feilet.",
  cannotExportEmptyCanvas: "Kan ikke eksportere et tomt lerret.",
  couldNotCopyToClipboard: "Kunne ikke kopiere til utklippstavlen.",
  decryptFailed: "Kunne ikke dekryptere data.",
  uploadedSecurly: "Opplastingen er kryptert og kan ikke leses av Excalidraw-serveren eller tredjeparter.",
  loadSceneOverridePrompt: "\xC5 laste inn ekstern tegning vil erstatte det eksisterende innholdet. \xD8nsker du \xE5 fortsette?",
  collabStopOverridePrompt: "Hvis du slutter \xF8kten, overskrives din forrige, lokalt lagrede tegning. Er du sikker?\n\n(Hvis du \xF8nsker \xE5 beholde din lokale tegning, bare lukk nettleserfanen i stedet.)",
  errorAddingToLibrary: "Kunne ikke legge element i biblioteket",
  errorRemovingFromLibrary: "Kunne ikke fjerne element fra biblioteket",
  confirmAddLibrary: "Dette vil legge til {{numShapes}} figur(er) i biblioteket ditt. Er du sikker?",
  imageDoesNotContainScene: "Det ser ikke ut til at dette bildet inneholder noen scenedata. Har du aktivert innebygging av scene under eksporten?",
  cannotRestoreFromImage: "Scenen kunne ikke gjenopprettes fra denne bildefilen",
  invalidSceneUrl: "Kunne ikke importere scene fra den oppgitte URL-en. Den er enten \xF8delagt, eller inneholder ikke gyldig Excalidraw JSON-data.",
  resetLibrary: "Dette vil t\xF8mme biblioteket ditt. Er du sikker?",
  removeItemsFromsLibrary: "Slett {{count}} element(er) fra biblioteket?",
  invalidEncryptionKey: "Krypteringsn\xF8kkel m\xE5 ha 22 tegn. Live-samarbeid er deaktivert.",
  collabOfflineWarning: "Ingen Internett-tilkobling tilgjengelig.\nEndringer dine vil ikke bli lagret!"
};
var errors = {
  unsupportedFileType: "Filtypen st\xF8ttes ikke.",
  imageInsertError: "Kunne ikke sette inn bildet. Pr\xF8v igjen senere...",
  fileTooBig: "Filen er for stor. Maksimal tillatt st\xF8rrelse er {{maxSize}}.",
  svgImageInsertError: "Kunne ikke sette inn SVG-bilde. SVG-koden ser ugyldig ut.",
  failedToFetchImage: "",
  invalidSVGString: "Ugyldig SVG.",
  cannotResolveCollabServer: "Kunne ikke koble til samarbeidsserveren. Vennligst oppdater siden og pr\xF8v p\xE5 nytt.",
  importLibraryError: "Kunne ikke laste bibliotek",
  collabSaveFailed: "Kan ikke lagre i backend-databasen. Hvis problemer vedvarer, b\xF8r du lagre filen lokalt for \xE5 sikre at du ikke mister arbeidet.",
  collabSaveFailed_sizeExceeded: "Kunne ikke lagre til backend-databasen, lerretet ser ut til \xE5 v\xE6re for stort. Du b\xF8r lagre filen lokalt for \xE5 sikre at du ikke mister arbeidet ditt.",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "Ser ut som om du bruker Brave nettleser med <bold>Aggressivt Block Finger</bold> -innstillingen aktivert.",
    line2: "Dette kan resultere i \xE5 bryte <bold>tekst-elementene</bold> i tegningene.",
    line3: "Vi anbefaler p\xE5 det sterkeste \xE5 deaktivere denne innstillingen. Du kan f\xF8lge <link>disse trinnene</link> om hvordan du gj\xF8r det.",
    line4: "Hvis deaktivering av denne innstillingen ikke fikser visningen av tekstelementer, vennligst \xE5pne en <issueLink>sak</issueLink> p\xE5 v\xE5r GitHub, eller skriv oss p\xE5 <discordLink>Discord</discordLink>"
  },
  libraryElementTypeError: {
    embeddable: "Innebygde elementer kan ikke legges til i biblioteket.",
    iframe: "",
    image: "St\xF8tte for \xE5 legge til bilder i biblioteket kommer snart!"
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "Velg",
  image: "Sett inn bilde",
  rectangle: "Rektangel",
  diamond: "Diamant",
  ellipse: "Ellipse",
  arrow: "Pil",
  line: "Linje",
  freedraw: "Tegn",
  text: "Tekst",
  library: "Bibliotek",
  lock: "Behold merket verkt\xF8y som aktivt",
  penMode: "Pennemodus - forhindre ber\xF8ring",
  link: "Legg til / oppdater link for en valgt figur",
  eraser: "Viskel\xE6r",
  frame: "Rammeverkt\xF8y",
  magicframe: "",
  embeddable: "Nettinnbygging",
  laser: "",
  hand: "H\xE5nd (panoreringsverkt\xF8y)",
  extraTools: "Flere verkt\xF8y",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "Handlinger: lerret",
  selectedShapeActions: "Handlinger: valgt objekt",
  shapes: "Former"
};
var hints = {
  canvasPanning: "For \xE5 flytte lerretet, hold musehjulet eller mellomromstasten mens du drar, eller bruk h\xE5nd-verkt\xF8yet",
  linearElement: "Klikk for \xE5 starte linje med flere punkter, eller dra for en enkel linje",
  freeDraw: "Klikk og dra, slipp n\xE5r du er ferdig",
  text: "Tips: du kan ogs\xE5 legge til tekst ved \xE5 dobbeltklikke hvor som helst med utvalgsverkt\xF8yet",
  embeddable: "Klikk og dra for \xE5 opprette en nettside innebygd",
  text_selected: "Dobbeltklikk eller trykk ENTER for \xE5 redigere tekst",
  text_editing: "Trykk Escape eller Ctrl/Cmd+Enter for \xE5 fullf\xF8re redigering",
  linearElementMulti: "Klikk p\xE5 siste punkt eller trykk Escape eller Enter for \xE5 fullf\xF8re",
  lockAngle: "Du kan l\xE5se vinkelen ved \xE5 holde nede SHIFT",
  resize: "Du kan beholde forholdet ved \xE5 trykke SHIFT mens du endrer st\xF8rrelse,\ntrykk ALT for \xE5 endre st\xF8rrelsen fra midten",
  resizeImage: "Du kan endre st\xF8rrelse fritt ved \xE5 holde SHIFT,\nhold ALT for \xE5 endre st\xF8rrelse fra midten",
  rotate: "Du kan l\xE5se vinklene ved \xE5 holde SHIFT mens du roterer",
  lineEditor_info: "Hold Ctrl/Cmd og dobbelklikk eller trykk Ctrl/Cmd + Enter for \xE5 endre punkter",
  lineEditor_pointSelected: "Trykk p\xE5 Slett for \xE5 fjerne punktet, Ctrl / Cmd+D for \xE5 duplisere, eller dra for \xE5 flytte",
  lineEditor_nothingSelected: "Velg et punkt \xE5 redigere (hold SHIFT for \xE5 velge flere),\neller hold Alt og klikk for \xE5 legge til nye punkter",
  placeImage: "Klikk for \xE5 plassere bildet, eller klikk og dra for \xE5 angi st\xF8rrelsen manuelt",
  publishLibrary: "Publiser ditt eget bibliotek",
  bindTextToElement: "Trykk Enter for \xE5 legge til tekst",
  deepBoxSelect: "Hold CTRL/CMD for \xE5 markere dypt og forhindre flytting",
  eraserRevert: "Hold Alt for \xE5 reversere elementene merket for sletting",
  firefox_clipboard_write: 'Denne funksjonen kan sannsynligvis aktiveres ved \xE5 sette "dom.events.asyncClipboard.clipboardItem" flagget til "true". For \xE5 endre nettleserens flagg i Firefox, bes\xF8k "about:config"-siden.',
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "Kan ikke vise forh\xE5ndsvisning",
  canvasTooBig: "Lerretet kan v\xE6re for stort.",
  canvasTooBigTip: "Tips: Pr\xF8v \xE5 flytte de ytterste elementene litt tettere sammen."
};
var errorSplash = {
  headingMain: "En feil oppsto. Pr\xF8v <button>\xE5 laste siden p\xE5 nytt.</button>",
  clearCanvasMessage: "Om ny sidelasting ikke fungerer, pr\xF8v <button>\xE5 t\xF8mme lerretet.</button>",
  clearCanvasCaveat: " Dette vil f\xF8re til tap av arbeid ",
  trackedToSentry: "Feilen med identifikator {{eventId}} ble logget i v\xE5rt system.",
  openIssueMessage: "Vi er veldig n\xF8ye med \xE5 ikke inkludere dine scene-opplysninger i feilen. Hvis din scene ikke er privat, vurder \xE5 f\xF8lge opp i v\xE5rt <button>feilrapporteringssystem.</button> Ta med opplysningene nedenfor ved \xE5 kopiere og lime inn i GitHub-saken.",
  sceneContent: "Scene-innhold:"
};
var roomDialog = {
  desc_intro: "Du kan invitere personer til scenen din for \xE5 samarbeide med deg.",
  desc_privacy: "Ta det med ro, sesjonen bruker ende-til-ende-kryptering, s\xE5 alt du tegner forblir privat. Ikke en gang serveren v\xE5r kan se hva du lager.",
  button_startSession: "Start \xF8kt",
  button_stopSession: "Stopp sesjon",
  desc_inProgressIntro: "Sanntids-samarbeids\xF8kt er n\xE5 i gang.",
  desc_shareLink: "Del denne linken med de du vil samarbeide med:",
  desc_exitSession: "Dersom du avslutter sesjonen blir du frakoblet rommet, men du kan fortsette \xE5 arbeide med scenen lokalt. V\xE6r oppmerksom p\xE5 at dette ikke vil p\xE5virke andre personer, og de vil fortsatt ha mulighet til \xE5 samarbeide p\xE5 deres versjon.",
  shareTitle: "Bli med i en live samarbeids\xF8kt p\xE5 Excalidraw"
};
var errorDialog = {
  title: "Feil"
};
var exportDialog = {
  disk_title: "Lagre til disk",
  disk_details: "Eksporter scene-dataene til en fil som du kan importere fra senere.",
  disk_button: "Lagre til fil",
  link_title: "Delbar lenke",
  link_details: "Eksporter som en skrivebeskyttet lenke.",
  link_button: "Eksporter til lenke",
  excalidrawplus_description: "Lagre scenen til ditt Excalidraw+ arbeidsomr\xE5de.",
  excalidrawplus_button: "Eksporter",
  excalidrawplus_exportError: "Kunne ikke eksportere til Excalidraw+ for \xF8yeblikket..."
};
var helpDialog = {
  blog: "Les bloggen v\xE5r",
  click: "klikk",
  deepSelect: "Marker dypt",
  deepBoxSelect: "Marker dypt innad i boks og forhindre flytting",
  curvedArrow: "Buet pil",
  curvedLine: "Buet linje",
  documentation: "Dokumentasjon",
  doubleClick: "dobbeltklikk",
  drag: "dra",
  editor: "Redigeringsvisning",
  editLineArrowPoints: "Rediger linje/pilpunkter",
  editText: "Rediger tekst / legg til etikett",
  github: "Funnet et problem? Send inn",
  howto: "F\xF8lg v\xE5re veiledninger",
  or: "eller",
  preventBinding: "Forhindre pilbinding",
  tools: "Verkt\xF8y",
  shortcuts: "Tastatursnarveier",
  textFinish: "Fullf\xF8r redigering (teksteditor)",
  textNewLine: "Legg til ny linje (teksteditor)",
  title: "Hjelp",
  view: "Vis",
  zoomToFit: "Zoom for \xE5 se alle elementer",
  zoomToSelection: "Zoom til utvalg",
  toggleElementLock: "L\xE5s/l\xE5s opp utvalg",
  movePageUpDown: "Flytt side opp/ned",
  movePageLeftRight: "Flytt siden til venstre/h\xF8yre"
};
var clearCanvasDialog = {
  title: "T\xF8m lerret"
};
var publishDialog = {
  title: "Publiser bibliotek",
  itemName: "Elementnavn",
  authorName: "Forfatterens navn",
  githubUsername: "GitHub-brukernavnet",
  twitterUsername: "Twitter-brukernavn",
  libraryName: "Biblioteknavn",
  libraryDesc: "Beskrivelse av bibliotek",
  website: "Nettsted",
  placeholder: {
    authorName: "Ditt navn eller brukernavn",
    libraryName: "Navnet p\xE5 biblioteket ditt",
    libraryDesc: "Beskrivelse av biblioteket ditt for \xE5 hjelpe folk med \xE5 forst\xE5 bruken",
    githubHandle: "Github-brukernavn (valgfritt), slik at du kan redigere biblioteket n\xE5r du har sendt inn for gjennomgang",
    twitterHandle: "Twitter-brukernavn (valgfritt), slik at vi vet hvem vi skal kreditere n\xE5r promotert p\xE5 Twitter",
    website: "Lenke til din personlige nettside eller et annet sted (valgfritt)"
  },
  errors: {
    required: "P\xE5krevd",
    website: "Angi en gyldig nettadresse"
  },
  noteDescription: "Send inn biblioteket ditt som skal inkluderes i <link>kildekode for offentlig bibliotek</link>for andre \xE5 bruke dem i tegninger.",
  noteGuidelines: "Biblioteket m\xE5 godkjennes manuelt f\xF8rst. Les <link>retningslinjene</link> f\xF8r innsending. Du vil trenge en GitHub-konto for \xE5 kommunisere og gj\xF8re endringer hvis \xF8nsket, men det er ikke p\xE5krevd.",
  noteLicense: "Ved \xE5 sende inn godtar du at biblioteket blir publisert under <link>MIT-lisens, </link>som kortfattet betyr at andre kan bruke dem uten begrensninger.",
  noteItems: "Hvert bibliotek m\xE5 ha sitt eget navn, s\xE5 det er filtrerbart. F\xF8lgende bibliotekselementer vil bli inkludert:",
  atleastOneLibItem: "Vennligst velg minst ett bibliotek for \xE5 komme i gang",
  republishWarning: "Merk: noen av de valgte elementene er merket som allerede publisert/sendt. Du b\xF8r kun sende inn elementer p\xE5 nytt n\xE5r du oppdaterer et eksisterende bibliotek eller innlevering."
};
var publishSuccessDialog = {
  title: "Bibliotek innsendt",
  content: "Takk {{authorName}}. Ditt bibliotek har blitt sendt inn for gjennomgang. Du kan spore statusen<link>her</link>"
};
var confirmDialog = {
  resetLibrary: "Nullstill bibliotek",
  removeItemsFromLib: "Fjern valgte elementer fra bibliotek"
};
var imageExportDialog = {
  header: "Eksporter bilde",
  label: {
    withBackground: "Bakgrunn",
    onlySelected: "Kun valgte",
    darkMode: "M\xF8rk modus",
    embedScene: "Bygg inn scene",
    scale: "Skalering",
    padding: "Avstand"
  },
  tooltip: {
    embedScene: "Scenedata vil bli lagret i den eksporterte PNG/SVG-filen, slik at scenen kan gjenopprettes fra den.\nDet vil \xF8ke den eksporterte filst\xF8rrelsen."
  },
  title: {
    exportToPng: "Eksporter til PNG",
    exportToSvg: "Eksporter til SVG",
    copyPngToClipboard: "Kopier PNG til utklippstavlen"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "Kopier til utklippstavle"
  }
};
var encrypted = {
  tooltip: "Dine tegninger er ende-til-ende-krypterte slik at Excalidraw sine servere aldri vil se dem.",
  link: "Blogginnlegg om ende-til-ende-kryptering i Excalidraw"
};
var stats = {
  angle: "Vinkel",
  element: "Element",
  elements: "Elementer",
  height: "H\xF8yde",
  scene: "Scene",
  selected: "Valgt",
  storage: "Lagring",
  title: "Statistikk for nerder",
  total: "Totalt",
  version: "Versjon",
  versionCopy: "Klikk for \xE5 kopiere",
  versionNotAvailable: "Versjon ikke tilgjengelig",
  width: "Bredde"
};
var toast = {
  addedToLibrary: "Lagt til i biblioteket",
  copyStyles: "Kopierte stiler.",
  copyToClipboard: "Kopiert til utklippstavlen.",
  copyToClipboardAsPng: "Kopierte {{exportSelection}} til utklippstavlen som PNG\n({{exportColorScheme}})",
  fileSaved: "Fil lagret.",
  fileSavedToFilename: "Lagret til {filename}",
  canvas: "lerret",
  selection: "utvalg",
  pasteAsSingleElement: "Bruk {{shortcut}} for \xE5 lime inn som ett enkelt element,\neller lim inn i en eksisterende tekstbehandler",
  unableToEmbed: "Innbygging av denne nettadressen er ikke tillatt. Oppret en sak p\xE5 GitHub for \xE5 be om url-hvitelisting",
  unrecognizedLinkFormat: 'Linken du bygget inn samsvarer ikke med det forventede formatet. Pr\xF8v \xE5 lime inn "bygg inn"-strengen fra kildesiden'
};
var colors = {
  transparent: "Gjennomsiktig",
  black: "Svart",
  white: "Hvit",
  red: "R\xF8d",
  pink: "Rosa",
  grape: "Drue",
  violet: "Fiolett",
  gray: "Gr\xE5",
  blue: "Bl\xE5",
  cyan: "Turkis",
  teal: "Bl\xE5gr\xF8nn",
  green: "Gr\xF8nn",
  yellow: "Gul",
  orange: "Oransje",
  bronze: "Bronse"
};
var welcomeScreen = {
  app: {
    center_heading: "Alle dine data lagres lokalt i din nettleser.",
    center_heading_plus: "\xD8nsker du \xE5 g\xE5 til Excalidraw+ i stedet?",
    menuHint: "Eksporter, innstillinger, spr\xE5k, ..."
  },
  defaults: {
    menuHint: "Eksporter, innstillinger og mer...",
    center_heading: "Diagrammer. Gjort. Enkelt.",
    toolbarHint: "Velg et verkt\xF8y og start \xE5 tegne!",
    helpHint: "Snarveier & hjelp"
  }
};
var colorPicker = {
  mostUsedCustomColors: "Mest brukte egendefinerte farger",
  colors: "Farger",
  shades: "Toner",
  hexCode: "Heksadesimal kode",
  noShades: "Ingen toner tilgjengelig for denne fargen"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Eksporter som bilde",
      button: "Eksporter som bilde",
      description: "Eksporter scene-dataene til en fil som du kan importere fra senere."
    },
    saveToDisk: {
      title: "Lagre til disk",
      button: "Lagre til disk",
      description: "Eksporter scene-dataene til en fil som du kan importere fra senere."
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "Eksporter til Excalidraw+",
      description: "Lagre scenen til ditt Excalidraw+-arbeidsomr\xE5de."
    }
  },
  modal: {
    loadFromFile: {
      title: "Last inn fra fil",
      button: "Last inn fra fil",
      description: "\xC5 laste fra en fil vil <bold>erstatte ditt eksisterende innhold</bold>.<br></br>Du kan sikkerhetskopiere tegningen din f\xF8rst ved \xE5 bruke en av valgene under."
    },
    shareableLink: {
      title: "Last inn fra lenke",
      button: "Erstatt innholdet mitt",
      description: "Lasting av ekstern tegning vil <bold>erstatte ditt eksisterende innhold</bold>.<br></br>Du kan sikkerhetskopiere tegningen din f\xF8rst ved \xE5 bruke en av valgene nedenfor."
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var nb_NO_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  nb_NO_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=nb-NO-BMB73KRH.js.map
