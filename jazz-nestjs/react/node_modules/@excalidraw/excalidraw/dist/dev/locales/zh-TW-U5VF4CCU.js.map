{"version": 3, "sources": ["../../../locales/zh-TW.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"貼上\",\n    \"pasteAsPlaintext\": \"以純文字貼上\",\n    \"pasteCharts\": \"貼上圖表\",\n    \"selectAll\": \"全選\",\n    \"multiSelect\": \"將物件加入選取範圍\",\n    \"moveCanvas\": \"移動畫布\",\n    \"cut\": \"剪下\",\n    \"copy\": \"複製\",\n    \"copyAsPng\": \"以PNG格式儲存到剪貼板\",\n    \"copyAsSvg\": \"以SVG格式複製到剪貼板\",\n    \"copyText\": \"以文字格式複製至剪貼簿\",\n    \"copySource\": \"複製來源至剪貼簿\",\n    \"convertToCode\": \"轉換為程式碼\",\n    \"bringForward\": \"上移一層\",\n    \"sendToBack\": \"移到最底層\",\n    \"bringToFront\": \"置於最頂層\",\n    \"sendBackward\": \"往後移一層\",\n    \"delete\": \"刪除\",\n    \"copyStyles\": \"複製樣式\",\n    \"pasteStyles\": \"貼上樣式\",\n    \"stroke\": \"筆畫\",\n    \"background\": \"背景\",\n    \"fill\": \"填滿\",\n    \"strokeWidth\": \"筆跡寬度\",\n    \"strokeStyle\": \"筆畫樣式\",\n    \"strokeStyle_solid\": \"實線\",\n    \"strokeStyle_dashed\": \"虛線\",\n    \"strokeStyle_dotted\": \"點線\",\n    \"sloppiness\": \"線條風格\",\n    \"opacity\": \"透明度\",\n    \"textAlign\": \"文字對齊\",\n    \"edges\": \"邊緣\",\n    \"sharp\": \"尖銳\",\n    \"round\": \"平滑\",\n    \"arrowheads\": \"箭頭\",\n    \"arrowhead_none\": \"無\",\n    \"arrowhead_arrow\": \"箭頭\",\n    \"arrowhead_bar\": \"條狀箭頭\",\n    \"arrowhead_circle\": \"圓形\",\n    \"arrowhead_circle_outline\": \"圓形（外框）\",\n    \"arrowhead_triangle\": \"三角形\",\n    \"arrowhead_triangle_outline\": \"三角形（外框）\",\n    \"arrowhead_diamond\": \"菱形\",\n    \"arrowhead_diamond_outline\": \"菱形（外框）\",\n    \"fontSize\": \"字型大小\",\n    \"fontFamily\": \"字體集\",\n    \"addWatermark\": \"加上 \\\"Made with Excalidraw\\\" 浮水印\",\n    \"handDrawn\": \"手寫\",\n    \"normal\": \"一般\",\n    \"code\": \"代碼\",\n    \"small\": \"小\",\n    \"medium\": \"中\",\n    \"large\": \"大\",\n    \"veryLarge\": \"特大\",\n    \"solid\": \"實心\",\n    \"hachure\": \"斜線筆觸\",\n    \"zigzag\": \"Ｚ字形\",\n    \"crossHatch\": \"交叉筆觸\",\n    \"thin\": \"細\",\n    \"bold\": \"粗\",\n    \"left\": \"左側\",\n    \"center\": \"置中\",\n    \"right\": \"右側\",\n    \"extraBold\": \"極粗\",\n    \"architect\": \"精確\",\n    \"artist\": \"藝術\",\n    \"cartoonist\": \"卡通\",\n    \"fileTitle\": \"檔案名稱\",\n    \"colorPicker\": \"色彩選擇工具\",\n    \"canvasColors\": \"使用於畫布\",\n    \"canvasBackground\": \"Canvas 背景\",\n    \"drawingCanvas\": \"繪圖 canvas\",\n    \"layers\": \"圖層\",\n    \"actions\": \"動作\",\n    \"language\": \"語言\",\n    \"liveCollaboration\": \"即時協作...\",\n    \"duplicateSelection\": \"複製\",\n    \"untitled\": \"無標題\",\n    \"name\": \"名稱\",\n    \"yourName\": \"你的名稱\",\n    \"madeWithExcalidraw\": \"以 Excalidraw 製作\",\n    \"group\": \"建立群組\",\n    \"ungroup\": \"取消群組\",\n    \"collaborators\": \"協作者\",\n    \"showGrid\": \"顯示格線\",\n    \"addToLibrary\": \"加入資料庫\",\n    \"removeFromLibrary\": \"從資料庫中移除\",\n    \"libraryLoadingMessage\": \"資料庫讀取中…\",\n    \"libraries\": \"瀏覽資料庫\",\n    \"loadingScene\": \"場景讀取中…\",\n    \"align\": \"對齊\",\n    \"alignTop\": \"對齊頂部\",\n    \"alignBottom\": \"對齊底部\",\n    \"alignLeft\": \"對齊左側\",\n    \"alignRight\": \"對齊右側\",\n    \"centerVertically\": \"垂直置中\",\n    \"centerHorizontally\": \"水平置中\",\n    \"distributeHorizontally\": \"水平分布\",\n    \"distributeVertically\": \"垂直分布\",\n    \"flipHorizontal\": \"水平翻轉\",\n    \"flipVertical\": \"垂直翻轉\",\n    \"viewMode\": \"檢視模式\",\n    \"share\": \"共享\",\n    \"showStroke\": \"顯示線條檢色器\",\n    \"showBackground\": \"顯示背景檢色器\",\n    \"toggleTheme\": \"切換主題\",\n    \"personalLib\": \"個人資料庫\",\n    \"excalidrawLib\": \"Excalidraw 資料庫\",\n    \"decreaseFontSize\": \"縮小文字\",\n    \"increaseFontSize\": \"放大文字\",\n    \"unbindText\": \"取消綁定文字\",\n    \"bindText\": \"結合文字至容器\",\n    \"createContainerFromText\": \"將文字包於容器中\",\n    \"link\": {\n      \"edit\": \"編輯連結\",\n      \"editEmbed\": \"編輯連結&嵌入\",\n      \"create\": \"建立連結\",\n      \"createEmbed\": \"建立連結&嵌入\",\n      \"label\": \"連結\",\n      \"labelEmbed\": \"連結&嵌入\",\n      \"empty\": \"未設定連結\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"編輯線條\",\n      \"exit\": \"結束線條編輯\"\n    },\n    \"elementLock\": {\n      \"lock\": \"鎖定\",\n      \"unlock\": \"解鎖\",\n      \"lockAll\": \"全部鎖定\",\n      \"unlockAll\": \"全部解鎖\"\n    },\n    \"statusPublished\": \"已發布\",\n    \"sidebarLock\": \"側欄維持開啟\",\n    \"selectAllElementsInFrame\": \"選取框架內的所有元素\",\n    \"removeAllElementsFromFrame\": \"從框架內移除所有元素\",\n    \"eyeDropper\": \"從畫布中選取顏色\",\n    \"textToDiagram\": \"文字轉圖表\",\n    \"prompt\": \"提示詞\"\n  },\n  \"library\": {\n    \"noItems\": \"尚未加入任何物件...\",\n    \"hint_emptyLibrary\": \"選取畫布上的物件以加入，或從下方的公開 repository 中安裝資料庫\",\n    \"hint_emptyPrivateLibrary\": \"選擇畫布上的物件以在此加入\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"重置 canvas\",\n    \"exportJSON\": \"匯出至檔案\",\n    \"exportImage\": \"匯出圖片\",\n    \"export\": \"儲存至...\",\n    \"copyToClipboard\": \"複製至剪貼簿\",\n    \"save\": \"儲存目前檔案\",\n    \"saveAs\": \"儲存為\",\n    \"load\": \"開啟\",\n    \"getShareableLink\": \"取得共享連結\",\n    \"close\": \"關閉\",\n    \"selectLanguage\": \"選擇語言\",\n    \"scrollBackToContent\": \"捲動回到內容\",\n    \"zoomIn\": \"放大\",\n    \"zoomOut\": \"縮小\",\n    \"resetZoom\": \"重設縮放\",\n    \"menu\": \"選單\",\n    \"done\": \"完成\",\n    \"edit\": \"編輯\",\n    \"undo\": \"復原\",\n    \"redo\": \"重做\",\n    \"resetLibrary\": \"重設資料庫\",\n    \"createNewRoom\": \"建立新協作會議室\",\n    \"fullScreen\": \"全螢幕\",\n    \"darkMode\": \"深色模式\",\n    \"lightMode\": \"淺色模式\",\n    \"zenMode\": \"專注模式\",\n    \"objectsSnapMode\": \"吸附至物件\",\n    \"exitZenMode\": \"離開專注模式\",\n    \"cancel\": \"取消\",\n    \"clear\": \"清除\",\n    \"remove\": \"刪除\",\n    \"embed\": \"切換嵌入\",\n    \"publishLibrary\": \"發布\",\n    \"submit\": \"送出\",\n    \"confirm\": \"確認\",\n    \"embeddableInteractionButton\": \"點擊以互動\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"這將會清除整個 canvas。你確定嗎？\",\n    \"couldNotCreateShareableLink\": \"無法建立共享連結。\",\n    \"couldNotCreateShareableLinkTooBig\": \"無法建立共享連結：場景太大\",\n    \"couldNotLoadInvalidFile\": \"無法讀取失效的檔案。\",\n    \"importBackendFailed\": \"後端讀取失敗。\",\n    \"cannotExportEmptyCanvas\": \"無法輸出空白的 canvas。\",\n    \"couldNotCopyToClipboard\": \"無法複製到剪貼簿\",\n    \"decryptFailed\": \"無法解密資料。\",\n    \"uploadedSecurly\": \"上傳已通過 end-to-end 加密，Excalidraw 伺服器和第三方無法皆讀取其內容。\",\n    \"loadSceneOverridePrompt\": \"讀取外部圖樣將取代目前的內容。是否要繼續？\",\n    \"collabStopOverridePrompt\": \"停止連線將覆蓋您先前於本機儲存的繪圖進度，是否確認？\\n\\n（如要保留原有的本機繪圖進度，直接關閉瀏覽器分頁即可。）\",\n    \"errorAddingToLibrary\": \"無法於此資料庫加入項目\",\n    \"errorRemovingFromLibrary\": \"無法由此資料庫移除項目\",\n    \"confirmAddLibrary\": \"這將會將 {{numShapes}} 個圖形加入你的資料庫，你確定嗎？\",\n    \"imageDoesNotContainScene\": \"此圖檔中未包含場景資料。輸出檔案時是否有包含場景資料？\",\n    \"cannotRestoreFromImage\": \"無法由此檔案回復場景。\",\n    \"invalidSceneUrl\": \"無法由提供的 URL 匯入場景。可能是發生異常，或未包含有效的 Excalidraw JSON 資料。\",\n    \"resetLibrary\": \"這會清除您的資料庫，是否確定？\",\n    \"removeItemsFromsLibrary\": \"從資料庫刪除 {{count}} 項？\",\n    \"invalidEncryptionKey\": \"加密鍵必須為22字元。即時協作已停用。\",\n    \"collabOfflineWarning\": \"沒有可用的網路連線。\\n變更無法儲存！\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"不支援的檔案類型。\",\n    \"imageInsertError\": \"無法插入圖片。請稍後再試…\",\n    \"fileTooBig\": \"檔案過大。可接受的最大尺寸為 {{maxSize}} 。\",\n    \"svgImageInsertError\": \"無法插入 SVG 圖片。此 SVG 檔案有問題。\",\n    \"failedToFetchImage\": \"無法獲取圖片。\",\n    \"invalidSVGString\": \"無效的 SVG。\",\n    \"cannotResolveCollabServer\": \"無法連結至 collab 伺服器。請重新整理後再試一次。\",\n    \"importLibraryError\": \"無法載入資料庫\",\n    \"collabSaveFailed\": \"無法儲存至後端資料庫。若此問題持續發生，請將檔案儲存於本機以確保資料不會遺失。\",\n    \"collabSaveFailed_sizeExceeded\": \"無法儲存至後端資料庫，可能的原因為畫布尺寸過大。請將檔案儲存於本機以確保資料不會遺失。\",\n    \"imageToolNotSupported\": \"圖片已停用\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"看起來您開啟了 Brave 瀏覽器的 <bold>Aggressively Block Fingerprinting</bold> 設定。\",\n      \"line2\": \"這可能造成您畫布中 <bold>文字元素</bold> 的異常。\",\n      \"line3\": \"我們強烈建議您關閉此設定。您可以依照 <link>這些步驟</link> 來進行。\",\n      \"line4\": \"若關閉此設定並未修復文字元素的顯示問題，請回報於我們 GitHub 上的 <issueLink>issue</issueLink>，或在 <discordLink>Discord</discordLink> 上告訴我們。\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"可嵌入元素無法加入資料庫\",\n      \"iframe\": \"IFrame 元素無法加入資料庫\",\n      \"image\": \"即將支援加入圖片至資料庫！\"\n    },\n    \"asyncPasteFailedOnRead\": \"無法貼上（無法由系統剪貼簿讀入）\",\n    \"asyncPasteFailedOnParse\": \"無法貼上\",\n    \"copyToSystemClipboardFailed\": \"無法複製至剪貼簿\"\n  },\n  \"toolBar\": {\n    \"selection\": \"選取\",\n    \"image\": \"插入圖片\",\n    \"rectangle\": \"長方形\",\n    \"diamond\": \"菱形\",\n    \"ellipse\": \"橢圓\",\n    \"arrow\": \"箭頭\",\n    \"line\": \"線條\",\n    \"freedraw\": \"繪圖\",\n    \"text\": \"文字\",\n    \"library\": \"資料庫\",\n    \"lock\": \"可連續使用選取的工具\",\n    \"penMode\": \"筆模式 - 避免觸摸\",\n    \"link\": \"為所選的形狀增加\\b/更新連結\",\n    \"eraser\": \"橡皮擦\",\n    \"frame\": \"框架工具\",\n    \"magicframe\": \"線框稿轉為程式碼\",\n    \"embeddable\": \"嵌入網站\",\n    \"laser\": \"雷射筆\",\n    \"hand\": \"手形（平移工具）\",\n    \"extraTools\": \"更多工具\",\n    \"mermaidToExcalidraw\": \"Mermaid 至 Excalidraw\",\n    \"magicSettings\": \"AI 設定\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"canvas 動作\",\n    \"selectedShapeActions\": \"選取圖形動作\",\n    \"shapes\": \"形狀\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"若要移動畫布，請在拖曳時按住滑鼠滾輪或空白鍵，或使用手形工具\",\n    \"linearElement\": \"點擊以繪製多點曲線；或拖曳以繪製直線\",\n    \"freeDraw\": \"點擊並拖曳來繪圖，放開即結束\",\n    \"text\": \"提示：亦可使用選取工具在任何地方雙擊來加入文字\",\n    \"embeddable\": \"點擊並拖移以建立嵌入網站\",\n    \"text_selected\": \"雙擊滑鼠或按 Enter 以編輯文字\",\n    \"text_editing\": \"按跳脫鍵或 Ctrl 或 Cmd + Enter 以結束編輯\",\n    \"linearElementMulti\": \"按下 Escape 或 Enter 以結束繪製\",\n    \"lockAngle\": \"按住 SHIFT 可限制旋轉角度\",\n    \"resize\": \"縮放時按住 Shift 可保持原比例縮放；\\\\n按住 Alt 可由中心點進行縮放\",\n    \"resizeImage\": \"按住 SHIFT 可任意縮放，按住 ALT 可由中央縮放。\",\n    \"rotate\": \"旋轉時按住 Shift 可限制旋轉角度\",\n    \"lineEditor_info\": \"按住 Ctrl 或 Cmd 並雙擊或按住 Ctrl 或 Cmd + Enter 來編輯控制點\",\n    \"lineEditor_pointSelected\": \"按下 Delete 可移除錨點；Ctrl 或 Cmd + D 可複製；或可拖曳來移動\",\n    \"lineEditor_nothingSelected\": \"選擇要編輯的錨點（按住 SHIFT 可多選），\\n或按住 Alt 並點擊以增加新錨點。\",\n    \"placeImage\": \"點擊以放置圖片，或點擊並拖曳以手動調整其尺寸。\",\n    \"publishLibrary\": \"發布個人資料庫\",\n    \"bindTextToElement\": \"按下 Enter 以加入文字。\",\n    \"deepBoxSelect\": \"按住 Ctrl 或 Cmd 以深度選取並避免拖曳\",\n    \"eraserRevert\": \"按住 Alt 以反選取已標記待刪除的元素\",\n    \"firefox_clipboard_write\": \"此功能有機會透過將 \\\"dom.events.asyncClipboard.clipboardItem\\\" 設定為 \\\"true\\\" 來開啟。\\n若要變更 Firefox 瀏覽器的此設定值，請至 \\\"about:config\\\" 頁面。\",\n    \"disableSnapping\": \"按住 Ctrl 或 Cmd 以禁用吸附\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"無法顯示預覽\",\n    \"canvasTooBig\": \"畫布可能過大\",\n    \"canvasTooBigTip\": \"提示：可嘗試將最遠的元素移動至較集中的位置\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"發生錯誤，嘗試<button>重新載入頁面。</button>\",\n    \"clearCanvasMessage\": \"若重新載入仍無法解決問題，嘗試<button>清除 canvas。</button>\",\n    \"clearCanvasCaveat\": \"此動作將造成目前的作品被移除。\",\n    \"trackedToSentry\": \"此錯誤與其識別碼{{eventId}}將由系統記錄。\",\n    \"openIssueMessage\": \"我們將謹慎處理，你的作品內容不會被包含在錯誤報告中。若你的作品不需保持私密，請考慮使用我們的<button>bug tracker。</button>請將下列資訊複製貼上至 GitHub issue 中。\",\n    \"sceneContent\": \"作品內容：\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"你可以邀請其他人一起協作目前的作品。\",\n    \"desc_privacy\": \"連線使用 end-to-end 加密故無須擔心作品的安全性。即使是我們的伺服器也無法取得其內容。\",\n    \"button_startSession\": \"開始連線\",\n    \"button_stopSession\": \"停止連線\",\n    \"desc_inProgressIntro\": \"即時協作連線正在進行中。\",\n    \"desc_shareLink\": \"將此連結分享給欲協作的對象：\",\n    \"desc_exitSession\": \"停止連線將中斷你與協作會議室的連結，但你仍可於本機編輯此作品。意指停止連線後你的編輯不會被先前共同協作的人看見，且他們可繼續共同協作另一個版本。\",\n    \"shareTitle\": \"加入 Excalidraw 上的即時協作會議室\"\n  },\n  \"errorDialog\": {\n    \"title\": \"錯誤\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"儲存至硬碟\",\n    \"disk_details\": \"將場景匯出為可供匯入之檔案\",\n    \"disk_button\": \"儲存至檔案\",\n    \"link_title\": \"可共享連結\",\n    \"link_details\": \"匯出為唯讀連結\",\n    \"link_button\": \"匯出為連結\",\n    \"excalidrawplus_description\": \"將此場景儲存至你的 Excalidraw+ 工作區\",\n    \"excalidrawplus_button\": \"輸出\",\n    \"excalidrawplus_exportError\": \"目前無法輸出至 Excalidraw+\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"閱讀部落格\",\n    \"click\": \"點擊\",\n    \"deepSelect\": \"深度選取\",\n    \"deepBoxSelect\": \"在容器內深度選取並避免拖曳\",\n    \"curvedArrow\": \"曲箭頭\",\n    \"curvedLine\": \"曲線\",\n    \"documentation\": \"文件\",\n    \"doubleClick\": \"雙擊\",\n    \"drag\": \"拖曳\",\n    \"editor\": \"編輯器\",\n    \"editLineArrowPoints\": \"編輯線/箭頭控制點\",\n    \"editText\": \"編輯文字/增加標籤\",\n    \"github\": \"發現異常？回報問題\",\n    \"howto\": \"參照我們的說明\",\n    \"or\": \"或\",\n    \"preventBinding\": \"避免箭號連結\",\n    \"tools\": \"工具\",\n    \"shortcuts\": \"鍵盤快速鍵\",\n    \"textFinish\": \"完成編輯（文字編輯器）\",\n    \"textNewLine\": \"換行（文字編輯器）\",\n    \"title\": \"說明\",\n    \"view\": \"檢視\",\n    \"zoomToFit\": \"放大至填滿畫面\",\n    \"zoomToSelection\": \"縮放至選取區\",\n    \"toggleElementLock\": \"鎖定/解鎖已選的項目\",\n    \"movePageUpDown\": \"向上/下移動頁面\",\n    \"movePageLeftRight\": \"向左/右移動頁面\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"清除畫布\"\n  },\n  \"publishDialog\": {\n    \"title\": \"發布資料庫\",\n    \"itemName\": \"項目名稱\",\n    \"authorName\": \"作者名稱\",\n    \"githubUsername\": \"GitHub 帳號\",\n    \"twitterUsername\": \"Twitter 帳號\",\n    \"libraryName\": \"資料庫名稱\",\n    \"libraryDesc\": \"資料庫說明\",\n    \"website\": \"網站\",\n    \"placeholder\": {\n      \"authorName\": \"您的名稱或帳號\",\n      \"libraryName\": \"您的資料庫名稱\",\n      \"libraryDesc\": \"提供您的資料庫說明以利他人理解其用途\",\n      \"githubHandle\": \"Github handle（選填），填寫後您可編輯已送出待審查的資料庫\",\n      \"twitterHandle\": \"Twitter 帳號（選填），填寫後若我們在 Twitter 推廣時可提及您\",\n      \"website\": \"您個人網站或其他網站的連結（選填）\"\n    },\n    \"errors\": {\n      \"required\": \"必填\",\n      \"website\": \"請輸入有效的 URL\"\n    },\n    \"noteDescription\": \"送出您的資料庫後將被包含於<link>公開資料庫 repository</link>以利他人在其繪圖中使用。\",\n    \"noteGuidelines\": \"資料庫需先經人工審查。請閱讀<link>說明文件</link>再送出。若需溝通與修改時要透過 GitHub 帳號來進行，但並非強制需求。\",\n    \"noteLicense\": \"送出即代表您同意此資料庫將發布時使用 <link>MIT 授權，</link>簡單來說是指任何人都能不受限制的使用。\",\n    \"noteItems\": \"每個資料庫項目都有獨立的名稱故可篩選。會包含下列資料庫項目：\",\n    \"atleastOneLibItem\": \"請選擇至少一項資料庫項目\",\n    \"republishWarning\": \"注意：部分選取中的物件先前已發布/送出過。建議僅在要更新現存資料庫或已送出的物件時才重新送出這些物件。\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"資料庫已送出\",\n    \"content\": \"感謝 {{authorName}} 。您的資料庫已送出待審查。您可查看目前狀態<link>在此</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"重設資料庫\",\n    \"removeItemsFromLib\": \"從資料庫移除所選的項目\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"匯出圖片\",\n    \"label\": {\n      \"withBackground\": \"背景\",\n      \"onlySelected\": \"僅選取物件\",\n      \"darkMode\": \"深色模式\",\n      \"embedScene\": \"嵌入場景\",\n      \"scale\": \"縮放比例\",\n      \"padding\": \"內間距\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"用於回復場景的場景資料會被包含在輸出的 PNG/SVG 檔案中。\\n會增加輸出的檔案大小。\"\n    },\n    \"title\": {\n      \"exportToPng\": \"輸出成 PNG\",\n      \"exportToSvg\": \"輸出成 SVG\",\n      \"copyPngToClipboard\": \"複製 PNG 至剪貼簿\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"複製至剪貼簿\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"你的作品已使用 end-to-end 方式加密，Excalidraw 的伺服器也無法取得其內容。\",\n    \"link\": \"Excalidraw 端到端加密的相關部落格文章\"\n  },\n  \"stats\": {\n    \"angle\": \"角度\",\n    \"element\": \"元素\",\n    \"elements\": \"元素\",\n    \"height\": \"高度\",\n    \"scene\": \"場景\",\n    \"selected\": \"已選\",\n    \"storage\": \"儲存\",\n    \"title\": \"詳細統計\",\n    \"total\": \"合計\",\n    \"version\": \"版本\",\n    \"versionCopy\": \"點擊複製\",\n    \"versionNotAvailable\": \"無法取得版本\",\n    \"width\": \"寬度\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"加入資料庫\",\n    \"copyStyles\": \"已複製樣式\",\n    \"copyToClipboard\": \"複製至剪貼簿。\",\n    \"copyToClipboardAsPng\": \"以 PNG 格式將 {{exportSelection}} 複製至剪貼簿\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"已儲存檔案。\",\n    \"fileSavedToFilename\": \"儲存為 {filename}\",\n    \"canvas\": \"畫布\",\n    \"selection\": \"已選項目\",\n    \"pasteAsSingleElement\": \"使用 {{shortcut}} 以做為單一物件貼上，\\n或貼上至現有的文字編輯器\",\n    \"unableToEmbed\": \"目前不允許嵌入此網址。您可至 GitHub 提出 issue 以要求將此網址加入合格名單。\",\n    \"unrecognizedLinkFormat\": \"您嵌入的連結格式不符。請嘗試貼入原網站所提供的「嵌入」字串。\"\n  },\n  \"colors\": {\n    \"transparent\": \"透明\",\n    \"black\": \"黑\",\n    \"white\": \"白\",\n    \"red\": \"紅\",\n    \"pink\": \"粉紅\",\n    \"grape\": \"深紫\",\n    \"violet\": \"藍紫\",\n    \"gray\": \"灰\",\n    \"blue\": \"藍\",\n    \"cyan\": \"青\",\n    \"teal\": \"藍綠\",\n    \"green\": \"綠\",\n    \"yellow\": \"黃\",\n    \"orange\": \"橘\",\n    \"bronze\": \"銅\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"所有資料皆已在瀏覽器中儲存於本機\",\n      \"center_heading_plus\": \"您是否是要前往 Excalidraw+ ？\",\n      \"menuHint\": \"輸出、偏好設定、語言...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"輸出、偏好設定及其他...\",\n      \"center_heading\": \"圖表。製作。超簡單。\",\n      \"toolbarHint\": \"選個工具開始畫圖吧！\",\n      \"helpHint\": \"快速鍵與說明\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"最常使用的自訂顏色\",\n    \"colors\": \"顏色\",\n    \"shades\": \"漸變色\",\n    \"hexCode\": \"Hex 碼\",\n    \"noShades\": \"沒有此顏色的漸變色\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"匯出為圖片\",\n        \"button\": \"匯出為圖片\",\n        \"description\": \"將場景匯出為可供匯入的圖片檔案\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"儲存至硬碟\",\n        \"button\": \"儲存至硬碟\",\n        \"description\": \"將場景匯出為可供匯入的檔案\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"匯出至 Excalidraw+\",\n        \"description\": \"將此場景儲存至您的 Excalidraw+ 工作區\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"從檔案載入\",\n        \"button\": \"從檔案載入\",\n        \"description\": \"從檔案載入將<bold>取代您目前的內容</bold>。<br></br>可先使用下方的選項備份您的繪圖。\"\n      },\n      \"shareableLink\": {\n        \"title\": \"從連結載入\",\n        \"button\": \"取代我的內容\",\n        \"description\": \"載入外部繪圖將<bold>取代您目前的內容</bold>。<br></br>可先使用下方的選項備份您的繪圖。\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Mermaid 至 Excalidraw\",\n    \"button\": \"插入\",\n    \"description\": \"目前僅支援 <flowchartLink>Flowchart</flowchartLink> 、 <sequenceLink>Sequence</sequenceLink> 及 <classLink>Class </classLink> 圖表。其餘檔案類型在 Excalidraw 將會以圖像呈現。\",\n    \"syntax\": \"Mermaid 語法\",\n    \"preview\": \"預覽\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}