{"version": 3, "sources": ["../../../locales/ro-RO.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"Lipire\",\n    \"pasteAsPlaintext\": \"Inserare ca text simplu\",\n    \"pasteCharts\": \"Lipire diagrame\",\n    \"selectAll\": \"Selectare totală\",\n    \"multiSelect\": \"Adaugă element la selecție\",\n    \"moveCanvas\": \"<PERSON><PERSON>e pânză\",\n    \"cut\": \"Decupare\",\n    \"copy\": \"Copiere\",\n    \"copyAsPng\": \"Copiere în memoria temporară ca PNG\",\n    \"copyAsSvg\": \"Copiere în memoria temporară ca SVG\",\n    \"copyText\": \"Copiere în memoria temporară ca text\",\n    \"copySource\": \"Copiere sursă în memoria temporară\",\n    \"convertToCode\": \"Convertire în cod\",\n    \"bringForward\": \"Aducere în plan apropiat\",\n    \"sendToBack\": \"Trimitere în ultimul plan\",\n    \"bringToFront\": \"Aducere în prim plan\",\n    \"sendBackward\": \"Trimitere în plan secundar\",\n    \"delete\": \"Ștergere\",\n    \"copyStyles\": \"Copiere stiluri\",\n    \"pasteStyles\": \"Lipire stiluri\",\n    \"stroke\": \"Contur\",\n    \"background\": \"Fundal\",\n    \"fill\": \"Umplere\",\n    \"strokeWidth\": \"Lățimea conturului\",\n    \"strokeStyle\": \"Stilul conturului\",\n    \"strokeStyle_solid\": \"Neîntrerupt\",\n    \"strokeStyle_dashed\": \"Liniuțe\",\n    \"strokeStyle_dotted\": \"Punctat\",\n    \"sloppiness\": \"Aspectul trasării\",\n    \"opacity\": \"Opacitate\",\n    \"textAlign\": \"Alinierea textului\",\n    \"edges\": \"Margini\",\n    \"sharp\": \"Ascuțite\",\n    \"round\": \"Rotunde\",\n    \"arrowheads\": \"Vârfuri de săgeată\",\n    \"arrowhead_none\": \"Niciunul\",\n    \"arrowhead_arrow\": \"Săgeată\",\n    \"arrowhead_bar\": \"Bară\",\n    \"arrowhead_circle\": \"Cerc\",\n    \"arrowhead_circle_outline\": \"Cerc (contur)\",\n    \"arrowhead_triangle\": \"Triunghi\",\n    \"arrowhead_triangle_outline\": \"Triunghi (contur)\",\n    \"arrowhead_diamond\": \"Romb\",\n    \"arrowhead_diamond_outline\": \"Romb (contur)\",\n    \"fontSize\": \"Dimensiune font\",\n    \"fontFamily\": \"Familia de fonturi\",\n    \"addWatermark\": \"Adaugă „Realizat cu Excalidraw”\",\n    \"handDrawn\": \"Scris de mână\",\n    \"normal\": \"Normal\",\n    \"code\": \"Cod\",\n    \"small\": \"Mică\",\n    \"medium\": \"Medie\",\n    \"large\": \"Mare\",\n    \"veryLarge\": \"Foarte mare\",\n    \"solid\": \"Plină\",\n    \"hachure\": \"Hașură\",\n    \"zigzag\": \"Zigzag\",\n    \"crossHatch\": \"Hașură transversală\",\n    \"thin\": \"Subțire\",\n    \"bold\": \"Îngroșată\",\n    \"left\": \"Stânga\",\n    \"center\": \"Centru\",\n    \"right\": \"Dreapta\",\n    \"extraBold\": \"Extra îngroșată\",\n    \"architect\": \"Arhitect\",\n    \"artist\": \"Artist\",\n    \"cartoonist\": \"Caricaturist\",\n    \"fileTitle\": \"Nume de fișier\",\n    \"colorPicker\": \"Selector de culoare\",\n    \"canvasColors\": \"Folosite pe pânză\",\n    \"canvasBackground\": \"Fundalul pânzei\",\n    \"drawingCanvas\": \"Pânză pentru desenat\",\n    \"layers\": \"Straturi\",\n    \"actions\": \"Acțiuni\",\n    \"language\": \"Limbă\",\n    \"liveCollaboration\": \"Colaborare în direct...\",\n    \"duplicateSelection\": \"Duplicare\",\n    \"untitled\": \"Nedenumit\",\n    \"name\": \"Nume\",\n    \"yourName\": \"Numele tău\",\n    \"madeWithExcalidraw\": \"Realizat cu Excalidraw\",\n    \"group\": \"Grupare selecție\",\n    \"ungroup\": \"Degrupare selecție\",\n    \"collaborators\": \"Colaboratori\",\n    \"showGrid\": \"Afișare grilă\",\n    \"addToLibrary\": \"Adăugare la bibliotecă\",\n    \"removeFromLibrary\": \"Eliminare din bibliotecă\",\n    \"libraryLoadingMessage\": \"Se încarcă biblioteca…\",\n    \"libraries\": \"Răsfoiește bibliotecile\",\n    \"loadingScene\": \"Se încarcă scena…\",\n    \"align\": \"Aliniere\",\n    \"alignTop\": \"Aliniere sus\",\n    \"alignBottom\": \"Aliniere jos\",\n    \"alignLeft\": \"Aliniere la stânga\",\n    \"alignRight\": \"Aliniere la dreapta\",\n    \"centerVertically\": \"Centrare verticală\",\n    \"centerHorizontally\": \"Centrare orizontală\",\n    \"distributeHorizontally\": \"Distribuie orizontal\",\n    \"distributeVertically\": \"Distribuie vertical\",\n    \"flipHorizontal\": \"Răsturnare orizontală\",\n    \"flipVertical\": \"Răsturnare verticală\",\n    \"viewMode\": \"Mod de vizualizare\",\n    \"share\": \"Distribuie\",\n    \"showStroke\": \"Afișare selector culoare contur\",\n    \"showBackground\": \"Afișare selector culoare fundal\",\n    \"toggleTheme\": \"Comutare temă\",\n    \"personalLib\": \"Biblioteca personală\",\n    \"excalidrawLib\": \"Biblioteca Excalidraw\",\n    \"decreaseFontSize\": \"Micșorează dimensiunea fontului\",\n    \"increaseFontSize\": \"Mărește dimensiunea fontului\",\n    \"unbindText\": \"Deconectare text\",\n    \"bindText\": \"Legare text de container\",\n    \"createContainerFromText\": \"Încadrare text într-un container\",\n    \"link\": {\n      \"edit\": \"Editare URL\",\n      \"editEmbed\": \"Editare URL și încorporare\",\n      \"create\": \"Creare URL\",\n      \"createEmbed\": \"Creare URL și încorporare\",\n      \"label\": \"URL\",\n      \"labelEmbed\": \"URL și încorporare\",\n      \"empty\": \"Nu este setat niciun URL\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Editare linie\",\n      \"exit\": \"Părăsire editor de linii\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Blocare\",\n      \"unlock\": \"Deblocare\",\n      \"lockAll\": \"Blocare toate\",\n      \"unlockAll\": \"Deblocare toate\"\n    },\n    \"statusPublished\": \"Publicat\",\n    \"sidebarLock\": \"Păstrează deschisă bara laterală\",\n    \"selectAllElementsInFrame\": \"\",\n    \"removeAllElementsFromFrame\": \"\",\n    \"eyeDropper\": \"Alegere culoare din pânză\",\n    \"textToDiagram\": \"Text la diagramă\",\n    \"prompt\": \"Solicitare\"\n  },\n  \"library\": {\n    \"noItems\": \"Niciun element adăugat încă...\",\n    \"hint_emptyLibrary\": \"Selectează un element de pe pânză pentru a-l adăuga aici sau instalează o bibliotecă din depozitul public, de mai jos.\",\n    \"hint_emptyPrivateLibrary\": \"Selectează un element de pe pânză pentru a-l adăuga aici.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Resetare pânză\",\n    \"exportJSON\": \"Exportare la fișiere\",\n    \"exportImage\": \"Exportare imagine...\",\n    \"export\": \"Salvare în...\",\n    \"copyToClipboard\": \"Copiere în memoria temporară\",\n    \"save\": \"Salvare în fișierul curent\",\n    \"saveAs\": \"Salvare ca\",\n    \"load\": \"Deschidere\",\n    \"getShareableLink\": \"Obține URL partajabil\",\n    \"close\": \"Închidere\",\n    \"selectLanguage\": \"Selectare limbă\",\n    \"scrollBackToContent\": \"Derulare înapoi la conținut\",\n    \"zoomIn\": \"Apropiere\",\n    \"zoomOut\": \"Depărtare\",\n    \"resetZoom\": \"Resetare transfocare\",\n    \"menu\": \"Meniu\",\n    \"done\": \"Efectuat\",\n    \"edit\": \"Edit\",\n    \"undo\": \"Anulare\",\n    \"redo\": \"Refacere\",\n    \"resetLibrary\": \"Resetare bibliotecă\",\n    \"createNewRoom\": \"Creare cameră nouă\",\n    \"fullScreen\": \"Ecran complet\",\n    \"darkMode\": \"Mod întunecat\",\n    \"lightMode\": \"Mod luminos\",\n    \"zenMode\": \"Mod zen\",\n    \"objectsSnapMode\": \"Ancorare la obiecte\",\n    \"exitZenMode\": \"Ieșire din modul zen\",\n    \"cancel\": \"Anulare\",\n    \"clear\": \"Ștergere\",\n    \"remove\": \"Eliminare\",\n    \"embed\": \"Comutare încorporare\",\n    \"publishLibrary\": \"Publicare\",\n    \"submit\": \"Trimitere\",\n    \"confirm\": \"Confirmare\",\n    \"embeddableInteractionButton\": \"Clic pentru interacționare\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Această opțiune va șterge întreaga pânză. Confirmi?\",\n    \"couldNotCreateShareableLink\": \"Nu s-a putut crea un URL partajabil.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Nu s-a putut crea un URL partajabil: scena este prea mare\",\n    \"couldNotLoadInvalidFile\": \"Fișierul invalid nu a putut fi încărcat\",\n    \"importBackendFailed\": \"Importarea de la nivel de server a eșuat.\",\n    \"cannotExportEmptyCanvas\": \"Nu se poate exporta pânza goală.\",\n    \"couldNotCopyToClipboard\": \"Nu s-a putut copia în memoria temporară.\",\n    \"decryptFailed\": \"Datele nu au putut fi decriptate.\",\n    \"uploadedSecurly\": \"Încărcarea a fost securizată prin criptare integrală, însemnând că serverul Excalidraw și terții nu pot citi conținutul.\",\n    \"loadSceneOverridePrompt\": \"Încărcarea desenului extern va înlocui conținutul existent. Dorești să continui?\",\n    \"collabStopOverridePrompt\": \"Oprirea sesiunii va suprascrie desenul anterior stocat local. Confirmi alegerea?\\n\\n(Dacă vrei să păstrezi desenul local, pur și simplu închide fila navigatorului în schimb.)\",\n    \"errorAddingToLibrary\": \"Elementul nu a putut fi adăugat în bibliotecă\",\n    \"errorRemovingFromLibrary\": \"Elementul nu a putut fi eliminat din bibliotecă\",\n    \"confirmAddLibrary\": \"Această acțiune va adăuga {{numShapes}} formă(e) la biblioteca ta. Confirmi?\",\n    \"imageDoesNotContainScene\": \"Această imagine nu pare să conțină date de scenă. Ai activat încorporarea scenei în timpul exportului?\",\n    \"cannotRestoreFromImage\": \"Scena nu a putut fi restaurată din acest fișier de imagine\",\n    \"invalidSceneUrl\": \"Scena nu a putut fi importată din URL-ul furnizat. Este fie incorect formată, fie nu conține date JSON Excalidraw valide.\",\n    \"resetLibrary\": \"Această opțiune va elimina conținutul din bibliotecă. Confirmi?\",\n    \"removeItemsFromsLibrary\": \"Ștergi {{count}} element(e) din bibliotecă?\",\n    \"invalidEncryptionKey\": \"Cheia de criptare trebuie să aibă 22 de caractere. Colaborarea în direct este dezactivată.\",\n    \"collabOfflineWarning\": \"Nu este disponibilă nicio conexiune la internet.\\nModificările nu vor fi salvate!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Tip de fișier neacceptat.\",\n    \"imageInsertError\": \"Imaginea nu a putut fi introdusă. Reîncearcă mai târziu...\",\n    \"fileTooBig\": \"Fișierul este prea mare. Dimensiunea maximă permisă este de {{maxSize}}.\",\n    \"svgImageInsertError\": \"Imaginea SVG nu a putut fi introdus. Marcajul SVG pare invalid.\",\n    \"failedToFetchImage\": \"Preluarea imaginii a eșuat.\",\n    \"invalidSVGString\": \"SVG invalid.\",\n    \"cannotResolveCollabServer\": \"Nu a putut fi realizată conexiunea la serverul de colaborare. Reîncarcă pagina și încearcă din nou.\",\n    \"importLibraryError\": \"Biblioteca nu a putut fi încărcată\",\n    \"collabSaveFailed\": \"Nu s-a putut salva în baza de date la nivel de server. Dacă problemele persistă, ar trebui să salvezi fișierul la nivel local pentru a te asigura că nu îți pierzi munca.\",\n    \"collabSaveFailed_sizeExceeded\": \"Nu s-a putut salva în baza de date la nivel de server, întrucât se pare că pânza este prea mare. Ar trebui să salvezi fișierul la nivel local pentru a te asigura că nu îți pierzi munca.\",\n    \"imageToolNotSupported\": \"Imaginile sunt dezactivate.\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"Se pare că folosești navigatorul Brave cu opțiunea <bold>strictă pentru blocarea amprentării</bold>.\",\n      \"line2\": \"Acest lucru poate duce la întreruperea <bold>elementelor text</bold> din desene.\",\n      \"line3\": \"Îți recomandăm ferm să dezactivezi această setare. Poți urma <link>acești pași</link> pentru a face acest lucru.\",\n      \"line4\": \"Dacă dezactivarea acestei setări nu duce la remedierea afișării elementelor text, deschide un tichet de <issueLink>problemă</issueLink> pe pagina noastră de GitHub sau scrie-ne pe <discordLink>Discord</discordLink>\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"Elementele încorporabile nu pot fi adăugate la bibliotecă.\",\n      \"iframe\": \"Elementele iFrame nu pot fi adăugate la bibliotecă.\",\n      \"image\": \"În curând vor putea fi adăugate imagini în bibliotecă!\"\n    },\n    \"asyncPasteFailedOnRead\": \"Lipirea nu a putut fi efectuată (nu s-a putut citit din memoria temporară a sistemului).\",\n    \"asyncPasteFailedOnParse\": \"Lipirea nu a putut fi efectuată.\",\n    \"copyToSystemClipboardFailed\": \"Nu s-a putut copia în memoria temporară.\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Selecție\",\n    \"image\": \"Introducere imagine\",\n    \"rectangle\": \"Dreptunghi\",\n    \"diamond\": \"Romb\",\n    \"ellipse\": \"Elipsă\",\n    \"arrow\": \"Săgeată\",\n    \"line\": \"Linie\",\n    \"freedraw\": \"Desenare\",\n    \"text\": \"Text\",\n    \"library\": \"Bibliotecă\",\n    \"lock\": \"Menține activ instrumentul selectat după desenare\",\n    \"penMode\": \"Mod stilou – împiedică atingerea\",\n    \"link\": \"Adăugare/actualizare URL pentru forma selectată\",\n    \"eraser\": \"Radieră\",\n    \"frame\": \"\",\n    \"magicframe\": \"Structură-de-fire la cod\",\n    \"embeddable\": \"Încorporare web\",\n    \"laser\": \"Indicator laser\",\n    \"hand\": \"Mână (instrument de panoramare)\",\n    \"extraTools\": \"\",\n    \"mermaidToExcalidraw\": \"Mermaid la Excalidraw\",\n    \"magicSettings\": \"Setări IA\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Acțiuni pentru pânză\",\n    \"selectedShapeActions\": \"Acțiuni pentru forma selectată\",\n    \"shapes\": \"Forme\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"Pentru a muta pânză, ține apăsată rotița mausului sau bara de spațiu sau folosește instrumentul în formă de mână\",\n    \"linearElement\": \"Dă clic pentru a crea mai multe puncte, glisează pentru a forma o singură linie\",\n    \"freeDraw\": \"Dă clic pe pânză și glisează cursorul, apoi eliberează-l când ai terminat\",\n    \"text\": \"Sfat: poți adăuga text și dând dublu clic oriunde cu instrumentul de selecție\",\n    \"embeddable\": \"Dă clic și trage pentru a crea un cod de încorporare de pagină web\",\n    \"text_selected\": \"Dă dublu clic sau apasă tasta Enter pentru a edita textul\",\n    \"text_editing\": \"Apasă tasta Escape sau Ctrl sau Cmd + Enter pentru a finaliza editarea\",\n    \"linearElementMulti\": \"Dă clic pe ultimul punct sau apasă tasta Escape sau tasta Enter pentru a termina\",\n    \"lockAngle\": \"Poți constrânge unghiul prin ținerea apăsată a tastei SHIFT\",\n    \"resize\": \"Poți constrânge proporțiile, ținând apăsată tasta SHIFT în timp ce redimensionezi,\\nține apăsată tasta ALT pentru a redimensiona de la centru\",\n    \"resizeImage\": \"Poți redimensiona liber ținând apăsată tasta SHIFT,\\nține apăsată tasta ALT pentru a redimensiona din centru\",\n    \"rotate\": \"Poți constrânge unghiurile, ținând apăsată tasta SHIFT în timp ce rotești\",\n    \"lineEditor_info\": \"Ține apăsată tasta Ctrl sau Cmd și dă dublu clic sau apasă tasta Ctrl sau Cmd + Enter pentru a edita puncte\",\n    \"lineEditor_pointSelected\": \"Apasă tasta Delete pentru a elimina punctele,\\ncombinația de taste Ctrl sau Cmd + D pentru a le duplica sau glisează-le pentru a le schimba poziția\",\n    \"lineEditor_nothingSelected\": \"Selectează un punct pentru a-l edita (ține apăsată tasta SHIFT pentru a selecta mai multe),\\nsau ține apăsată tasta Alt și dă clic pentru a adăuga puncte noi\",\n    \"placeImage\": \"Dă clic pentru a poziționa imaginea sau dă clic și glisează pentru a seta manual dimensiunea imaginii\",\n    \"publishLibrary\": \"Publică propria bibliotecă\",\n    \"bindTextToElement\": \"Apasă tasta Enter pentru a adăuga text\",\n    \"deepBoxSelect\": \"Ține apăsată tasta Ctrl sau Cmd pentru a efectua selectarea de adâncime și pentru a preveni glisarea\",\n    \"eraserRevert\": \"Ține apăsată tasta Alt pentru a anula elementele marcate pentru ștergere\",\n    \"firefox_clipboard_write\": \"Această caracteristică poate fi probabil activată prin setarea preferinței „dom.events.asyncClipboard.clipboardItem” ca „true”. Pentru a schimba preferințele navigatorului în Firefox, accesează pagina „about:config”.\",\n    \"disableSnapping\": \"Ține apăsat CtrlOrCmd pentru a dezactiva ancorarea\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Nu se poate afișa previzualizarea\",\n    \"canvasTooBig\": \"Pânza poate fi prea mare.\",\n    \"canvasTooBigTip\": \"Sfat: încearcă să apropii puțin mai mult elementele cele mai îndepărtate.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"A apărut o eroare. Încearcă <button>să reîncarci pagina</button>.\",\n    \"clearCanvasMessage\": \"Dacă reîncărcarea nu funcționează, încearcă <button>să ștergi pânza</button>.\",\n    \"clearCanvasCaveat\": \" Acest lucru va duce la pierderea progresului \",\n    \"trackedToSentry\": \"Eroarea cu identificatorul {{eventId}} a fost urmărită în sistemul nostru.\",\n    \"openIssueMessage\": \"Am luat măsuri de precauție pentru a nu include informații despre scenă în eroare. Dacă scena nu este privată, oferă-ne mai multe informații în <button>monitorul nostru pentru erori</button>. Include informațiile de mai jos copiindu-le și lipindu-le în tichetul cu problemă de pe GitHub.\",\n    \"sceneContent\": \"Conținutul scenei:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Poți invita alte persoane pentru a colabora la scena actuală.\",\n    \"desc_privacy\": \"Nu te îngrijora. Sesiunea utilizează criptarea integrală, astfel încât orice desenezi va rămâne privat. Nici măcar serverul nostru nu va putea vedea pe ce ai lucrat.\",\n    \"button_startSession\": \"Pornire sesiune\",\n    \"button_stopSession\": \"Oprire sesiune\",\n    \"desc_inProgressIntro\": \"Sesiunea de colaborare în direct este în curs de desfășurare.\",\n    \"desc_shareLink\": \"Distribuie acest URL persoanelor cu care dorești să colaborezi:\",\n    \"desc_exitSession\": \"Oprirea sesiunii te va deconecta de la sală, însă vei putea lucra în continuare, pe plan local, cu scena. Reține că această opțiune nu va afecta alte persoane, iar acestea vor putea să colaboreze în continuare pe versiunea lor.\",\n    \"shareTitle\": \"Alătură-te unei sesiuni de colaborare în direct pe Excalidraw\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Eroare\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Salvare pe disc\",\n    \"disk_details\": \"Exportă datele scenei pe un fișier din care poți importa mai târziu.\",\n    \"disk_button\": \"Salvare în fișier\",\n    \"link_title\": \"URL partajabil\",\n    \"link_details\": \"Exportă ca URL doar în citire.\",\n    \"link_button\": \"Exportare în URL\",\n    \"excalidrawplus_description\": \"Salvează scena în spațiul de lucru Excalidraw+.\",\n    \"excalidrawplus_button\": \"Exportare\",\n    \"excalidrawplus_exportError\": \"Excalidraw+ nu a putut fi exportat în acest moment...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Citește blogul nostru\",\n    \"click\": \"clic\",\n    \"deepSelect\": \"Selectare de adâncime\",\n    \"deepBoxSelect\": \"Selectare de adâncime în casetă și prevenire glisare\",\n    \"curvedArrow\": \"Săgeată curbată\",\n    \"curvedLine\": \"Linie curbată\",\n    \"documentation\": \"Documentație\",\n    \"doubleClick\": \"dublu clic\",\n    \"drag\": \"glisare\",\n    \"editor\": \"Editor\",\n    \"editLineArrowPoints\": \"Editare puncte de săgeată/rând\",\n    \"editText\": \"Editare text/adăugare etichetă\",\n    \"github\": \"Ai întâmpinat o problemă? Trimite un raport\",\n    \"howto\": \"Urmărește ghidurile noastre\",\n    \"or\": \"sau\",\n    \"preventBinding\": \"Împiedică legarea săgeții\",\n    \"tools\": \"Instrumente\",\n    \"shortcuts\": \"Comenzi rapide de la tastatură\",\n    \"textFinish\": \"Finalizează editarea (editor de text)\",\n    \"textNewLine\": \"Adaugă o linie nouă (editor de text)\",\n    \"title\": \"Ajutor\",\n    \"view\": \"Vizualizare\",\n    \"zoomToFit\": \"Transfocare pentru a cuprinde totul\",\n    \"zoomToSelection\": \"Transfocare la selecție\",\n    \"toggleElementLock\": \"Blocare/deblocare selecție\",\n    \"movePageUpDown\": \"Deplasare pagină sus/jos\",\n    \"movePageLeftRight\": \"Deplasare pagină stânga/dreapta\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Ștergere pânză\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Publicare bibliotecă\",\n    \"itemName\": \"Denumirea elementului\",\n    \"authorName\": \"Numele autorului\",\n    \"githubUsername\": \"Numele de utilizator GitHub\",\n    \"twitterUsername\": \"Numele de utilizator Twitter\",\n    \"libraryName\": \"Denumirea bibliotecii\",\n    \"libraryDesc\": \"Descrierea bibliotecii\",\n    \"website\": \"Pagină de internet\",\n    \"placeholder\": {\n      \"authorName\": \"Numele sau numele tău de utilizator\",\n      \"libraryName\": \"Numele bibliotecii tale\",\n      \"libraryDesc\": \"Descrierea bibliotecii tale pentru a ajuta oamenii să înțeleagă utilizarea acesteia\",\n      \"githubHandle\": \"Numele de utilizator GitHub (opțional), pentru a putea edita biblioteca odată ce este trimisă spre revizuire\",\n      \"twitterHandle\": \"Numele de utilizator Twitter (opțional), pentru a indica sursa la promovarea pe Twitter\",\n      \"website\": \"Trimitere către pagina ta personală de internet sau altundeva (opțional)\"\n    },\n    \"errors\": {\n      \"required\": \"Obligatoriu\",\n      \"website\": \"Introdu un URL valid\"\n    },\n    \"noteDescription\": \"Trimite-ți biblioteca pentru a fi inclusă în <link>depozitul de biblioteci publice</link> în vederea utilizării de către alte persoane în desenele lor.\",\n    \"noteGuidelines\": \"Biblioteca trebuie aprobată manual mai întâi. Citește <link>orientările</link> înainte de trimitere. Vei avea nevoie de un cont GitHub pentru a comunica și efectua modificări, dacă este cazul, însă nu este strict necesar.\",\n    \"noteLicense\": \"Prin trimiterea bibliotecii, ești de acord că aceasta va fi publicată sub <link>Licența MIT, </link>care, pe scurt, înseamnă că oricine o poate folosi fără restricții.\",\n    \"noteItems\": \"Fiecare element din bibliotecă trebuie să aibă propriul nume astfel încât să fie filtrabil. Următoarele elemente din bibliotecă vor fi incluse:\",\n    \"atleastOneLibItem\": \"Selectează cel puțin un element din bibliotecă pentru a începe\",\n    \"republishWarning\": \"Observație: unele dintre elementele selectate sunt marcate ca fiind deja publicate/trimise. Ar trebui să retrimiți elemente numai atunci când actualizezi o trimitere sau o bibliotecă existentă.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Bibliotecă trimisă\",\n    \"content\": \"Îți mulțumim, {{authorName}}. Biblioteca a fost trimisă spre revizuire. Poți urmări starea <link>aici</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Resetare bibliotecă\",\n    \"removeItemsFromLib\": \"Elimină elementele selectate din bibliotecă\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Exportare imagine\",\n    \"label\": {\n      \"withBackground\": \"Fundal\",\n      \"onlySelected\": \"Numai selecția\",\n      \"darkMode\": \"Mod întunecat\",\n      \"embedScene\": \"Încorporare scenă\",\n      \"scale\": \"Scală\",\n      \"padding\": \"Spațiere\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Datele scenei vor fi salvate în fișierul PNG/SVG exportat, astfel că scena va putea fi restaurată din acesta.\\nVa crește dimensiunea fișierului exportat.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"Exportare ca PNG\",\n      \"exportToSvg\": \"Exportare ca SVG\",\n      \"copyPngToClipboard\": \"Copiere PNG în memoria temporară\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Copiere în memoria temporară\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Desenele tale sunt criptate integral, astfel că serverele Excalidraw nu le vor vedea niciodată.\",\n    \"link\": \"Articol de blog pe criptarea integrală din Excalidraw\"\n  },\n  \"stats\": {\n    \"angle\": \"Unghi\",\n    \"element\": \"Element\",\n    \"elements\": \"Elemente\",\n    \"height\": \"Înălțime\",\n    \"scene\": \"Scenă\",\n    \"selected\": \"Selectate\",\n    \"storage\": \"Stocare\",\n    \"title\": \"Statistici pentru pasionați\",\n    \"total\": \"Total\",\n    \"version\": \"Versiune\",\n    \"versionCopy\": \"Clic pentru copiere\",\n    \"versionNotAvailable\": \"Versiune indisponibilă\",\n    \"width\": \"Lățime\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Adăugat în bibliotecă\",\n    \"copyStyles\": \"Stiluri copiate.\",\n    \"copyToClipboard\": \"Copiat în memoria temporară.\",\n    \"copyToClipboardAsPng\": \"S-a copiat {{exportSelection}} în memoria temporară sub formă de PNG\\n({{exportColorScheme}})\",\n    \"fileSaved\": \"Fișier salvat.\",\n    \"fileSavedToFilename\": \"Salvat în {filename}\",\n    \"canvas\": \"pânza\",\n    \"selection\": \"selecția\",\n    \"pasteAsSingleElement\": \"Folosește {{shortcut}} pentru a insera ca un singur element\\nsau insera într-un editor de text existent\",\n    \"unableToEmbed\": \"Încorporarea acestui URL nu este permisă momentan. Deschideți un tichet cu probleme pe GitHub pentru a solicita adăugarea acestui URL în lista albă\",\n    \"unrecognizedLinkFormat\": \"URL-ul pe care l-ai încorporat nu coincide cu formatul așteptat. Încearcă să lipești șirul „de încorporat” furnizat de pagina sursă\"\n  },\n  \"colors\": {\n    \"transparent\": \"Transparent\",\n    \"black\": \"Negru\",\n    \"white\": \"Alb\",\n    \"red\": \"Roșu\",\n    \"pink\": \"Roz\",\n    \"grape\": \"Struguriu\",\n    \"violet\": \"Violet\",\n    \"gray\": \"Gri\",\n    \"blue\": \"Albastru\",\n    \"cyan\": \"Cyan\",\n    \"teal\": \"Cyan-verde\",\n    \"green\": \"Verde\",\n    \"yellow\": \"Galben\",\n    \"orange\": \"Portocaliu\",\n    \"bronze\": \"Bronz\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"Toate datele tale sunt salvate local în navigatorul tău.\",\n      \"center_heading_plus\": \"Ai vrut să mergi în schimb la Excalidraw+?\",\n      \"menuHint\": \"Exportare, preferințe, limbi, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Exportare, preferințe și mai multe...\",\n      \"center_heading\": \"Diagrame. Făcute. Simple.\",\n      \"toolbarHint\": \"Alege un instrument și începe să desenezi!\",\n      \"helpHint\": \"Comenzi rapide și ajutor\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"Cele mai utilizate culori personalizate\",\n    \"colors\": \"Culori\",\n    \"shades\": \"Nuanțe\",\n    \"hexCode\": \"Cod hexa\",\n    \"noShades\": \"Nu este disponibilă nicio nuanță pentru această culoare\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"Exportare ca imagine\",\n        \"button\": \"Exportare ca imagine\",\n        \"description\": \"Exportă datele scenei ca fișier din care poți importa mai târziu.\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"Salvare pe disc\",\n        \"button\": \"Salvare pe disc\",\n        \"description\": \"Exportă datele scenei pe un fișier din care poți importa mai târziu.\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"Excalidraw+\",\n        \"button\": \"Exportare în Excalidraw+\",\n        \"description\": \"Salvează scena în spațiul de lucru Excalidraw+.\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"Încărcare din fișier\",\n        \"button\": \"Încărcare din fișier\",\n        \"description\": \"Încărcarea dintr-un fișier va <bold>înlocui conținutul existent</bold>.<br></br>Poți face mai întâi o copie de rezervă a desenului folosind una dintre opțiunile de mai jos.\"\n      },\n      \"shareableLink\": {\n        \"title\": \"Încărcare din lnk\",\n        \"button\": \"Înlocuiește conținutul meu\",\n        \"description\": \"Încărcarea unui desen extern va <bold>înlocui conținutul existent</bold>.<br></br>Poți face mai întâi o copie de rezervă a desenului folosind una dintre opțiunile de mai jos.\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"Mermaid la Excalidraw\",\n    \"button\": \"Introducere\",\n    \"description\": \"În prezent, numai <flowchartLink>Organigramele</flowchartLink>, <sequenceLink>Diagramele de secvență</sequenceLink> și <classLink>Diagramele de clasă</classLink> sunt acceptate. Celelalte tipuri vor fi redate ca imagine în Excalidraw.\",\n    \"syntax\": \"Sintaxă Mermaid\",\n    \"preview\": \"Previzualizare\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}