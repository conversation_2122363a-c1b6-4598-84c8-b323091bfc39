import "../chunk-XDFCUUT6.js";

// locales/zh-CN.json
var labels = {
  paste: "\u7C98\u8D34",
  pasteAsPlaintext: "\u7C98\u8D34\u4E3A\u7EAF\u6587\u672C",
  pasteCharts: "\u7C98\u8D34\u56FE\u8868",
  selectAll: "\u5168\u90E8\u9009\u4E2D",
  multiSelect: "\u6DFB\u52A0\u5143\u7D20\u5230\u9009\u533A",
  moveCanvas: "\u79FB\u52A8\u753B\u5E03",
  cut: "\u526A\u5207",
  copy: "\u62F7\u8D1D",
  copyAsPng: "\u590D\u5236\u4E3A PNG \u5230\u526A\u8D34\u677F",
  copyAsSvg: "\u590D\u5236\u4E3A SVG \u5230\u526A\u8D34\u677F",
  copyText: "\u590D\u5236\u6587\u672C\u5230\u526A\u8D34\u677F",
  copySource: "\u590D\u5236\u6E90\u7801\u5230\u526A\u8D34\u677F",
  convertToCode: "\u8F6C\u6362\u6210\u4EE3\u7801",
  bringForward: "\u4E0A\u79FB\u4E00\u5C42",
  sendToBack: "\u7F6E\u4E8E\u5E95\u5C42",
  bringToFront: "\u7F6E\u4E8E\u9876\u5C42",
  sendBackward: "\u4E0B\u79FB\u4E00\u5C42",
  delete: "\u5220\u9664",
  copyStyles: "\u62F7\u8D1D\u6837\u5F0F",
  pasteStyles: "\u7C98\u8D34\u6837\u5F0F",
  stroke: "\u63CF\u8FB9",
  background: "\u80CC\u666F",
  fill: "\u586B\u5145",
  strokeWidth: "\u63CF\u8FB9\u5BBD\u5EA6",
  strokeStyle: "\u8FB9\u6846\u6837\u5F0F",
  strokeStyle_solid: "\u5B9E\u7EBF",
  strokeStyle_dashed: "\u865A\u7EBF",
  strokeStyle_dotted: "\u70B9\u865A\u7EBF",
  sloppiness: "\u7EBF\u6761\u98CE\u683C",
  opacity: "\u900F\u660E\u5EA6",
  textAlign: "\u6587\u672C\u5BF9\u9F50",
  edges: "\u8FB9\u89D2",
  sharp: "\u5C16\u9510",
  round: "\u5706\u6DA6",
  arrowheads: "\u7AEF\u70B9",
  arrowhead_none: "\u65E0",
  arrowhead_arrow: "\u7BAD\u5934",
  arrowhead_bar: "\u6761\u72B6",
  arrowhead_circle: "\u5706\u70B9",
  arrowhead_circle_outline: "\u5706\u70B9\uFF08\u7A7A\u5FC3\uFF09",
  arrowhead_triangle: "\u4E09\u89D2\u7BAD\u5934",
  arrowhead_triangle_outline: "\u4E09\u89D2\u7BAD\u5934\uFF08\u7A7A\u5FC3\uFF09",
  arrowhead_diamond: "\u83F1\u5F62",
  arrowhead_diamond_outline: "\u83F1\u5F62\uFF08\u7A7A\u5FC3\uFF09",
  fontSize: "\u5B57\u4F53\u5927\u5C0F",
  fontFamily: "\u5B57\u4F53",
  addWatermark: "\u6DFB\u52A0 \u201C\u4F7F\u7528 Excalidraw \u521B\u5EFA\u201D \u6C34\u5370",
  handDrawn: "\u624B\u5199",
  normal: "\u666E\u901A",
  code: "\u4EE3\u7801",
  small: "\u5C0F",
  medium: "\u4E2D",
  large: "\u5927",
  veryLarge: "\u52A0\u5927",
  solid: "\u5B9E\u5FC3",
  hachure: "\u7EBF\u6761",
  zigzag: "\u4E4B\u5B57\u5F62\u6298\u7EBF",
  crossHatch: "\u4EA4\u53C9\u7EBF\u6761",
  thin: "\u7EC6",
  bold: "\u7C97",
  left: "\u5DE6\u5BF9\u9F50",
  center: "\u5C45\u4E2D",
  right: "\u53F3\u5BF9\u9F50",
  extraBold: "\u7279\u7C97",
  architect: "\u6734\u7D20",
  artist: "\u827A\u672F",
  cartoonist: "\u6F2B\u753B\u5BB6",
  fileTitle: "\u6587\u4EF6\u540D",
  colorPicker: "\u53D6\u8272\u5668",
  canvasColors: "\u753B\u5E03\u4E0A\u7684",
  canvasBackground: "\u753B\u5E03\u80CC\u666F",
  drawingCanvas: "\u7ED8\u5236 Canvas",
  layers: "\u56FE\u5C42",
  actions: "\u64CD\u4F5C",
  language: "\u8BED\u8A00",
  liveCollaboration: "\u5B9E\u65F6\u534F\u4F5C...",
  duplicateSelection: "\u590D\u5236",
  untitled: "\u65E0\u6807\u9898",
  name: "\u540D\u5B57",
  yourName: "\u60A8\u7684\u59D3\u540D",
  madeWithExcalidraw: "\u4F7F\u7528 Excalidraw \u521B\u5EFA",
  group: "\u7F16\u7EC4",
  ungroup: "\u89E3\u9664\u7F16\u7EC4",
  collaborators: "\u534F\u4F5C\u8005",
  showGrid: "\u663E\u793A\u7F51\u683C",
  addToLibrary: "\u6DFB\u52A0\u5230\u7D20\u6750\u5E93\u4E2D",
  removeFromLibrary: "\u4ECE\u7D20\u6750\u5E93\u4E2D\u79FB\u9664",
  libraryLoadingMessage: "\u6B63\u5728\u52A0\u8F7D\u7D20\u6750\u5E93\u2026",
  libraries: "\u6D4F\u89C8\u7D20\u6750\u5E93",
  loadingScene: "\u6B63\u5728\u52A0\u8F7D\u7ED8\u56FE\u2026",
  align: "\u5BF9\u9F50",
  alignTop: "\u9876\u90E8\u5BF9\u9F50",
  alignBottom: "\u5E95\u7AEF\u5BF9\u9F50",
  alignLeft: "\u5DE6\u5BF9\u9F50",
  alignRight: "\u53F3\u5BF9\u9F50",
  centerVertically: "\u5782\u76F4\u5C45\u4E2D",
  centerHorizontally: "\u6C34\u5E73\u5C45\u4E2D",
  distributeHorizontally: "\u6C34\u5E73\u7B49\u8DDD\u5206\u5E03",
  distributeVertically: "\u5782\u76F4\u7B49\u8DDD\u5206\u5E03",
  flipHorizontal: "\u6C34\u5E73\u7FFB\u8F6C",
  flipVertical: "\u5782\u76F4\u7FFB\u8F6C",
  viewMode: "\u67E5\u770B\u6A21\u5F0F",
  share: "\u5206\u4EAB",
  showStroke: "\u663E\u793A\u63CF\u8FB9\u989C\u8272\u9009\u62E9\u5668",
  showBackground: "\u663E\u793A\u80CC\u666F\u989C\u8272\u9009\u62E9\u5668",
  toggleTheme: "\u5207\u6362\u4E3B\u9898",
  personalLib: "\u4E2A\u4EBA\u7D20\u6750\u5E93",
  excalidrawLib: "Excalidraw \u7D20\u6750\u5E93",
  decreaseFontSize: "\u7F29\u5C0F\u5B57\u4F53\u5927\u5C0F",
  increaseFontSize: "\u653E\u5927\u5B57\u4F53\u5927\u5C0F",
  unbindText: "\u53D6\u6D88\u6587\u672C\u7ED1\u5B9A",
  bindText: "\u5C06\u6587\u672C\u7ED1\u5B9A\u5230\u5BB9\u5668",
  createContainerFromText: "\u5C06\u6587\u672C\u5305\u56F4\u5728\u5BB9\u5668\u4E2D",
  link: {
    edit: "\u7F16\u8F91\u94FE\u63A5",
    editEmbed: "\u7F16\u8F91\u94FE\u63A5\u4E0E\u5D4C\u5165",
    create: "\u65B0\u5EFA\u94FE\u63A5",
    createEmbed: "\u521B\u5EFA\u94FE\u63A5\u4E0E\u5D4C\u5165",
    label: "\u94FE\u63A5",
    labelEmbed: "\u94FE\u63A5\u4E0E\u5D4C\u5165",
    empty: "\u672A\u8BBE\u5B9A\u94FE\u63A5"
  },
  lineEditor: {
    edit: "\u7F16\u8F91\u7EBF\u6761",
    exit: "\u9000\u51FA\u7EBF\u6761\u7F16\u8F91"
  },
  elementLock: {
    lock: "\u9501\u5B9A",
    unlock: "\u89E3\u9664\u9501\u5B9A",
    lockAll: "\u5168\u90E8\u9501\u5B9A",
    unlockAll: "\u5168\u90E8\u89E3\u9501"
  },
  statusPublished: "\u5DF2\u53D1\u5E03",
  sidebarLock: "\u4FA7\u8FB9\u680F\u5E38\u9A7B",
  selectAllElementsInFrame: "\u9009\u62E9\u753B\u6846\u4E2D\u7684\u6240\u6709\u5143\u7D20",
  removeAllElementsFromFrame: "\u5206\u79BB\u51FA\u753B\u6846\u4E2D\u7684\u6240\u6709\u5143\u7D20",
  eyeDropper: "\u4ECE\u753B\u5E03\u4E0A\u53D6\u8272",
  textToDiagram: "\u6587\u5B57\u81F3\u56FE\u8868",
  prompt: "Prompt"
};
var library = {
  noItems: "\u5C1A\u672A\u6DFB\u52A0\u4EFB\u4F55\u9879\u76EE\u2026\u2026",
  hint_emptyLibrary: "\u9009\u4E2D\u753B\u5E03\u4E0A\u7684\u9879\u76EE\u6DFB\u52A0\u5230\u6B64\u5904\uFF0C\u6216\u4ECE\u4E0B\u65B9\u7684\u516C\u5171\u7D20\u6750\u5E93\u4E2D\u5BFC\u5165\u3002",
  hint_emptyPrivateLibrary: "\u9009\u4E2D\u753B\u5E03\u4E0A\u7684\u9879\u76EE\u6DFB\u52A0\u5230\u6B64\u5904\u3002"
};
var buttons = {
  clearReset: "\u91CD\u7F6E\u753B\u5E03",
  exportJSON: "\u5BFC\u51FA\u4E3A\u6587\u4EF6",
  exportImage: "\u5BFC\u51FA\u56FE\u7247...",
  export: "\u4FDD\u5B58\u5230...",
  copyToClipboard: "\u590D\u5236\u5230\u526A\u8D34\u677F",
  save: "\u4FDD\u5B58\u81F3\u5F53\u524D\u6587\u4EF6",
  saveAs: "\u4FDD\u5B58\u4E3A",
  load: "\u6253\u5F00",
  getShareableLink: "\u83B7\u53D6\u5171\u4EAB\u94FE\u63A5",
  close: "\u5173\u95ED",
  selectLanguage: "\u9009\u62E9\u8BED\u8A00",
  scrollBackToContent: "\u6EDA\u52A8\u56DE\u5230\u5185\u5BB9",
  zoomIn: "\u653E\u5927",
  zoomOut: "\u7F29\u5C0F",
  resetZoom: "\u91CD\u7F6E\u7F29\u653E",
  menu: "\u83DC\u5355",
  done: "\u5B8C\u6210",
  edit: "\u7F16\u8F91",
  undo: "\u64A4\u9500",
  redo: "\u91CD\u505A",
  resetLibrary: "\u91CD\u7F6E\u7D20\u6750\u5E93",
  createNewRoom: "\u65B0\u5EFA\u4F1A\u8BAE\u5BA4",
  fullScreen: "\u5168\u5C4F",
  darkMode: "\u6DF1\u8272\u6A21\u5F0F",
  lightMode: "\u6D45\u8272\u6A21\u5F0F",
  zenMode: "\u7985\u6A21\u5F0F",
  objectsSnapMode: "\u5438\u9644\u81F3\u5BF9\u8C61",
  exitZenMode: "\u9000\u51FA\u7985\u6A21\u5F0F",
  cancel: "\u53D6\u6D88",
  clear: "\u6E05\u9664",
  remove: "\u5220\u9664",
  embed: "\u5207\u6362\u5D4C\u5165",
  publishLibrary: "\u53D1\u5E03",
  submit: "\u63D0\u4EA4",
  confirm: "\u786E\u5B9A",
  embeddableInteractionButton: "\u70B9\u51FB\u4EE5\u5F00\u59CB\u4EA4\u4E92"
};
var alerts = {
  clearReset: "\u8FD9\u5C06\u4F1A\u6E05\u9664\u6574\u4E2A\u753B\u5E03\u3002\u60A8\u662F\u5426\u8981\u7EE7\u7EED?",
  couldNotCreateShareableLink: "\u65E0\u6CD5\u521B\u5EFA\u5171\u4EAB\u94FE\u63A5",
  couldNotCreateShareableLinkTooBig: "\u65E0\u6CD5\u521B\u5EFA\u53EF\u5171\u4EAB\u94FE\u63A5\uFF1A\u753B\u5E03\u8FC7\u5927",
  couldNotLoadInvalidFile: "\u65E0\u6CD5\u52A0\u8F7D\u65E0\u6548\u7684\u6587\u4EF6",
  importBackendFailed: "\u4ECE\u540E\u7AEF\u5BFC\u5165\u5931\u8D25\u3002",
  cannotExportEmptyCanvas: "\u65E0\u6CD5\u5BFC\u51FA\u7A7A\u767D\u753B\u5E03\u3002",
  couldNotCopyToClipboard: "\u65E0\u6CD5\u590D\u5236\u5230\u526A\u8D34\u677F\u3002",
  decryptFailed: "\u65E0\u6CD5\u89E3\u5BC6\u6570\u636E\u3002",
  uploadedSecurly: "\u4E0A\u4F20\u5DF2\u88AB\u7AEF\u5230\u7AEF\u52A0\u5BC6\u4FDD\u62A4\uFF0C\u8FD9\u610F\u5473\u7740 Excalidraw \u7684\u670D\u52A1\u5668\u548C\u7B2C\u4E09\u65B9\u90FD\u65E0\u6CD5\u8BFB\u53D6\u5185\u5BB9\u3002",
  loadSceneOverridePrompt: "\u52A0\u8F7D\u5916\u90E8\u7ED8\u56FE\u5C06\u53D6\u4EE3\u60A8\u73B0\u6709\u7684\u5185\u5BB9\u3002\u60A8\u60F3\u8981\u7EE7\u7EED\u5417\uFF1F",
  collabStopOverridePrompt: "\u505C\u6B62\u4F1A\u8BDD\u5C06\u8986\u76D6\u60A8\u5148\u524D\u672C\u5730\u5B58\u50A8\u7684\u7ED8\u56FE\u3002 \u60A8\u786E\u5B9A\u5417\uFF1F\n\n(\u5982\u679C\u60A8\u60F3\u4FDD\u6301\u672C\u5730\u7ED8\u56FE\uFF0C\u53EA\u9700\u5173\u95ED\u6D4F\u89C8\u5668\u9009\u9879\u5361\u3002)",
  errorAddingToLibrary: "\u65E0\u6CD5\u5C06\u9879\u76EE\u6DFB\u52A0\u5230\u7D20\u6750\u5E93\u4E2D",
  errorRemovingFromLibrary: "\u65E0\u6CD5\u4ECE\u7D20\u6750\u5E93\u4E2D\u79FB\u9664\u9879\u76EE",
  confirmAddLibrary: "\u8FD9\u5C06\u6DFB\u52A0 {{numShapes}} \u4E2A\u5F62\u72B6\u5230\u60A8\u7684\u7D20\u6750\u5E93\u4E2D\u3002\u60A8\u786E\u5B9A\u5417\uFF1F",
  imageDoesNotContainScene: "\u6B64\u56FE\u50CF\u4F3C\u4E4E\u4E0D\u5305\u542B\u4EFB\u4F55\u753B\u5E03\u6570\u636E\u3002\u60A8\u662F\u5426\u5728\u5BFC\u51FA\u65F6\u542F\u7528\u4E86\u753B\u5E03\u5D4C\u5165\u529F\u80FD\uFF1F",
  cannotRestoreFromImage: "\u65E0\u6CD5\u4ECE\u6B64\u56FE\u50CF\u6587\u4EF6\u6062\u590D\u753B\u5E03",
  invalidSceneUrl: "\u65E0\u6CD5\u4ECE\u63D0\u4F9B\u7684 URL \u5BFC\u5165\u573A\u666F\u3002\u5B83\u6216\u8005\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u6216\u8005\u4E0D\u5305\u542B\u6709\u6548\u7684 Excalidraw JSON \u6570\u636E\u3002",
  resetLibrary: "\u8FD9\u5C06\u4F1A\u6E05\u9664\u4F60\u7684\u7D20\u6750\u5E93\u3002\u4F60\u786E\u5B9A\u8981\u8FD9\u4E48\u505A\u5417\uFF1F",
  removeItemsFromsLibrary: "\u786E\u5B9A\u8981\u4ECE\u7D20\u6750\u5E93\u4E2D\u5220\u9664 {{count}} \u4E2A\u9879\u76EE\u5417\uFF1F",
  invalidEncryptionKey: "\u5BC6\u94A5\u5FC5\u987B\u5305\u542B22\u4E2A\u5B57\u7B26\u3002\u5B9E\u65F6\u534F\u4F5C\u5DF2\u88AB\u7981\u7528\u3002",
  collabOfflineWarning: "\u65E0\u7F51\u7EDC\u8FDE\u63A5\u3002\n\u60A8\u7684\u6539\u52A8\u5C06\u4E0D\u4F1A\u88AB\u4FDD\u5B58\uFF01"
};
var errors = {
  unsupportedFileType: "\u4E0D\u652F\u6301\u7684\u6587\u4EF6\u683C\u5F0F\u3002",
  imageInsertError: "\u65E0\u6CD5\u63D2\u5165\u56FE\u50CF\u3002\u8BF7\u7A0D\u540E\u518D\u8BD5\u2026\u2026",
  fileTooBig: "\u6587\u4EF6\u8FC7\u5927\u3002\u6700\u5927\u5141\u8BB8\u7684\u5927\u5C0F\u4E3A {{maxSize}}\u3002",
  svgImageInsertError: "\u65E0\u6CD5\u63D2\u5165 SVG \u56FE\u50CF\u3002\u8BE5 SVG \u6807\u8BB0\u4F3C\u4E4E\u662F\u65E0\u6548\u7684\u3002",
  failedToFetchImage: "\u65E0\u6CD5\u83B7\u53D6\u56FE\u7247\u3002",
  invalidSVGString: "\u65E0\u6548\u7684 SVG\u3002",
  cannotResolveCollabServer: "\u65E0\u6CD5\u8FDE\u63A5\u5230\u5B9E\u65F6\u534F\u4F5C\u670D\u52A1\u5668\u3002\u8BF7\u91CD\u65B0\u52A0\u8F7D\u9875\u9762\u5E76\u91CD\u8BD5\u3002",
  importLibraryError: "\u65E0\u6CD5\u52A0\u8F7D\u7D20\u6750\u5E93",
  collabSaveFailed: "\u65E0\u6CD5\u4FDD\u5B58\u5230\u540E\u7AEF\u6570\u636E\u5E93\u3002\u5982\u679C\u95EE\u9898\u6301\u7EED\u5B58\u5728\uFF0C\u60A8\u5E94\u8BE5\u4FDD\u5B58\u6587\u4EF6\u5230\u672C\u5730\uFF0C\u4EE5\u786E\u4FDD\u60A8\u7684\u5DE5\u4F5C\u4E0D\u4F1A\u4E22\u5931\u3002",
  collabSaveFailed_sizeExceeded: "\u65E0\u6CD5\u4FDD\u5B58\u5230\u540E\u7AEF\u6570\u636E\u5E93\uFF0C\u753B\u5E03\u4F3C\u4E4E\u8FC7\u5927\u3002\u60A8\u5E94\u8BE5\u4FDD\u5B58\u6587\u4EF6\u5230\u672C\u5730\uFF0C\u4EE5\u786E\u4FDD\u60A8\u7684\u5DE5\u4F5C\u4E0D\u4F1A\u4E22\u5931\u3002",
  imageToolNotSupported: "\u56FE\u7247\u5DF2\u88AB\u7981\u7528\u3002",
  brave_measure_text_error: {
    line1: "\u60A8\u4F3C\u4E4E\u6B63\u5728\u4F7F\u7528 Brave \u6D4F\u89C8\u5668\u5E76\u542F\u7528\u4E86<bold>\u79EF\u6781\u963B\u6B62\u6307\u7EB9\u8BC6\u522B</bold>\u7684\u8BBE\u7F6E\u3002",
    line2: "\u8FD9\u53EF\u80FD\u4F1A\u7834\u574F\u7ED8\u56FE\u4E2D\u7684 <bold>\u6587\u672C\u5143\u7D20</bold>\u3002",
    line3: "\u6211\u4EEC\u5F3A\u70C8\u5EFA\u8BAE\u7981\u7528\u6B64\u8BBE\u7F6E\u3002\u60A8\u53EF\u4EE5\u6309\u7167<link>\u8FD9\u4E9B\u6B65\u9AA4</link>\u6765\u8BBE\u7F6E\u3002",
    line4: "\u5982\u679C\u7981\u7528\u6B64\u8BBE\u7F6E\u65E0\u6CD5\u4FEE\u590D\u6587\u672C\u5143\u7D20\u7684\u663E\u793A\uFF0C\u8BF7\u5728 GitHub \u4E0A\u63D0\u4EA4\u4E00\u4E2A <issueLink>issue</issueLink> \uFF0C\u6216\u8005\u5728 <discordLink>Discord</discordLink> \u4E0A\u53CD\u9988"
  },
  libraryElementTypeError: {
    embeddable: "\u5D4C\u5165\u7684\u5143\u7D20\u4E0D\u80FD\u88AB\u6DFB\u52A0\u5230\u7D20\u6750\u5E93\u3002",
    iframe: "\u4E0D\u80FD\u5C06 IFrame \u5143\u7D20\u6DFB\u52A0\u5230\u7D20\u6750\u5E93\u4E2D\u3002",
    image: "\u6211\u4EEC\u4E0D\u4E45\u5C06\u652F\u6301\u6DFB\u52A0\u56FE\u7247\u5230\u7D20\u6750\u5E93"
  },
  asyncPasteFailedOnRead: "\u65E0\u6CD5\u7C98\u8D34\uFF08\u65E0\u6CD5\u8BFB\u53D6\u7CFB\u7EDF\u526A\u8D34\u677F\uFF09\u3002",
  asyncPasteFailedOnParse: "\u65E0\u6CD5\u7C98\u8D34\u3002",
  copyToSystemClipboardFailed: "\u65E0\u6CD5\u590D\u5236\u5230\u526A\u8D34\u677F\u3002"
};
var toolBar = {
  selection: "\u9009\u62E9",
  image: "\u63D2\u5165\u56FE\u50CF",
  rectangle: "\u77E9\u5F62",
  diamond: "\u83F1\u5F62",
  ellipse: "\u692D\u5706",
  arrow: "\u7BAD\u5934",
  line: "\u7EBF\u6761",
  freedraw: "\u81EA\u7531\u4E66\u5199",
  text: "\u6587\u5B57",
  library: "\u7D20\u6750\u5E93",
  lock: "\u7ED8\u5236\u540E\u4FDD\u6301\u6240\u9009\u7684\u5DE5\u5177\u680F\u72B6\u6001",
  penMode: "\u7B14\u6A21\u5F0F \u2013 \u907F\u514D\u8BEF\u89E6",
  link: "\u4E3A\u9009\u4E2D\u7684\u5F62\u72B6\u6DFB\u52A0/\u66F4\u65B0\u94FE\u63A5",
  eraser: "\u6A61\u76AE",
  frame: "\u753B\u6846\u5DE5\u5177",
  magicframe: "\u7EBF\u6846\u56FE\u81F3\u4EE3\u7801",
  embeddable: "\u5D4C\u5165\u7F51\u9875",
  laser: "\u6FC0\u5149\u7B14",
  hand: "\u6293\u624B\uFF08\u5E73\u79FB\u5DE5\u5177\uFF09",
  extraTools: "\u66F4\u591A\u5DE5\u5177",
  mermaidToExcalidraw: "Mermaid \u81F3 Excalidraw",
  magicSettings: "AI \u8BBE\u7F6E"
};
var headings = {
  canvasActions: "\u753B\u5E03\u52A8\u4F5C",
  selectedShapeActions: "\u9009\u5B9A\u5F62\u72B6\u64CD\u4F5C",
  shapes: "\u5F62\u72B6"
};
var hints = {
  canvasPanning: "\u8981\u79FB\u52A8\u753B\u5E03\uFF0C\u8BF7\u6309\u4F4F\u9F20\u6807\u6EDA\u8F6E\u6216\u7A7A\u683C\u952E\u540C\u65F6\u62D6\u62FD\u9F20\u6807\uFF0C\u6216\u4F7F\u7528\u6293\u624B\u5DE5\u5177\u3002",
  linearElement: "\u70B9\u51FB\u521B\u5EFA\u591A\u4E2A\u70B9 \u62D6\u52A8\u521B\u5EFA\u76F4\u7EBF",
  freeDraw: "\u70B9\u51FB\u5E76\u62D6\u52A8\uFF0C\u5B8C\u6210\u65F6\u677E\u5F00",
  text: "\u63D0\u793A\uFF1A\u60A8\u4E5F\u53EF\u4EE5\u4F7F\u7528\u9009\u62E9\u5DE5\u5177\u53CC\u51FB\u4EFB\u610F\u4F4D\u7F6E\u6765\u6DFB\u52A0\u6587\u5B57",
  embeddable: "\u70B9\u51FB\u5E76\u62D6\u52A8\u4EE5\u521B\u5EFA\u5D4C\u5165\u7F51\u9875",
  text_selected: "\u53CC\u51FB\u6216\u6309\u56DE\u8F66\u952E\u4EE5\u7F16\u8F91\u6587\u672C",
  text_editing: "\u6309\u4E0B Escape \u6216 CtrlOrCmd+ENTER \u5B8C\u6210\u7F16\u8F91",
  linearElementMulti: "\u70B9\u51FB\u6700\u540E\u4E00\u4E2A\u70B9\u6216\u6309\u4E0B Esc/Enter \u6765\u5B8C\u6210",
  lockAngle: "\u53EF\u4EE5\u6309\u4F4F Shift \u6765\u7EA6\u675F\u89D2\u5EA6",
  resize: "\u60A8\u53EF\u4EE5\u6309\u4F4FSHIFT\u6765\u9650\u5236\u6BD4\u4F8B\u5927\u5C0F\uFF0C\n\u6309\u4F4FALT\u6765\u8C03\u6574\u4E2D\u5FC3\u5927\u5C0F",
  resizeImage: "\u6309\u4F4FSHIFT\u53EF\u4EE5\u81EA\u7531\u7F29\u653E\uFF0C\n\u6309\u4F4FALT\u53EF\u4EE5\u4ECE\u4E2D\u95F4\u7F29\u653E",
  rotate: "\u65CB\u8F6C\u65F6\u53EF\u4EE5\u6309\u4F4F Shift \u6765\u7EA6\u675F\u89D2\u5EA6",
  lineEditor_info: "\u6309\u4F4F CtrlOrCmd \u5E76\u53CC\u51FB\u6216\u6309 CtrlOrCmd + Enter \u6765\u7F16\u8F91\u70B9",
  lineEditor_pointSelected: "\u6309\u4E0B Delete \u79FB\u9664\u70B9\uFF0CCtrlOrCmd+D \u4EE5\u590D\u5236\uFF0C\u62D6\u52A8\u4EE5\u79FB\u52A8",
  lineEditor_nothingSelected: "\u9009\u62E9\u8981\u7F16\u8F91\u7684\u70B9 (\u6309\u4F4F SHIFT \u9009\u62E9\u591A\u4E2A)\uFF0C\n\u6216\u6309\u4F4F Alt \u5E76\u70B9\u51FB\u4EE5\u6DFB\u52A0\u65B0\u70B9",
  placeImage: "\u70B9\u51FB\u653E\u7F6E\u56FE\u50CF\uFF0C\u6216\u8005\u70B9\u51FB\u5E76\u62D6\u52A8\u4EE5\u624B\u52A8\u8BBE\u7F6E\u56FE\u50CF\u5927\u5C0F",
  publishLibrary: "\u53D1\u5E03\u60A8\u81EA\u5DF1\u7684\u7D20\u6750\u5E93",
  bindTextToElement: "\u6309\u4E0B Enter \u4EE5\u6DFB\u52A0\u6587\u672C",
  deepBoxSelect: "\u6309\u4F4F CtrlOrCmd \u4EE5\u6DF1\u5EA6\u9009\u62E9\uFF0C\u5E76\u907F\u514D\u62D6\u62FD",
  eraserRevert: "\u6309\u4F4F Alt \u4EE5\u53CD\u9009\u88AB\u6807\u8BB0\u5220\u9664\u7684\u5143\u7D20",
  firefox_clipboard_write: "\u5C06\u9AD8\u7EA7\u914D\u7F6E\u9996\u9009\u9879\u201Cdom.events.asyncClipboard.clipboardItem\u201D\u8BBE\u7F6E\u4E3A\u201Ctrue\u201D\u53EF\u4EE5\u542F\u7528\u6B64\u529F\u80FD\u3002\u8981\u66F4\u6539 Firefox \u7684\u9AD8\u7EA7\u914D\u7F6E\u9996\u9009\u9879\uFF0C\u8BF7\u524D\u5F80\u201Cabout:config\u201D\u9875\u9762\u3002",
  disableSnapping: "\u6309\u4F4F Ctrl \u6216 Cmd \u4EE5\u7981\u7528\u5438\u9644"
};
var canvasError = {
  cannotShowPreview: "\u65E0\u6CD5\u663E\u793A\u9884\u89C8",
  canvasTooBig: "\u753B\u5E03\u53EF\u80FD\u8FC7\u5927\u3002",
  canvasTooBigTip: "\u63D0\u793A\uFF1A\u5C1D\u8BD5\u5C06\u6700\u8FDC\u7684\u5143\u7D20\u79FB\u52A8\u5230\u548C\u5176\u5B83\u5143\u7D20\u66F4\u8FD1\u4E00\u4E9B\u3002"
};
var errorSplash = {
  headingMain: "\u9047\u5230\u5F02\u5E38\u3002\u8BF7\u5C1D\u8BD5<button>\u91CD\u65B0\u52A0\u8F7D\u9875\u9762</button>\u3002",
  clearCanvasMessage: "\u5982\u679C\u91CD\u65B0\u52A0\u8F7D\u9875\u9762\u65E0\u6548\uFF0C\u8BF7\u5C1D\u8BD5<button>\u6E05\u9664\u753B\u5E03</button>\u3002",
  clearCanvasCaveat: "\u8FD9\u4F1A\u9020\u6210\u5F53\u524D\u5DE5\u4F5C\u4E22\u5931",
  trackedToSentry: "\u6807\u8BC6\u7B26\u4E3A{{eventId}}\u7684\u9519\u8BEF\u5DF2\u5728\u6211\u4EEC\u7684\u7CFB\u7EDF\u4E2D\u88AB\u8BB0\u5F55",
  openIssueMessage: "\u6211\u4EEC\u975E\u5E38\u8C28\u614E\u5730\u5904\u7406\u9519\u8BEF\u4FE1\u606F\uFF0C\u60A8\u7684\u753B\u5E03\u5185\u5BB9\u4E0D\u4F1A\u88AB\u5305\u542B\u5728\u9519\u8BEF\u62A5\u544A\u4E2D\u3002\u5982\u679C\u60A8\u7684\u753B\u5E03\u5185\u5BB9\u4E0D\u9700\u8981\u4FDD\u6301\u79C1\u5BC6\uFF0C\u8BF7\u8003\u8651\u5728\u6211\u4EEC\u7684 <button>bug \u8DDF\u8E2A\u7CFB\u7EDF</button>\u4E0A\u63D0\u4F9B\u66F4\u591A\u4FE1\u606F\u3002\u8BF7\u590D\u5236\u7C98\u8D34\u4EE5\u4E0B\u4FE1\u606F\u5230 GitHub Issue \u4E2D\u3002",
  sceneContent: "\u753B\u5E03\u5185\u5BB9:"
};
var roomDialog = {
  desc_intro: "\u4F60\u53EF\u4EE5\u9080\u8BF7\u5176\u4ED6\u4EBA\u5230\u76EE\u524D\u7684\u753B\u9762\u4E2D\u4E0E\u4F60\u534F\u4F5C\u3002",
  desc_privacy: "\u522B\u62C5\u5FC3\uFF0C\u8BE5\u4F1A\u8BDD\u4F7F\u7528\u7AEF\u5230\u7AEF\u52A0\u5BC6\uFF0C\u65E0\u8BBA\u7ED8\u5236\u4EC0\u4E48\u90FD\u5C06\u4FDD\u6301\u79C1\u5BC6\uFF0C\u751A\u81F3\u8FDE\u6211\u4EEC\u7684\u670D\u52A1\u5668\u4E5F\u65E0\u6CD5\u67E5\u770B\u3002",
  button_startSession: "\u5F00\u59CB\u4F1A\u8BDD",
  button_stopSession: "\u7ED3\u675F\u4F1A\u8BDD",
  desc_inProgressIntro: "\u5B9E\u65F6\u534F\u4F5C\u4F1A\u8BDD\u8FDB\u884C\u4E2D\u3002",
  desc_shareLink: "\u5206\u4EAB\u6B64\u94FE\u63A5\u7ED9\u4F60\u8981\u534F\u4F5C\u7684\u7528\u6237",
  desc_exitSession: "\u505C\u6B62\u4F1A\u8BDD\u5C06\u4E2D\u65AD\u60A8\u4E0E\u623F\u95F4\u7684\u8FDE\u63A5\uFF0C\u4F46\u60A8\u4F9D\u7136\u53EF\u4EE5\u5728\u672C\u5730\u7EE7\u7EED\u4F7F\u7528\u753B\u5E03\u3002\u8BF7\u6CE8\u610F\uFF0C\u8FD9\u4E0D\u4F1A\u5F71\u54CD\u5230\u5176\u4ED6\u7528\u6237\uFF0C\u4ED6\u4EEC\u4ECD\u53EF\u4EE5\u5728\u4ED6\u4EEC\u7684\u7248\u672C\u4E0A\u7EE7\u7EED\u534F\u4F5C\u3002",
  shareTitle: "\u52A0\u5165 Excalidraw \u5B9E\u65F6\u534F\u4F5C\u4F1A\u8BDD"
};
var errorDialog = {
  title: "\u9519\u8BEF"
};
var exportDialog = {
  disk_title: "\u4FDD\u5B58\u5230\u672C\u5730",
  disk_details: "\u5C06\u753B\u5E03\u6570\u636E\u5BFC\u51FA\u4E3A\u6587\u4EF6\uFF0C\u4EE5\u4FBF\u4EE5\u540E\u5BFC\u5165",
  disk_button: "\u4FDD\u5B58\u4E3A\u6587\u4EF6",
  link_title: "\u5206\u4EAB\u94FE\u63A5",
  link_details: "\u5BFC\u51FA\u4E3A\u53EA\u8BFB\u94FE\u63A5\u3002",
  link_button: "\u5BFC\u51FA\u94FE\u63A5",
  excalidrawplus_description: "\u5C06\u753B\u5E03\u4FDD\u5B58\u5230\u60A8\u7684 Excalidraw+ \u5DE5\u4F5C\u533A\u3002",
  excalidrawplus_button: "\u5BFC\u51FA",
  excalidrawplus_exportError: "\u6682\u65F6\u65E0\u6CD5\u5BFC\u51FA\u5230 Excalidraw+ ..."
};
var helpDialog = {
  blog: "\u6D4F\u89C8\u6211\u4EEC\u7684\u535A\u5BA2",
  click: "\u5355\u51FB",
  deepSelect: "\u6DF1\u5EA6\u9009\u62E9",
  deepBoxSelect: "\u5728\u65B9\u6846\u5185\u6DF1\u5EA6\u9009\u62E9\u5E76\u907F\u514D\u62D6\u62FD",
  curvedArrow: "\u66F2\u7EBF\u7BAD\u5934",
  curvedLine: "\u66F2\u7EBF",
  documentation: "\u6587\u6863",
  doubleClick: "\u53CC\u51FB",
  drag: "\u62D6\u52A8",
  editor: "\u7F16\u8F91\u5668",
  editLineArrowPoints: "\u7F16\u8F91\u7EBF\u6761\u6216\u7BAD\u5934\u7684\u70B9",
  editText: "\u6DFB\u52A0\u6216\u7F16\u8F91\u6587\u672C",
  github: "\u53D1\u73B0\u95EE\u9898\uFF1F\u63D0\u4EA4\u53CD\u9988",
  howto: "\u5E2E\u52A9\u6587\u6863",
  or: "\u6216",
  preventBinding: "\u7981\u7528\u7BAD\u5934\u5438\u9644",
  tools: "\u5DE5\u5177",
  shortcuts: "\u5FEB\u6377\u952E\u5217\u8868",
  textFinish: "\u5B8C\u6210\u7F16\u8F91 (\u6587\u672C\u7F16\u8F91\u5668)",
  textNewLine: "\u6DFB\u52A0\u65B0\u884C(\u6587\u672C\u7F16\u8F91\u5668)",
  title: "\u5E2E\u52A9",
  view: "\u89C6\u56FE",
  zoomToFit: "\u7F29\u653E\u4EE5\u9002\u5E94\u6240\u6709\u5143\u7D20",
  zoomToSelection: "\u7F29\u653E\u5230\u9009\u533A",
  toggleElementLock: "\u9501\u5B9A/\u89E3\u9501",
  movePageUpDown: "\u4E0A\u4E0B\u79FB\u52A8\u9875\u9762",
  movePageLeftRight: "\u5DE6\u53F3\u79FB\u52A8\u9875\u9762"
};
var clearCanvasDialog = {
  title: "\u6E05\u9664\u753B\u5E03"
};
var publishDialog = {
  title: "\u53D1\u5E03\u7D20\u6750\u5E93",
  itemName: "\u9879\u76EE\u540D\u79F0",
  authorName: "\u4F5C\u8005\u540D",
  githubUsername: "GitHub \u7528\u6237\u540D",
  twitterUsername: "Twitter \u7528\u6237\u540D",
  libraryName: "\u540D\u79F0",
  libraryDesc: "\u7B80\u4ECB",
  website: "\u7F51\u5740",
  placeholder: {
    authorName: "\u60A8\u7684\u540D\u5B57\u6216\u7528\u6237\u540D",
    libraryName: "\u7D20\u6750\u5E93\u540D\u79F0",
    libraryDesc: "\u4ECB\u7ECD\u60A8\u7684\u7D20\u6750\u5E93\uFF0C\u8BA9\u4EBA\u4EEC\u4E86\u89E3\u5176\u7528\u9014",
    githubHandle: "GitHub \u7528\u6237\u540D\uFF08\u53EF\u9009\uFF09\uFF0C\u586B\u5199\u540E\uFF0C\u60A8\u53EF\u4EE5\u7F16\u8F91\u5DF2\u63D0\u4EA4\u5F85\u5BA1\u7684\u7D20\u6750\u5E93",
    twitterHandle: "Twitter \u7528\u6237\u540D\uFF08\u53EF\u9009\uFF09\uFF0C\u586B\u5199\u540E\uFF0C\u5F53\u6211\u4EEC\u5728Twitter\u53D1\u5E03\u63A8\u5E7F\u4FE1\u606F\u65F6\u4FBF\u53EF\u63D0\u53CA\u60A8",
    website: "\u60A8\u4E2A\u4EBA\u7F51\u7AD9\u7684\u6216\u4EFB\u610F\u7684\u94FE\u63A5\uFF08\u53EF\u9009\uFF09"
  },
  errors: {
    required: "\u5FC5\u586B",
    website: "\u8F93\u5165\u4E00\u4E2A\u6709\u6548\u7684URL"
  },
  noteDescription: "\u63D0\u4EA4\u540E\uFF0C\u60A8\u7684\u7D20\u6750\u5E93\u5C06\u88AB\u5305\u542B\u5728<link>\u516C\u5171\u7D20\u6750\u5E93\u5E7F\u573A</link>\u4EE5\u4F9B\u5176\u4ED6\u4EBA\u5728\u7ED8\u56FE\u4E2D\u4F7F\u7528\u3002",
  noteGuidelines: "\u63D0\u4EA4\u7684\u7D20\u6750\u5E93\u9700\u5148\u7ECF\u4EBA\u5DE5\u5BA1\u6838\u3002\u5728\u63D0\u4EA4\u4E4B\u524D\uFF0C\u8BF7\u5148\u9605\u8BFB<link>\u6307\u5357</link> \u3002\u540E\u7EED\u6C9F\u901A\u548C\u5BF9\u5E93\u7684\u4FEE\u6539\u9700\u8981 GitHub \u8D26\u53F7\uFF0C\u4F46\u8FD9\u4E0D\u662F\u5FC5\u987B\u7684\u3002",
  noteLicense: "\u63D0\u4EA4\u5373\u8868\u660E\u60A8\u5DF2\u540C\u610F\u7D20\u6750\u5E93\u5C06\u9075\u5FAA <link>MIT \u8BB8\u53EF\u8BC1</link>\uFF0C\u7B80\u800C\u8A00\u4E4B\uFF0C\u4EFB\u4F55\u4EBA\u90FD\u53EF\u4EE5\u4E0D\u53D7\u9650\u5236\u5730\u4F7F\u7528\u5B83\u4EEC\u3002",
  noteItems: "\u7D20\u6750\u5E93\u4E2D\u6BCF\u4E2A\u9879\u76EE\u90FD\u6709\u5404\u81EA\u7684\u540D\u79F0\u4EE5\u4F9B\u7B5B\u9009\u3002\u4EE5\u4E0B\u9879\u76EE\u5C06\u88AB\u5305\u542B\uFF1A",
  atleastOneLibItem: "\u8BF7\u9009\u62E9\u81F3\u5C11\u4E00\u4E2A\u7D20\u6750\u5E93\u4EE5\u5F00\u59CB",
  republishWarning: "\u6CE8\u610F\uFF1A\u90E8\u5206\u9009\u4E2D\u7684\u9879\u76EE\u5DF2\u7ECF\u53D1\u5E03\u6216\u63D0\u4EA4\u3002\u8BF7\u4EC5\u5728\u66F4\u65B0\u5DF2\u6709\u6216\u5DF2\u63D0\u4EA4\u7684\u7D20\u6750\u5E93\u65F6\u91CD\u590D\u63D0\u4EA4\u9879\u76EE\u3002"
};
var publishSuccessDialog = {
  title: "\u7D20\u6750\u5E93\u5DF2\u63D0\u4EA4",
  content: "\u8C22\u8C22\u4F60 {{authorName}}\u3002\u60A8\u7684\u7D20\u6750\u5E93\u5DF2\u88AB\u63D0\u4EA4\u5BA1\u6838\u3002\u8BF7\u70B9\u51FB<link>\u6B64\u5904</link>\u8DDF\u8FDB\u6B64\u6B21\u63D0\u4EA4\u7684\u72B6\u6001"
};
var confirmDialog = {
  resetLibrary: "\u91CD\u7F6E\u7D20\u6750\u5E93",
  removeItemsFromLib: "\u4ECE\u7D20\u6750\u5E93\u4E2D\u5220\u9664\u9009\u4E2D\u7684\u9879\u76EE"
};
var imageExportDialog = {
  header: "\u5BFC\u51FA\u56FE\u7247",
  label: {
    withBackground: "\u80CC\u666F",
    onlySelected: "\u4EC5\u9009\u4E2D",
    darkMode: "\u6DF1\u8272\u6A21\u5F0F",
    embedScene: "\u5305\u542B\u753B\u5E03\u6570\u636E",
    scale: "\u7F29\u653E\u6BD4\u4F8B",
    padding: "\u5185\u8FB9\u8DDD"
  },
  tooltip: {
    embedScene: "\u753B\u5E03\u6570\u636E\u5C06\u88AB\u4FDD\u5B58\u5230\u5BFC\u51FA\u7684 PNG/SVG \u6587\u4EF6\uFF0C\u4EE5\u4FBF\u6062\u590D\u3002\n\u5C06\u4F1A\u589E\u52A0\u5BFC\u51FA\u6587\u4EF6\u7684\u5927\u5C0F\u3002"
  },
  title: {
    exportToPng: "\u5BFC\u51FA\u4E3A PNG",
    exportToSvg: "\u5BFC\u51FA\u4E3A SVG",
    copyPngToClipboard: "\u590D\u5236 PNG \u5230\u526A\u5207\u677F"
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "\u590D\u5236\u5230\u526A\u8D34\u677F"
  }
};
var encrypted = {
  tooltip: "\u60A8\u7684\u7ED8\u56FE\u91C7\u7528\u7AEF\u5230\u7AEF\u52A0\u5BC6\uFF0C\u5176\u5185\u5BB9\u5BF9\u4E8E Excalidraw \u670D\u52A1\u5668\u662F\u4E0D\u53EF\u89C1\u7684\u3002",
  link: "Excalidraw \u4E2D\u5173\u4E8E\u7AEF\u5230\u7AEF\u52A0\u5BC6\u7684\u535A\u5BA2"
};
var stats = {
  angle: "\u89D2\u5EA6",
  element: "\u5143\u7D20",
  elements: "\u5143\u7D20",
  height: "\u9AD8\u5EA6",
  scene: "\u753B\u5E03",
  selected: "\u9009\u4E2D",
  storage: "\u5B58\u50A8",
  title: "\u8BE6\u7EC6\u7EDF\u8BA1\u4FE1\u606F",
  total: "\u603B\u8BA1",
  version: "\u7248\u672C",
  versionCopy: "\u70B9\u51FB\u590D\u5236",
  versionNotAvailable: "\u7248\u672C\u4E0D\u53EF\u7528",
  width: "\u5BBD\u5EA6"
};
var toast = {
  addedToLibrary: "\u6DFB\u52A0\u5230\u7D20\u6750\u5E93\u4E2D",
  copyStyles: "\u6837\u5F0F\u5DF2\u62F7\u8D1D\u3002",
  copyToClipboard: "\u5DF2\u590D\u5236\u5230\u526A\u5207\u677F\u3002",
  copyToClipboardAsPng: "\u5DF2\u5C06 {{exportSelection}} \u4F5C\u4E3A PNG \u590D\u5236\u5230\u526A\u8D34\u677F\n({{exportColorScheme}})",
  fileSaved: "\u6587\u4EF6\u5DF2\u4FDD\u5B58\u3002",
  fileSavedToFilename: "\u4FDD\u5B58\u5230 {filename}",
  canvas: "\u753B\u5E03",
  selection: "\u6240\u9009\u9879",
  pasteAsSingleElement: "\u4F7F\u7528 {{shortcut}} \u7C98\u8D34\u4E3A\u5355\u4E2A\u5143\u7D20\uFF0C\n\u6216\u7C98\u8D34\u5230\u73B0\u6709\u7684\u6587\u672C\u7F16\u8F91\u5668\u91CC",
  unableToEmbed: "\u76EE\u524D\u4E0D\u5141\u8BB8\u5D4C\u5165\u6B64\u7F51\u5740\u3002\u8BF7\u5728 GitHub \u4E0A\u63D0 issue \u8BF7\u6C42\u5C06\u6B64\u7F51\u5740\u52A0\u5165\u767D\u540D\u5355",
  unrecognizedLinkFormat: "\u60A8\u5D4C\u5165\u7684\u94FE\u63A5\u4E0D\u7B26\u5408\u683C\u5F0F\u8981\u6C42\u3002\u8BF7\u5C1D\u8BD5\u7C98\u8D34\u6E90\u7F51\u7AD9\u63D0\u4F9B\u7684\u201C\u5D4C\u5165 (embed)\u201D\u5B57\u7B26\u4E32"
};
var colors = {
  transparent: "\u900F\u660E",
  black: "\u9ED1",
  white: "\u767D",
  red: "\u7EA2",
  pink: "\u7C89\u7EA2",
  grape: "\u7D2B\u7EA2",
  violet: "\u84DD\u7D2B",
  gray: "\u7070",
  blue: "\u84DD",
  cyan: "\u9752",
  teal: "\u84DD\u7EFF",
  green: "\u7EFF",
  yellow: "\u9EC4",
  orange: "\u6A59",
  bronze: "\u53E4\u94DC"
};
var welcomeScreen = {
  app: {
    center_heading: "\u60A8\u7684\u6240\u6709\u6570\u636E\u90FD\u50A8\u5B58\u5728\u6D4F\u89C8\u5668\u672C\u5730\u3002",
    center_heading_plus: "\u662F\u5426\u524D\u5F80 Excalidraw+ \uFF1F",
    menuHint: "\u5BFC\u51FA\u3001\u9996\u9009\u9879\u3001\u8BED\u8A00\u2026\u2026"
  },
  defaults: {
    menuHint: "\u5BFC\u51FA\u3001\u9996\u9009\u9879\u2026\u2026",
    center_heading: "\u56FE\uFF0C\u5316\u7E41\u4E3A\u7B80\u3002",
    toolbarHint: "\u9009\u62E9\u5DE5\u5177\u5E76\u5F00\u59CB\u7ED8\u56FE\uFF01",
    helpHint: "\u5FEB\u6377\u952E\u548C\u5E2E\u52A9"
  }
};
var colorPicker = {
  mostUsedCustomColors: "\u5E38\u7528\u81EA\u5B9A\u4E49\u989C\u8272",
  colors: "\u989C\u8272",
  shades: "\u8272\u8C03\u660E\u6697",
  hexCode: "\u5341\u516D\u8FDB\u5236\u503C",
  noShades: "\u6B64\u989C\u8272\u6CA1\u6709\u53EF\u7528\u7684\u660E\u6697\u53D8\u5316"
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "\u5BFC\u51FA\u4E3A\u56FE\u7247",
      button: "\u5BFC\u51FA\u4E3A\u56FE\u7247",
      description: "\u5C06\u753B\u5E03\u6570\u636E\u5BFC\u51FA\u4E3A\u56FE\u7247\uFF0C\u4EE5\u4FBF\u4EE5\u540E\u5BFC\u5165\u3002"
    },
    saveToDisk: {
      title: "\u4FDD\u5B58\u5230\u672C\u5730",
      button: "\u4FDD\u5B58\u5230\u672C\u5730",
      description: "\u5C06\u753B\u5E03\u6570\u636E\u5BFC\u51FA\u4E3A\u6587\u4EF6\uFF0C\u4EE5\u4FBF\u4EE5\u540E\u5BFC\u5165\u3002"
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "\u5BFC\u51FA\u5230 Excalidraw+",
      description: "\u5C06\u753B\u5E03\u4FDD\u5B58\u5230\u60A8\u7684 Excalidraw+ \u5DE5\u4F5C\u533A\u3002"
    }
  },
  modal: {
    loadFromFile: {
      title: "\u4ECE\u6587\u4EF6\u52A0\u8F7D",
      button: "\u4ECE\u6587\u4EF6\u52A0\u8F7D",
      description: "\u4ECE\u6587\u4EF6\u52A0\u8F7D\u5C06<bold>\u66FF\u6362\u60A8\u73B0\u6709\u7684\u5185\u5BB9</bold>\u3002<br></br>\u60A8\u53EF\u4EE5\u5148\u4F7F\u7528\u4E0B\u5217\u65B9\u5F0F\u5907\u4EFD\u60A8\u7684\u7ED8\u56FE\u3002"
    },
    shareableLink: {
      title: "\u4ECE\u94FE\u63A5\u52A0\u8F7D",
      button: "\u66FF\u6362\u6211\u7684\u5185\u5BB9",
      description: "\u52A0\u8F7D\u5916\u90E8\u7ED8\u56FE\u5C06<bold>\u66FF\u6362\u60A8\u73B0\u6709\u7684\u5185\u5BB9</bold>\u3002<br></br>\u60A8\u53EF\u4EE5\u5148\u4F7F\u7528\u4E0B\u5217\u65B9\u5F0F\u5907\u4EFD\u60A8\u7684\u7ED8\u56FE\u3002"
    }
  }
};
var mermaid = {
  title: "Mermaid \u81F3 Excalidraw",
  button: "\u63D2\u5165",
  description: "\u76EE\u524D\u4EC5\u652F\u6301<flowchartLink>\u6D41\u7A0B\u56FE</flowchartLink>\u3001<sequenceLink>\u5E8F\u5217\u56FE</sequenceLink>\u548C<classLink>\u7C7B\u56FE</classLink>\u3002\u5176\u4ED6\u7C7B\u578B\u5728 Excalidraw \u4E2D\u5C06\u4EE5\u56FE\u50CF\u5448\u73B0\u3002",
  syntax: "Mermaid \u8BED\u6CD5",
  preview: "\u9884\u89C8"
};
var zh_CN_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  zh_CN_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=zh-CN-4MXUOFTH.js.map
