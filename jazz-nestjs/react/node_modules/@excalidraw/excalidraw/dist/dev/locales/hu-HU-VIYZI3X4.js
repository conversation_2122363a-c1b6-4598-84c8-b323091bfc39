import "../chunk-XDFCUUT6.js";

// locales/hu-HU.json
var labels = {
  paste: "Beilleszt\xE9s",
  pasteAsPlaintext: "Beilleszt\xE9s form\xE1zatlan sz\xF6vegk\xE9nt",
  pasteCharts: "Grafikon beilleszt\xE9se",
  selectAll: "\xD6sszes kijel\xF6l\xE9se",
  multiSelect: "Elem hozz\xE1ad\xE1sa a kijel\xF6l\xE9shez",
  moveCanvas: "V\xE1szon mozgat\xE1sa",
  cut: "Kiv\xE1g\xE1s",
  copy: "M\xE1sol\xE1s",
  copyAsPng: "V\xE1g\xF3lapra m\xE1sol\xE1s mint PNG",
  copyAsSvg: "V\xE1g\xF3lapra m\xE1sol\xE1s mint SVG",
  copyText: "V\xE1g\xF3lapra m\xE1sol\xE1s sz\xF6vegk\xE9nt",
  copySource: "",
  convertToCode: "",
  bringForward: "El\u0151r\xE9bb hoz\xE1s",
  sendToBack: "H\xE1trak\xFCld\xE9s",
  bringToFront: "El\u0151rehoz\xE1s",
  sendBackward: "H\xE1tr\xE9bb k\xFCld\xE9s",
  delete: "T\xF6rl\xE9s",
  copyStyles: "St\xEDlus m\xE1sol\xE1sa",
  pasteStyles: "St\xEDlus beilleszt\xE9se",
  stroke: "K\xF6rvonal",
  background: "H\xE1tt\xE9r",
  fill: "Kit\xF6lt\xE9s",
  strokeWidth: "K\xF6rvonal vastags\xE1ga",
  strokeStyle: "K\xF6rvonal st\xEDlusa",
  strokeStyle_solid: "Kit\xF6lt\xF6tt",
  strokeStyle_dashed: "Szaggatott",
  strokeStyle_dotted: "Pontozott",
  sloppiness: "St\xEDlus",
  opacity: "\xC1ttetsz\u0151s\xE9g",
  textAlign: "Sz\xF6veg igaz\xEDt\xE1sa",
  edges: "Sz\xE9lek",
  sharp: "\xC9les",
  round: "Kerek",
  arrowheads: "Ny\xEDlhegyek",
  arrowhead_none: "Nincs",
  arrowhead_arrow: "Ny\xEDl",
  arrowhead_bar: "Oszlop",
  arrowhead_circle: "",
  arrowhead_circle_outline: "",
  arrowhead_triangle: "H\xE1romsz\xF6g",
  arrowhead_triangle_outline: "",
  arrowhead_diamond: "",
  arrowhead_diamond_outline: "",
  fontSize: "Bet\u0171m\xE9ret",
  fontFamily: "Bet\u0171k\xE9szlet csal\xE1d",
  addWatermark: 'Add hozz\xE1, hogy "Excalidraw-val k\xE9sz\xFClt"',
  handDrawn: "K\xE9zzel rajzolt",
  normal: "Norm\xE1l",
  code: "K\xF3d",
  small: "Kicsi",
  medium: "K\xF6zepes",
  large: "Nagy",
  veryLarge: "Nagyon nagy",
  solid: "Kit\xF6lt\xF6tt",
  hachure: "Vonalk\xE1zott",
  zigzag: "Cikkcakk",
  crossHatch: "Keresztcs\xEDkozott",
  thin: "V\xE9kony",
  bold: "F\xE9lk\xF6v\xE9r",
  left: "Bal",
  center: "K\xF6z\xE9p",
  right: "Jobb",
  extraBold: "Extra F\xE9lk\xF6v\xE9r",
  architect: "Tervez\u0151i",
  artist: "M\u0171v\xE9szi",
  cartoonist: "Karikat\xFAr\xE1s",
  fileTitle: "F\xE1jln\xE9v",
  colorPicker: "Sz\xEDnv\xE1laszt\xF3",
  canvasColors: "Rajzv\xE1szonon haszn\xE1lt",
  canvasBackground: "V\xE1szon h\xE1tt\xE9rsz\xEDne",
  drawingCanvas: "Rajzv\xE1szon",
  layers: "R\xE9tegek",
  actions: "M\u0171veletek",
  language: "Nyelv",
  liveCollaboration: "\xC9l\u0151 egy\xFCttm\u0171k\xF6d\xE9s...",
  duplicateSelection: "Duplik\xE1l\xE1s",
  untitled: "N\xE9vtelen",
  name: "N\xE9v",
  yourName: "Neved",
  madeWithExcalidraw: "Excalidraw-val k\xE9sz\xFClt",
  group: "Csoportos\xEDt\xE1s",
  ungroup: "Csoportbont\xE1s",
  collaborators: "K\xF6zrem\u0171k\xF6d\u0151k",
  showGrid: "R\xE1cs megjelen\xEDt\xE9se",
  addToLibrary: "Hozz\xE1ad\xE1s a k\xF6nyvt\xE1rhoz",
  removeFromLibrary: "Elt\xE1v\xF3l\xEDt\xE1s a k\xF6nyvt\xE1rb\xF3l",
  libraryLoadingMessage: "K\xF6nyvt\xE1r bet\xF6lt\xE9se\u2026",
  libraries: "K\xF6nyvt\xE1rak b\xF6ng\xE9sz\xE9se",
  loadingScene: "Jelenet bet\xF6lt\xE9se\u2026",
  align: "Igaz\xEDt\xE1s",
  alignTop: "Fel\xFClre igaz\xEDt\xE1s",
  alignBottom: "Alulra igaz\xEDt\xE1s",
  alignLeft: "Balra igaz\xEDt\xE1s",
  alignRight: "Jobbra igaz\xEDt\xE1s",
  centerVertically: "F\xFCgg\u0151legesen k\xF6z\xE9pre igaz\xEDtott",
  centerHorizontally: "V\xEDzszintesen k\xF6z\xE9pre igaz\xEDtott",
  distributeHorizontally: "V\xEDzszintes eloszt\xE1s",
  distributeVertically: "F\xFCgg\u0151leges eloszt\xE1s",
  flipHorizontal: "V\xEDzszintes t\xFCkr\xF6z\xE9s",
  flipVertical: "F\xFCgg\u0151leges t\xFCkr\xF6z\xE9s",
  viewMode: "N\xE9zet",
  share: "Megoszt\xE1s",
  showStroke: "K\xF6rvonal sz\xEDnv\xE1laszt\xF3 megjelen\xEDt\xE9se",
  showBackground: "H\xE1tt\xE9rsz\xEDn-v\xE1laszt\xF3 megjelen\xEDt\xE9se",
  toggleTheme: "T\xE9ma v\xE1lt\xE1sa",
  personalLib: "Szem\xE9lyes k\xF6nyvt\xE1r",
  excalidrawLib: "Excalidraw k\xF6nyvt\xE1r",
  decreaseFontSize: "Bet\u0171m\xE9ret cs\xF6kkent\xE9se",
  increaseFontSize: "Bet\u0171m\xE9ret n\xF6vel\xE9se",
  unbindText: "Sz\xF6vegk\xF6t\xE9s felold\xE1sa",
  bindText: "",
  createContainerFromText: "Sz\xF6veg bekeretez\xE9se",
  link: {
    edit: "Hivatkoz\xE1s szerkeszt\xE9se",
    editEmbed: "Link szerkeszt\xE9se / be\xE1gyaz\xE1sa",
    create: "Hivatkoz\xE1s l\xE9trehoz\xE1sa",
    createEmbed: "Link l\xE9trehoz\xE1sa / be\xE1gyaz\xE1sa",
    label: "Hivatkoz\xE1s",
    labelEmbed: "",
    empty: ""
  },
  lineEditor: {
    edit: "",
    exit: ""
  },
  elementLock: {
    lock: "R\xF6gz\xEDt\xE9s",
    unlock: "R\xF6gz\xEDt\xE9s felold\xE1sa",
    lockAll: "\xD6sszes r\xF6gz\xEDt\xE9se",
    unlockAll: "\xD6sszes felold\xE1sa"
  },
  statusPublished: "",
  sidebarLock: "",
  selectAllElementsInFrame: "",
  removeAllElementsFromFrame: "",
  eyeDropper: "",
  textToDiagram: "",
  prompt: ""
};
var library = {
  noItems: "",
  hint_emptyLibrary: "",
  hint_emptyPrivateLibrary: ""
};
var buttons = {
  clearReset: "V\xE1szon t\xF6rl\xE9se",
  exportJSON: "Export\xE1l\xE1s f\xE1jlba",
  exportImage: "K\xE9p export\xE1l\xE1sa...",
  export: "Ment\xE9s m\xE1sk\xE9nt...",
  copyToClipboard: "V\xE1g\xF3lapra m\xE1sol\xE1s",
  save: "Ment\xE9s az aktu\xE1lis f\xE1jlba",
  saveAs: "Ment\xE9s m\xE1sk\xE9nt",
  load: "Megnyit\xE1s",
  getShareableLink: "Megoszthat\xF3 link l\xE9trehoz\xE1sa",
  close: "Bez\xE1r\xE1s",
  selectLanguage: "Nyelv kiv\xE1laszt\xE1sa",
  scrollBackToContent: "Visszag\xF6rget\xE9s a tartalomhoz",
  zoomIn: "Nagy\xEDt\xE1s",
  zoomOut: "Kicsiny\xEDt\xE9s",
  resetZoom: "Nagy\xEDt\xE1s alaphelyzetbe",
  menu: "Men\xFC",
  done: "K\xE9sz",
  edit: "Szerkeszt\xE9s",
  undo: "Vissza",
  redo: "\xDAjra",
  resetLibrary: "K\xF6nyvt\xE1r alaphelyzetbe \xE1ll\xEDt\xE1sa",
  createNewRoom: "\xDAj szoba l\xE9trehoz\xE1sa",
  fullScreen: "Teljes k\xE9perny\u0151",
  darkMode: "S\xF6t\xE9t m\xF3d",
  lightMode: "Vil\xE1gos m\xF3d",
  zenMode: "Letisztult m\xF3d",
  objectsSnapMode: "",
  exitZenMode: "Kil\xE9p\xE9s a letisztult m\xF3db\xF3l",
  cancel: "M\xE9gsem",
  clear: "Ki\u0171r\xEDt\xE9s",
  remove: "Elt\xE1vol\xEDt\xE1s",
  embed: "",
  publishLibrary: "K\xF6zz\xE9t\xE9tel",
  submit: "Elk\xFCld\xE9s",
  confirm: "Meger\u0151s\xEDt\xE9s",
  embeddableInteractionButton: ""
};
var alerts = {
  clearReset: "Ez a m\u0171velet t\xF6rli a v\xE1szont. Biztos benne?",
  couldNotCreateShareableLink: "Nem siker\xFClt megoszthat\xF3 linket l\xE9trehozni.",
  couldNotCreateShareableLinkTooBig: "Nem siker\xFClt megoszthat\xF3 linket l\xE1trehozni: t\xFAl nagy a jelenet",
  couldNotLoadInvalidFile: "Nem siker\xFClt bet\xF6lteni a helytelen f\xE1jlt",
  importBackendFailed: "Nem siker\xFClt bet\xF6lteni a szerverr\u0151l.",
  cannotExportEmptyCanvas: "\xDCres v\xE1szont nem lehet export\xE1lni.",
  couldNotCopyToClipboard: "",
  decryptFailed: "Nem siker\xFClt visszafejteni a titkos\xEDtott adatot.",
  uploadedSecurly: "A felt\xF6lt\xE9st v\xE9gpontok k\xF6z\xF6tti titkos\xEDt\xE1ssal biztos\xEDtottuk, ami azt jelenti, hogy egy harmadik f\xE9l nem tudja megn\xE9zni a tartalm\xE1t, bele\xE9rtve az Excalidraw szervereit is.",
  loadSceneOverridePrompt: "A bet\xF6lt\xF6tt k\xFCls\u0151 rajz fel\xFCl fogja \xEDrnia megl\xE9v\u0151t. Szeretn\xE9d folytatni?",
  collabStopOverridePrompt: "A munkamenet le\xE1ll\xEDt\xE1sa fel\xFCl fogja \xEDrni az el\u0151z\u0151leg helyben t\xE1rolt rajzot. Biztosan ezt akarod?\n(Ha meg akarod tartani a helyben t\xE1rolt rajzot, egyszer\u0171en csak z\xE1rd be a b\xF6ng\xE9sz\u0151 f\xFClet)",
  errorAddingToLibrary: "A t\xE9tel nem addhat\xF3 hozz\xE1 a k\xF6nyvt\xE1rhoz",
  errorRemovingFromLibrary: "A t\xE9tel nem t\xE1vol\xEDthat\xF3 el a k\xF6nyvt\xE1rb\xF3l",
  confirmAddLibrary: "Ez a m\u0171velet {{numShapes}} form\xE1t fog hozz\xE1adni a k\xF6nyvt\xE1radhoz. Biztos vagy benne?",
  imageDoesNotContainScene: "\xDAgy t\u0171nik, hogy ez a k\xE9p nem tartalmaz jelenetadatokat. Enged\xE9lyezted a jelenetbe\xE1gyaz\xE1st az export\xE1l\xE1s sor\xE1n?",
  cannotRestoreFromImage: "A jelenet vissza\xE1ll\xEDt\xE1sa nem siker\xFClt ebb\u0151l a k\xE9p f\xE1jlb\xF3l",
  invalidSceneUrl: "Nem siker\xFClt import\xE1lni a jelenetet a megadott URL-r\u0151l. Rossz form\xE1tum\xFA, vagy nem tartalmaz \xE9rv\xE9nyes Excalidraw JSON-adatokat.",
  resetLibrary: "Ezzel t\xF6rl\xF6d a k\xF6nyvt\xE1r\xE1t. biztos vagy ebben?",
  removeItemsFromsLibrary: "{{count}} elemet t\xF6r\xF6lsz a k\xF6nyvt\xE1rb\xF3l?",
  invalidEncryptionKey: "A titkos\xEDt\xE1si kulcsnak 22 karakterb\u0151l kell \xE1llnia. Az \xE9l\u0151 egy\xFCttm\u0171k\xF6d\xE9s le van tiltva.",
  collabOfflineWarning: ""
};
var errors = {
  unsupportedFileType: "Nem t\xE1mogatott f\xE1jlt\xEDpus.",
  imageInsertError: "Nem siker\xFClt besz\xFArni a k\xE9pet. Pr\xF3b\xE1ld \xFAjra k\xE9s\u0151bb...",
  fileTooBig: "A f\xE1jl t\xFAl nagy. A megengedett maxim\xE1lis m\xE9ret {{maxSize}}.",
  svgImageInsertError: "Nem siker\xFClt besz\xFArni az SVG-k\xE9pet. Az SVG szintaktika \xE9rv\xE9nytelennek t\u0171nik.",
  failedToFetchImage: "",
  invalidSVGString: "\xC9rv\xE9nytelen SVG.",
  cannotResolveCollabServer: "",
  importLibraryError: "",
  collabSaveFailed: "",
  collabSaveFailed_sizeExceeded: "",
  imageToolNotSupported: "",
  brave_measure_text_error: {
    line1: "",
    line2: "",
    line3: "",
    line4: ""
  },
  libraryElementTypeError: {
    embeddable: "",
    iframe: "",
    image: ""
  },
  asyncPasteFailedOnRead: "",
  asyncPasteFailedOnParse: "",
  copyToSystemClipboardFailed: ""
};
var toolBar = {
  selection: "Kijel\xF6l\xE9s",
  image: "K\xE9p besz\xFAr\xE1sa",
  rectangle: "T\xE9glalap",
  diamond: "Rombusz",
  ellipse: "Ellipszis",
  arrow: "Ny\xEDl",
  line: "Vonal",
  freedraw: "Rajzol\xE1s",
  text: "Sz\xF6veg",
  library: "K\xF6nyvt\xE1r",
  lock: "Rajzol\xE1s ut\xE1n az akt\xEDv eszk\xF6zt tartsa kijel\xF6lve",
  penMode: "",
  link: "Hivatkoz\xE1s hozz\xE1ad\xE1sa/friss\xEDt\xE9se a kiv\xE1lasztott alakzathoz",
  eraser: "Rad\xEDr",
  frame: "",
  magicframe: "",
  embeddable: "Weblap be\xE1gyaz\xE1sa",
  laser: "L\xE9zermutat\xF3",
  hand: "",
  extraTools: "Tov\xE1bbi eszk\xF6z\xF6k",
  mermaidToExcalidraw: "",
  magicSettings: ""
};
var headings = {
  canvasActions: "V\xE1szon m\u0171veletek",
  selectedShapeActions: "Kijel\xF6lt forma m\u0171veletei",
  shapes: "Alakzatok"
};
var hints = {
  canvasPanning: "",
  linearElement: "Kattint\xE1ssal g\xF6rbe, az eger h\xFAz\xE1s\xE1val pedig egyenes nyilat rajzolhatsz",
  freeDraw: "Kattints \xE9s h\xFAzd, majd engedd el, amikor v\xE9gezt\xE9l",
  text: "Tipp: A kijel\xF6l\xE9s eszk\xF6zzel a dupla kattint\xE1s \xFAj sz\xF6veget hoz l\xE9tre",
  embeddable: "",
  text_selected: "Kattints dupl\xE1n, vagy nyomj entert a sz\xF6veg szerkeszt\xE9s\xE9hez",
  text_editing: "Nyomjd meg az Escape vagy a Ctrl/Cmd+ENTER billenty\u0171kombin\xE1ci\xF3t a szerkeszt\xE9s befejez\xE9s\xE9hez",
  linearElementMulti: "Kattints a k\xF6vetkez\u0151 \xEDv poz\xEDci\xF3j\xE1ra, vagy fejezd be a nyilat az Escape vagy Enter megnyom\xE1s\xE1val",
  lockAngle: "A SHIFT billenty\u0171 lenyomva tart\xE1s\xE1val korl\xE1tozhatja forgat\xE1s sz\xF6g\xE9t",
  resize: "A SHIFT billenty\u0171 lenyomva tart\xE1s\xE1val az \xE1tm\xE9retez\xE9s megtartja az ar\xE1nyokat,\naz ALT lenyomva tart\xE1s\xE1val pedig a k\xF6z\xE9ppont egy helyben marad",
  resizeImage: "A SHIFT billenty\u0171 lenyomva tart\xE1s\xE1val szabadon \xE1tm\xE9retezheted,\ntartsd lenyomva az ALT billenty\u0171t a k\xF6z\xE9pr\u0151l val\xF3 \xE1tm\xE9retez\xE9shez",
  rotate: "A SHIFT billenty\u0171 lenyomva tart\xE1s\xE1val korl\xE1tozhatja a sz\xF6gek illeszt\xE9s\xE9t",
  lineEditor_info: "",
  lineEditor_pointSelected: "Nyomd meg a T\xF6rl\xE9s gombot a pont(ok) elt\xE1vol\xEDt\xE1s\xE1hoz,\nA Ctrl/Cmd+D a t\xF6bbsz\xF6r\xF6z\xE9shez, vagy h\xFAz\xE1ssal mozgathatja",
  lineEditor_nothingSelected: "V\xE1laszd ki a szerkeszteni k\xEDv\xE1nt pontot (t\xF6bb kijel\xF6l\xE9s\xE9hez tartsd lenyomva a SHIFT billenty\u0171t),\nvagy Alt, \xE9s kattintson az \xFAj pontok hozz\xE1ad\xE1s\xE1hoz",
  placeImage: "Kattints a k\xE9p elhelyez\xE9s\xE9hez, vagy kattints \xE9s m\xE9retezd manu\xE1lisan",
  publishLibrary: "Tedd k\xF6zz\xE9 saj\xE1t k\xF6nyvt\xE1radat",
  bindTextToElement: "Nyomd meg az Entert sz\xF6veg hozz\xE1ad\xE1shoz",
  deepBoxSelect: "Tartsd lenyomva a Ctrl/Cmd billenty\u0171t a m\xE9ly kijel\xF6l\xE9shez \xE9s a h\xFAz\xE1s megakad\xE1lyoz\xE1s\xE1hoz",
  eraserRevert: "",
  firefox_clipboard_write: "",
  disableSnapping: ""
};
var canvasError = {
  cannotShowPreview: "El\u0151n\xE9zet nem jelen\xEDthet\u0151 meg",
  canvasTooBig: "A v\xE1szon tal\xE1n t\xFAl nagy.",
  canvasTooBigTip: "Tipp: pr\xF3b\xE1ld meg a legt\xE1volabbi elemeket k\xF6zelebb hozni egy m\xE1shoz."
};
var errorSplash = {
  headingMain: "Hiba t\xF6rt\xE9nt. Pr\xF3b\xE1ld <button>\xFAjrat\xF6lteni az oldalt.</button>",
  clearCanvasMessage: "Ha az \xFAjrat\xF6lt\xE9s nem m\u0171k\xF6dik, pr\xF3b\xE1ld <button>let\xF6r\xF6lni a v\xE1szont.</button>",
  clearCanvasCaveat: " Ezzel az eddigi munka elveszik ",
  trackedToSentry: "A hibak\xF3d azonos\xEDt\xF3val {{eventId}} nyomon van k\xF6vetve a rendszer\xFCnkben.",
  openIssueMessage: "Vigy\xE1ztunk arra, hogy a jelenthez tartoz\xF3 inform\xE1ci\xF3 ne jelenjen meg a hiba\xFCzenetben. Ha a jeleneted nem bizalmas, k\xE9rj\xFCk add hozz\xE1 a <button>hibak\xF6vet\u0151 rendszer\xFCnkh\xF6z.</button> K\xE9rj\xFCk, m\xE1solja be az al\xE1bbi inform\xE1ci\xF3kat a GitHub probl\xE9m\xE1ba.",
  sceneContent: "Jelenet tartalma:"
};
var roomDialog = {
  desc_intro: "Megh\xEDvhat embereket a jelenlegi jelenetbe, hogy egy\xFCttm\u0171k\xF6djenek \xF6nnel.",
  desc_privacy: "Ne agg\xF3dj, a munkamenet v\xE9gpontok k\xF6z\xF6tti titkos\xEDt\xE1st haszn\xE1l, teh\xE1t b\xE1rmit rajzolsz, priv\xE1t marad. M\xE9g a szerver\xFCnkr\u0151l se lehet belen\xE9zni.",
  button_startSession: "Munkamenet ind\xEDt\xE1sa",
  button_stopSession: "Munkamenet le\xE1ll\xEDt\xE1sa",
  desc_inProgressIntro: "Az \xE9l\u0151 egy\xFCttm\u0171k\xF6d\xE9si munkamenet folyamatban van.",
  desc_shareLink: "Ossza meg ezt a linket b\xE1rkivel, akivel egy\xFCtt szeretne m\u0171k\xF6dni:",
  desc_exitSession: "Az munkamenet le\xE1ll\xEDt\xE1sa kil\xE9pteti \xF6nt a szob\xE1b\xF3l, de folytathatja a munk\xE1t a saj\xE1t g\xE9p\xE9n. Vegye figyelembe, hogy ez nem \xE9rinti m\xE1s emberek munk\xE1j\xE1t \xE9s \u0151k tov\xE1bbra is egy\xFCttm\u0171k\xF6dhetnek a saj\xE1t v\xE1ltozatukon.",
  shareTitle: "Csatlakoz\xE1s egy \xE9l\u0151 egy\xFCttm\u0171k\xF6d\xE9si munkamenethez az Excalidraw-ban"
};
var errorDialog = {
  title: "Hiba"
};
var exportDialog = {
  disk_title: "Ment\xE9s lemezre",
  disk_details: "Export\xE1lja a jelenetadatokat egy f\xE1jlba, amelyb\u0151l k\xE9s\u0151bb import\xE1lhatja.",
  disk_button: "Ment\xE9s f\xE1jlba",
  link_title: "Megoszthat\xF3 hivatkoz\xE1s",
  link_details: "Export\xE1l\xE1s csak olvashat\xF3 hivatkoz\xE1sk\xE9nt.",
  link_button: "Export\xE1l\xE1s hivatkoz\xE1sba",
  excalidrawplus_description: "Mentse el a jelenetet az Excalidraw+ munkater\xFClet\xE9re.",
  excalidrawplus_button: "Export\xE1l\xE1s",
  excalidrawplus_exportError: "Jelenleg nem lehet export\xE1lni az Excalidraw+-ba..."
};
var helpDialog = {
  blog: "Olvasd a blogunkat",
  click: "kattint\xE1s",
  deepSelect: "M\xE9ly kijel\xF6l\xE9s",
  deepBoxSelect: "M\xE9ly kijel\xF6l\xE9s a dobozon bel\xFCl, \xE9s a h\xFAz\xE1s megakad\xE1lyoz\xE1sa",
  curvedArrow: "\xCDvelt ny\xEDl",
  curvedLine: "\xCDvelt vonal",
  documentation: "Dokument\xE1ci\xF3",
  doubleClick: "dupla kattint\xE1s",
  drag: "vonszol\xE1s",
  editor: "Szerkeszt\u0151",
  editLineArrowPoints: "",
  editText: "",
  github: "Hib\xE1t tal\xE1lt\xE1l? K\xFCld be",
  howto: "K\xF6vesd az \xFAtmutat\xF3inkat",
  or: "vagy",
  preventBinding: "A ny\xEDl ne ragadjon",
  tools: "",
  shortcuts: "Gyorsbillenty\u0171k",
  textFinish: "Szerkeszt\xE9s befejez\xE9se (sz\xF6veg)",
  textNewLine: "\xDAj sor hozz\xE1ad\xE1sa (sz\xF6veg)",
  title: "Seg\xEDts\xE9g",
  view: "N\xE9zet",
  zoomToFit: "Az \xF6sszes elem l\xE1t\xF3t\xE9rbe hoz\xE1sa",
  zoomToSelection: "Kijel\xF6l\xE9sre nagy\xEDt\xE1s",
  toggleElementLock: "",
  movePageUpDown: "",
  movePageLeftRight: ""
};
var clearCanvasDialog = {
  title: "Rajzv\xE1szon alaphelyzetbe"
};
var publishDialog = {
  title: "K\xF6nyvt\xE1r k\xF6zz\xE9t\xE9tele",
  itemName: "T\xE9tel neve",
  authorName: "Szerz\u0151 neve",
  githubUsername: "GitHub felhaszn\xE1l\xF3n\xE9v",
  twitterUsername: "Twitter felhaszn\xE1l\xF3n\xE9v",
  libraryName: "K\xF6nyvt\xE1r neve",
  libraryDesc: "K\xF6nyvt\xE1r le\xEDr\xE1sa",
  website: "Weboldal",
  placeholder: {
    authorName: "Neved vagy felhaszn\xE1l\xF3neved",
    libraryName: "A k\xF6nyvt\xE1rad neve",
    libraryDesc: "A k\xF6nyvt\xE1rad haszn\xE1lat\xE1t seg\xEDt\u0151 le\xEDr\xE1s",
    githubHandle: "GitHub-handle(opcion\xE1lis), \xEDgy szerkesztheted a k\xF6nyvt\xE1rat, miut\xE1n elk\xFCldted ellen\u0151rz\xE9sre",
    twitterHandle: "Twitter-felhaszn\xE1l\xF3n\xE9v (opcion\xE1lis), \xEDgy tudjuk, kinek kell j\xF3v\xE1\xEDrni a Twitteren kereszt\xFCli rekl\xE1moz\xE1st",
    website: "Hivatkoz\xE1s szem\xE9lyes webhelyedre vagy m\xE1shov\xE1 (nem k\xF6telez\u0151)"
  },
  errors: {
    required: "K\xF6telez\u0151",
    website: "Adj meg egy \xE9rv\xE9nyes URL-t"
  },
  noteDescription: "K\xFCld be k\xF6nyvt\xE1radat, hogy beker\xFClj\xF6n a <link>nyilv\xE1nos k\xF6nyvt\xE1r t\xE1rol\xF3ba</link>hogy m\xE1sok is felhaszn\xE1lhass\xE1k a rajzaikban.",
  noteGuidelines: "A k\xF6nyvt\xE1rat el\u0151sz\xF6r manu\xE1lisan kell j\xF3v\xE1hagyni. K\xE9rj\xFCk, olvassa el a <link>seg\xE9dletet</link> beny\xFAjt\xE1sa el\u0151tt. Sz\xFCks\xE9ge lesz egy GitHub-fi\xF3kra a kommunik\xE1ci\xF3hoz \xE9s a m\xF3dos\xEDt\xE1sokhoz, ha k\xE9rik, de ez nem felt\xE9tlen\xFCl sz\xFCks\xE9ges.",
  noteLicense: "A bek\xFCld\xE9ssel elfogadja, hogy a k\xF6nyvt\xE1r a k\xF6vetkez\u0151 alatt ker\xFCl k\xF6zz\xE9t\xE9telre <link>MIT Licensz </link>ami r\xF6viden azt jelenti, hogy b\xE1rki korl\xE1toz\xE1s n\xE9lk\xFCl haszn\xE1lhatja \u0151ket.",
  noteItems: "Minden k\xF6nyvt\xE1relemnek saj\xE1t nev\xE9vel kell rendelkeznie, hogy sz\u0171rhet\u0151 legyen. A k\xF6vetkez\u0151 k\xF6nyvt\xE1ri t\xE9telek ker\xFClnek bele:",
  atleastOneLibItem: "A kezd\xE9shez v\xE1lassz ki legal\xE1bb egy k\xF6nyvt\xE1ri elemet",
  republishWarning: ""
};
var publishSuccessDialog = {
  title: "A k\xF6nyvt\xE1r bek\xFCldve",
  content: "K\xF6sz\xF6nj\xFCk {{authorName}}. K\xF6nyvt\xE1radat elk\xFCldt\xFCk fel\xFClvizsg\xE1latra. Nyomon k\xF6vetheted az \xE1llapotot<link>itt</link>"
};
var confirmDialog = {
  resetLibrary: "K\xF6nyvt\xE1r alaphelyzetbe \xE1ll\xEDt\xE1sa",
  removeItemsFromLib: "A kiv\xE1lasztott elemek elt\xE1vol\xEDt\xE1sa a k\xF6nyvt\xE1rb\xF3l"
};
var imageExportDialog = {
  header: "K\xE9p export\xE1l\xE1sa",
  label: {
    withBackground: "",
    onlySelected: "",
    darkMode: "",
    embedScene: "",
    scale: "",
    padding: ""
  },
  tooltip: {
    embedScene: ""
  },
  title: {
    exportToPng: "",
    exportToSvg: "",
    copyPngToClipboard: ""
  },
  button: {
    exportToPng: "PNG",
    exportToSvg: "SVG",
    copyPngToClipboard: "V\xE1g\xF3lapra m\xE1sol\xE1s"
  }
};
var encrypted = {
  tooltip: "A rajzaidat v\xE9gpontok k\xF6z\xF6tti titkos\xEDt\xE1ssal t\xE1roljuk, teh\xE1t az Excalidraw szervereir\u0151l se tud m\xE1s belen\xE9zni.",
  link: "Blogbejegyz\xE9s a v\xE9gpontok k\xF6z\xF6tti titkos\xEDt\xE1sr\xF3l az Excalidraw-ban"
};
var stats = {
  angle: "Sz\xF6g",
  element: "Elem",
  elements: "Elemek",
  height: "Magass\xE1g",
  scene: "Jelenet",
  selected: "Kijel\xF6lt",
  storage: "T\xE1rhely",
  title: "Statisztik\xE1k",
  total: "\xD6sszesen",
  version: "Verzi\xF3",
  versionCopy: "Kattints a m\xE1sol\xE1shoz",
  versionNotAvailable: "A verzi\xF3 nem el\xE9rhet\u0151",
  width: "Sz\xE9less\xE9g"
};
var toast = {
  addedToLibrary: "K\xF6nyvt\xE1rhoz adva",
  copyStyles: "M\xE1solt st\xEDlusok.",
  copyToClipboard: "V\xE1g\xF3lapra m\xE1solva.",
  copyToClipboardAsPng: "Az {{exportSelection}} PNG form\xE1tumban a v\xE1g\xF3lapra m\xE1solva \n({{exportColorScheme}})",
  fileSaved: "F\xE1jl elmentve.",
  fileSavedToFilename: "Mentve mint {filename}",
  canvas: "rajzv\xE1szon",
  selection: "kijel\xF6l\xE9s",
  pasteAsSingleElement: "",
  unableToEmbed: "",
  unrecognizedLinkFormat: ""
};
var colors = {
  transparent: "\xC1tl\xE1tsz\xF3",
  black: "Fekete",
  white: "Feh\xE9r",
  red: "Piros",
  pink: "R\xF3zsasz\xEDn",
  grape: "",
  violet: "Ibolya",
  gray: "Sz\xFCrke",
  blue: "K\xE9k",
  cyan: "Ci\xE1n",
  teal: "K\xE9kes-z\xF6ld",
  green: "Z\xF6ld",
  yellow: "S\xE1rga",
  orange: "Narancss\xE1rga",
  bronze: "Bronz"
};
var welcomeScreen = {
  app: {
    center_heading: "",
    center_heading_plus: "",
    menuHint: ""
  },
  defaults: {
    menuHint: "",
    center_heading: "",
    toolbarHint: "",
    helpHint: ""
  }
};
var colorPicker = {
  mostUsedCustomColors: "",
  colors: "",
  shades: "",
  hexCode: "Hexadecim\xE1lis k\xF3d",
  noShades: ""
};
var overwriteConfirm = {
  action: {
    exportToImage: {
      title: "Export\xE1l\xE1s k\xE9pk\xE9nt",
      button: "Export\xE1l\xE1s k\xE9pk\xE9nt",
      description: ""
    },
    saveToDisk: {
      title: "Ment\xE9s a lemezre",
      button: "Ment\xE9s a lemezre",
      description: ""
    },
    excalidrawPlus: {
      title: "Excalidraw+",
      button: "",
      description: ""
    }
  },
  modal: {
    loadFromFile: {
      title: "Bet\xF6lt\xE9s f\xE1jlb\xF3l",
      button: "Bet\xF6lt\xE9s f\xE1jlb\xF3l",
      description: ""
    },
    shareableLink: {
      title: "Felt\xF6lt\xE1s linkb\u0151l",
      button: "",
      description: ""
    }
  }
};
var mermaid = {
  title: "",
  button: "",
  description: "",
  syntax: "",
  preview: ""
};
var hu_HU_default = {
  labels,
  library,
  buttons,
  alerts,
  errors,
  toolBar,
  headings,
  hints,
  canvasError,
  errorSplash,
  roomDialog,
  errorDialog,
  exportDialog,
  helpDialog,
  clearCanvasDialog,
  publishDialog,
  publishSuccessDialog,
  confirmDialog,
  imageExportDialog,
  encrypted,
  stats,
  toast,
  colors,
  welcomeScreen,
  colorPicker,
  overwriteConfirm,
  mermaid
};
export {
  alerts,
  buttons,
  canvasError,
  clearCanvasDialog,
  colorPicker,
  colors,
  confirmDialog,
  hu_HU_default as default,
  encrypted,
  errorDialog,
  errorSplash,
  errors,
  exportDialog,
  headings,
  helpDialog,
  hints,
  imageExportDialog,
  labels,
  library,
  mermaid,
  overwriteConfirm,
  publishDialog,
  publishSuccessDialog,
  roomDialog,
  stats,
  toast,
  toolBar,
  welcomeScreen
};
//# sourceMappingURL=hu-HU-VIYZI3X4.js.map
