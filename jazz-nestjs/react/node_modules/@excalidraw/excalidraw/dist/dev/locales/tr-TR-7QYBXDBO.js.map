{"version": 3, "sources": ["../../../locales/tr-TR.json"], "sourcesContent": ["{\n  \"labels\": {\n    \"paste\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n    \"pasteAsPlaintext\": \"<PERSON><PERSON><PERSON> metin olarak yapıştır\",\n    \"pasteCharts\": \"Grafikleri yapıştır\",\n    \"selectAll\": \"<PERSON>ü<PERSON><PERSON><PERSON><PERSON> seç\",\n    \"multiSelect\": \"<PERSON>çime öge ekle\",\n    \"moveCanvas\": \"<PERSON>vali taşı\",\n    \"cut\": \"Kes\",\n    \"copy\": \"Kopyala\",\n    \"copyAsPng\": \"Panoya PNG olarak kopyala\",\n    \"copyAsSvg\": \"Panoya SVG olarak kopyala\",\n    \"copyText\": \"Panoya metin olarak kopyala\",\n    \"copySource\": \"\",\n    \"convertToCode\": \"\",\n    \"bringForward\": \"Bir öne getir\",\n    \"sendToBack\": \"Arkaya gönder\",\n    \"bringToFront\": \"En öne getir\",\n    \"sendBackward\": \"Bir geriye gönder\",\n    \"delete\": \"Sil\",\n    \"copyStyles\": \"<PERSON>eri kopyala\",\n    \"pasteStyles\": \"<PERSON>eri yapıştır\",\n    \"stroke\": \"<PERSON>urg<PERSON>\",\n    \"background\": \"Arka plan\",\n    \"fill\": \"Doldur\",\n    \"strokeWidth\": \"Kontur genişliği\",\n    \"strokeStyle\": \"Kontur stili\",\n    \"strokeStyle_solid\": \"Dolu\",\n    \"strokeStyle_dashed\": \"Kesik çizgili\",\n    \"strokeStyle_dotted\": \"Noktalı\",\n    \"sloppiness\": \"Üstün körülük\",\n    \"opacity\": \"Opaklık\",\n    \"textAlign\": \"Metin hizala\",\n    \"edges\": \"Kenarlar\",\n    \"sharp\": \"Keskin\",\n    \"round\": \"Yuvarlak\",\n    \"arrowheads\": \"Ok uçları\",\n    \"arrowhead_none\": \"Yok\",\n    \"arrowhead_arrow\": \"Ok\",\n    \"arrowhead_bar\": \"Çizgi\",\n    \"arrowhead_circle\": \"\",\n    \"arrowhead_circle_outline\": \"\",\n    \"arrowhead_triangle\": \"Üçgen\",\n    \"arrowhead_triangle_outline\": \"\",\n    \"arrowhead_diamond\": \"\",\n    \"arrowhead_diamond_outline\": \"\",\n    \"fontSize\": \"Yazı tipi boyutu\",\n    \"fontFamily\": \"Yazı tipi ailesi\",\n    \"addWatermark\": \"\\\"Excalidraw ile yapıldı\\\" yazısını ekle\",\n    \"handDrawn\": \"El-yazısı\",\n    \"normal\": \"Normal\",\n    \"code\": \"Kod\",\n    \"small\": \"Küçük\",\n    \"medium\": \"Orta\",\n    \"large\": \"Büyük\",\n    \"veryLarge\": \"Çok geniş\",\n    \"solid\": \"Dolu\",\n    \"hachure\": \"Taralı\",\n    \"zigzag\": \"Zikzak\",\n    \"crossHatch\": \"Çapraz-taralı\",\n    \"thin\": \"İnce\",\n    \"bold\": \"Kalın\",\n    \"left\": \"Sol\",\n    \"center\": \"Ortala\",\n    \"right\": \"Sağ\",\n    \"extraBold\": \"Ekstra kalın\",\n    \"architect\": \"Mimar\",\n    \"artist\": \"Sanatçı\",\n    \"cartoonist\": \"Karikatürist\",\n    \"fileTitle\": \"Dosya adı\",\n    \"colorPicker\": \"Renk seçici\",\n    \"canvasColors\": \"Tuvalin üzerinde kullanıldı\",\n    \"canvasBackground\": \"Tuval arka planı\",\n    \"drawingCanvas\": \"Çizim tuvali\",\n    \"layers\": \"Katmanlar\",\n    \"actions\": \"Eylemler\",\n    \"language\": \"Dil\",\n    \"liveCollaboration\": \"Canlı ortak çalışma alanı...\",\n    \"duplicateSelection\": \"Çoğalt\",\n    \"untitled\": \"Adsız\",\n    \"name\": \"İsim\",\n    \"yourName\": \"İsminiz\",\n    \"madeWithExcalidraw\": \"Excalidraw ile yapıldı\",\n    \"group\": \"Seçimi grup yap\",\n    \"ungroup\": \"Seçilen grubu dağıt\",\n    \"collaborators\": \"Ortaklar\",\n    \"showGrid\": \"Izgarayı göster\",\n    \"addToLibrary\": \"Kütüphaneye ekle\",\n    \"removeFromLibrary\": \"Kütüphaneden kaldır\",\n    \"libraryLoadingMessage\": \"Kütüphane yükleniyor…\",\n    \"libraries\": \"Kütüphanelere gözat\",\n    \"loadingScene\": \"Sahne yükleniyor…\",\n    \"align\": \"Hizala\",\n    \"alignTop\": \"Yukarı hizala\",\n    \"alignBottom\": \"Aşağı hizala\",\n    \"alignLeft\": \"Sola hizala\",\n    \"alignRight\": \"Sağa hizala\",\n    \"centerVertically\": \"Dikeyde ortala\",\n    \"centerHorizontally\": \"Yatayda ortala\",\n    \"distributeHorizontally\": \"Yatay dağıt\",\n    \"distributeVertically\": \"Dikey dağıt\",\n    \"flipHorizontal\": \"Yatay döndür\",\n    \"flipVertical\": \"Dikey döndür\",\n    \"viewMode\": \"Görünüm modu\",\n    \"share\": \"Paylaş\",\n    \"showStroke\": \"Kontur için renk seçiciyi göster\",\n    \"showBackground\": \"Arkaplan için renk seçiciyi göster\",\n    \"toggleTheme\": \"Temayı etkinleştir/devre dışı bırak\",\n    \"personalLib\": \"Kişisel Kitaplık\",\n    \"excalidrawLib\": \"Excalidraw Kitaplığı\",\n    \"decreaseFontSize\": \"Yazı Tipi Boyutunu Küçült\",\n    \"increaseFontSize\": \"Yazı Tipi Boyutunu Büyült\",\n    \"unbindText\": \"Metni çöz\",\n    \"bindText\": \"Metni taşıyıcıya bağla\",\n    \"createContainerFromText\": \"Metni bileşen içinde sar\",\n    \"link\": {\n      \"edit\": \"Bağlantıyı düzenle\",\n      \"editEmbed\": \"Bağlantıyı düzenle & yerleştir\",\n      \"create\": \"Bağlantı oluştur\",\n      \"createEmbed\": \"Bağlantı oluştur & yerleştir\",\n      \"label\": \"Bağlantı\",\n      \"labelEmbed\": \"Bağlantı & yerleştirme\",\n      \"empty\": \"Herhangi bir bağlantı oluşturulmadı\"\n    },\n    \"lineEditor\": {\n      \"edit\": \"Çizgiyi düzenle\",\n      \"exit\": \"Çizgi düzenlemeden çık\"\n    },\n    \"elementLock\": {\n      \"lock\": \"Kilitle\",\n      \"unlock\": \"Kilidi Kaldır\",\n      \"lockAll\": \"Hepsini kilitle\",\n      \"unlockAll\": \"Hepsinin kilidini kaldır\"\n    },\n    \"statusPublished\": \"Yayınlandı\",\n    \"sidebarLock\": \"Kenar çubuğu açık kalsın\",\n    \"selectAllElementsInFrame\": \"Çerçevedeki tüm bileşenleri seç\",\n    \"removeAllElementsFromFrame\": \"Çerçevedeki tüm bileşenleri sil\",\n    \"eyeDropper\": \"Tuvalden renk seç\",\n    \"textToDiagram\": \"\",\n    \"prompt\": \"\"\n  },\n  \"library\": {\n    \"noItems\": \"Öğe eklenmedi...\",\n    \"hint_emptyLibrary\": \"Öğelerden birini eklemek için öğeyi seçiniz veya aşağıdaki genel kütüphaneden öğeleri ekleyin.\",\n    \"hint_emptyPrivateLibrary\": \"Tuvalden bir eleman seçerek sayfaya ekleyin.\"\n  },\n  \"buttons\": {\n    \"clearReset\": \"Tuvali sıfırla\",\n    \"exportJSON\": \"Dosyaya aktar\",\n    \"exportImage\": \"Resimleri dışa aktar...\",\n    \"export\": \"Şuraya kaydet...\",\n    \"copyToClipboard\": \"Panoya kopyala\",\n    \"save\": \"Geçerli dosyaya kaydet\",\n    \"saveAs\": \"Farklı kaydet\",\n    \"load\": \"Aç\",\n    \"getShareableLink\": \"Paylaşılabilir bağlantı al\",\n    \"close\": \"Kapat\",\n    \"selectLanguage\": \"Dil seçin\",\n    \"scrollBackToContent\": \"İçeriğe geri dön\",\n    \"zoomIn\": \"Yakınlaştır\",\n    \"zoomOut\": \"Uzaklaştır\",\n    \"resetZoom\": \"Yakınlaştırmayı sıfırla\",\n    \"menu\": \"Menü\",\n    \"done\": \"Tamam\",\n    \"edit\": \"Düzenle\",\n    \"undo\": \"Geri Al\",\n    \"redo\": \"Yeniden yap\",\n    \"resetLibrary\": \"Kütüphaneyi sıfırla\",\n    \"createNewRoom\": \"Yeni oda oluştur\",\n    \"fullScreen\": \"Tam ekran\",\n    \"darkMode\": \"Koyu tema\",\n    \"lightMode\": \"Açık tema\",\n    \"zenMode\": \"Zen modu\",\n    \"objectsSnapMode\": \"Nesnelere hizala\",\n    \"exitZenMode\": \"Zen modundan çık\",\n    \"cancel\": \"İptal\",\n    \"clear\": \"Temizle\",\n    \"remove\": \"Kaldır\",\n    \"embed\": \"\",\n    \"publishLibrary\": \"Yayınla\",\n    \"submit\": \"Gönder\",\n    \"confirm\": \"Onayla\",\n    \"embeddableInteractionButton\": \"Etkileşime girmek için tıkla\"\n  },\n  \"alerts\": {\n    \"clearReset\": \"Tuvalin tamamı temizlenecek. Emin misiniz?\",\n    \"couldNotCreateShareableLink\": \"Paylaşılabilir bağlantı oluşturulamadı.\",\n    \"couldNotCreateShareableLinkTooBig\": \"Paylaşılabilir bağlantı oluşturulamadı: sahne çok büyük\",\n    \"couldNotLoadInvalidFile\": \"Bilinmeyen dosya yüklenemiyor\",\n    \"importBackendFailed\": \"Sunucudan içe aktarma başarısız.\",\n    \"cannotExportEmptyCanvas\": \"Boş tuval dışarıya aktarılamaz.\",\n    \"couldNotCopyToClipboard\": \"Panoya kopyalanamıyor.\",\n    \"decryptFailed\": \"Şifrelenmiş veri çözümlenemedi.\",\n    \"uploadedSecurly\": \"Yükleme uçtan uca şifreleme ile korunmaktadır. Excalidraw sunucusu ve üçüncül şahıslar içeriği okuyamayacaktır.\",\n    \"loadSceneOverridePrompt\": \"Harici çizimler yüklemek mevcut olan içeriği değiştirecektir. Devam etmek istiyor musunuz?\",\n    \"collabStopOverridePrompt\": \"Oturumu sonlandırmak daha önceki, yerel olarak kaydedilmiş çizimin üzerine kaydedilmesine sebep olacak. Emin misiniz?\\n\\n(Yerel çiziminizi kaybetmemek için tarayıcı sekmesini kapatabilirsiniz.)\",\n    \"errorAddingToLibrary\": \"Öğe kütüphaneye eklenemedi\",\n    \"errorRemovingFromLibrary\": \"Öğe kütüphaneden silinemedi\",\n    \"confirmAddLibrary\": \"Bu, kitaplığınıza {{numShapes}} tane şekil ekleyecek. Emin misiniz?\",\n    \"imageDoesNotContainScene\": \"Bu görüntü herhangi bir sahne verisi içermiyor gibi görünüyor. Dışa aktarma sırasında sahne yerleştirmeyi etkinleştirdiniz mi?\",\n    \"cannotRestoreFromImage\": \"Sahne bu resim dosyasından geri yüklenemedi\",\n    \"invalidSceneUrl\": \"Verilen bağlantıdan çalışma alanı yüklenemedi. Dosya bozuk olabilir veya geçerli bir Excalidraw JSON verisi bulundurmuyor olabilir.\",\n    \"resetLibrary\": \"Bu işlem kütüphanenizi sıfırlayacak. Emin misiniz?\",\n    \"removeItemsFromsLibrary\": \"{{count}} öğe(ler) kitaplıktan kaldırılsın mı?\",\n    \"invalidEncryptionKey\": \"Şifreleme anahtarı 22 karakter olmalı. Canlı işbirliği devre dışı bırakıldı.\",\n    \"collabOfflineWarning\": \"İnternet bağlantısı bulunamadı. Değişiklikleriniz kaydedilmeyecek!\"\n  },\n  \"errors\": {\n    \"unsupportedFileType\": \"Desteklenmeyen dosya türü.\",\n    \"imageInsertError\": \"Görsel eklenemedi. Daha sonra tekrar deneyin...\",\n    \"fileTooBig\": \"Dosya çok büyük. İzin verilen maksimum boyut {{maxSize}}.\",\n    \"svgImageInsertError\": \"SVG resmi eklenemedi. SVG işaretlemesi geçersiz görünüyor.\",\n    \"failedToFetchImage\": \"\",\n    \"invalidSVGString\": \"Geçersiz SVG.\",\n    \"cannotResolveCollabServer\": \"İş birliği sunucusuna bağlanılamıyor. Lütfen sayfayı yenileyip tekrar deneyin.\",\n    \"importLibraryError\": \"Kütüphane yüklenemedi\",\n    \"collabSaveFailed\": \"Backend veritabanına kaydedilemedi. Eğer problem devam ederse, çalışmanızı korumak için dosyayı yerel olarak kaydetmelisiniz.\",\n    \"collabSaveFailed_sizeExceeded\": \"Backend veritabanına kaydedilemedi; tuval çok büyük. Çalışmanızı korumak için dosyayı yerel olarak kaydetmelisiniz.\",\n    \"imageToolNotSupported\": \"\",\n    \"brave_measure_text_error\": {\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"line3\": \"\",\n      \"line4\": \"\"\n    },\n    \"libraryElementTypeError\": {\n      \"embeddable\": \"\",\n      \"iframe\": \"\",\n      \"image\": \"Resimleri kütüphaneye ekleme desteği yakında geliyor!\"\n    },\n    \"asyncPasteFailedOnRead\": \"\",\n    \"asyncPasteFailedOnParse\": \"\",\n    \"copyToSystemClipboardFailed\": \"\"\n  },\n  \"toolBar\": {\n    \"selection\": \"Seçme\",\n    \"image\": \"Görsel ekle\",\n    \"rectangle\": \"Dikdörtgen\",\n    \"diamond\": \"Elmas\",\n    \"ellipse\": \"Elips\",\n    \"arrow\": \"Ok\",\n    \"line\": \"Çizgi\",\n    \"freedraw\": \"Çiz\",\n    \"text\": \"Yazı\",\n    \"library\": \"Kütüphane\",\n    \"lock\": \"Seçilen aracı çizimden sonra aktif tut\",\n    \"penMode\": \"Kalem modu - dokunmayı engelle\",\n    \"link\": \"Seçilen şekil için bağlantı Ekle/Güncelle\",\n    \"eraser\": \"Silgi\",\n    \"frame\": \"Çerçeve aracı\",\n    \"magicframe\": \"\",\n    \"embeddable\": \"Web Yerleştirme\",\n    \"laser\": \"Lazer işaretçisi\",\n    \"hand\": \"\",\n    \"extraTools\": \"Daha fazla araç\",\n    \"mermaidToExcalidraw\": \"\",\n    \"magicSettings\": \"\"\n  },\n  \"headings\": {\n    \"canvasActions\": \"Tuval eylemleri\",\n    \"selectedShapeActions\": \"Seçilen şekil aksiyonları\",\n    \"shapes\": \"Şekiller\"\n  },\n  \"hints\": {\n    \"canvasPanning\": \"\",\n    \"linearElement\": \"Birden fazla nokta için tıklayın, tek çizgi için sürükleyin\",\n    \"freeDraw\": \"Tıkla ve sürükle, bitirdiğinde serbest bırak\",\n    \"text\": \"İpucu: seçme aracıyla herhangi bir yere çift tıklayarak da yazı ekleyebilirsin\",\n    \"embeddable\": \"Web sitesi yerleştirmek için sürükle bırak\",\n    \"text_selected\": \"Metni düzenlemek için çift tıklayın veya ENTER'a basın\",\n    \"text_editing\": \"Düzenlemeyi bitirmek için ESC veya Ctrl/Cmd+ENTER tuşlarına basın\",\n    \"linearElementMulti\": \"Bitirmek için son noktaya tıklayın ya da Escape veya Enter tuşuna basın\",\n    \"lockAngle\": \"SHIFT tuşuna basılı tutarak açıyı koruyabilirsiniz\",\n    \"resize\": \"Yeniden boyutlandırırken SHIFT tuşunu basılı tutarak oranları sınırlayabilirsiniz,\\nmerkezden yeniden boyutlandırmak için ALT tuşunu basılı tutun\",\n    \"resizeImage\": \"SHIFT'e basılı tutarak serbestçe yeniden boyutlandırabilirsiniz, merkezden yeniden boyutlandırmak için ALT tuşunu basılı tutun\",\n    \"rotate\": \"Döndürürken SHIFT tuşuna basılı tutarak açıları koruyabilirsiniz\",\n    \"lineEditor_info\": \"Puanları düzenlemek için ctrl veya cmd tuşuna basılı tutup çift tıklayın veya enter tuşuna basın\",\n    \"lineEditor_pointSelected\": \"Sil tuşuna basarak noktaları silin,\\nCtrl/Cmd + D ile çoğaltın, ya da sürükleyerek taşıyın\",\n    \"lineEditor_nothingSelected\": \"Düzenlemek için bir nokta seçin (birden fazla seçmek için SHIFT tuşunu basılı tutun),\\nveya Alt tuşunu basılı tutun ve yeni noktalar eklemek için tıklayın\",\n    \"placeImage\": \"Resmi yerleştirmek için tıklayın ya da boyutunu manuel olarak ayarlamak için tıklayıp sürükleyin\",\n    \"publishLibrary\": \"Kendi kitaplığınızı yayınlayın\",\n    \"bindTextToElement\": \"Enter tuşuna basarak metin ekleyin\",\n    \"deepBoxSelect\": \"Ctrl/Cmd tuşuna basılı tutarak derin seçim yapın ya da sürüklemeyi engelleyin\",\n    \"eraserRevert\": \"Alt tuşuna basılı tutarak silinme için işaretlenmiş ögeleri tersine çevirin\",\n    \"firefox_clipboard_write\": \"\",\n    \"disableSnapping\": \"\"\n  },\n  \"canvasError\": {\n    \"cannotShowPreview\": \"Önizleme gösterilemiyor\",\n    \"canvasTooBig\": \"Kanvas çok büyük olabilir.\",\n    \"canvasTooBigTip\": \"İpucu: En uzaktaki elemanları birbirine yakınlaştırmayı deneyin.\"\n  },\n  \"errorSplash\": {\n    \"headingMain\": \"Hata oluştu. Lütfen <button>sayfayı yenilemeyi deneyin.</button>\",\n    \"clearCanvasMessage\": \"Yenileme sonrası sorun devam ediyorsa, lütfen <button>çizim alanını temizlemeyi deneyin.</button>\",\n    \"clearCanvasCaveat\": \" Bu, yaptığınız değişiklikleri sıfırlayacak \",\n    \"trackedToSentry\": \"Tanımlayıcı ile ilgili hata {{eventId}} sistemimize yakalandı.\",\n    \"openIssueMessage\": \"Sahne bilginizi hata mesajına yansıtmamak için oldukça dikkatli davrandık. Eğer sahneniz gizli değilse hatayı lütfen şuradan takip edin <button>hata takibi.</button> Lütfen aşağıya GitHub sorununa kopyalayarak ve yapıştırarak bilgi ekleyin.\",\n    \"sceneContent\": \"Sahne içeriği:\"\n  },\n  \"roomDialog\": {\n    \"desc_intro\": \"Çalışma alanınıza, sizinle birlikte çalışabilmeleri için başkalarını da ekleyebilirsiniz.\",\n    \"desc_privacy\": \"Çalışma ortamında yaptıklarınız ve çizimleriniz uçtan uca şifrelemeyle saklanmaktadır. Sunucularımız dahi bu verileri şifrelenmemiş haliyle göremez.\",\n    \"button_startSession\": \"Oturumu başlat\",\n    \"button_stopSession\": \"Oturumu sonlandır\",\n    \"desc_inProgressIntro\": \"Ortak çalışma ortamı oluşturuldu.\",\n    \"desc_shareLink\": \"Bu bağlantıyı birlikte çalışacağınız kişilerle paylaşabilirsiniz:\",\n    \"desc_exitSession\": \"Çalışma ortamını kapattığınızda ortak çalışmadan ayrılmış olursunuz ancak kendi versiyonunuzda çalışmaya devam edebilirsiniz. Bu durumda ortak çalıştığınız diğer kişiler etkilenmeyecek, çalışma ortamındaki versiyon üzerinden çalışmaya devam edebilecekler.\",\n    \"shareTitle\": \"Excalidraw'da canlı ortak calışma oturumuna katıl\"\n  },\n  \"errorDialog\": {\n    \"title\": \"Hata\"\n  },\n  \"exportDialog\": {\n    \"disk_title\": \"Belleğe kaydet\",\n    \"disk_details\": \"Sahne verilerini daha sonra içe aktarabileceğiniz bir dosyaya aktarın.\",\n    \"disk_button\": \"Dosyaya kaydet\",\n    \"link_title\": \"Paylaşılabilir bağlantı\",\n    \"link_details\": \"Salt okunur bir bağlantı olarak dışa aktarın.\",\n    \"link_button\": \"Bağlantı olarak dışa aktar\",\n    \"excalidrawplus_description\": \"Sahneyi Excalidraw+ çalışma alanınıza kaydedin.\",\n    \"excalidrawplus_button\": \"Dışa aktar\",\n    \"excalidrawplus_exportError\": \"Şu anda Excalidraw+'a aktarılamadı...\"\n  },\n  \"helpDialog\": {\n    \"blog\": \"Blog'umuzu okuyun\",\n    \"click\": \"tıkla\",\n    \"deepSelect\": \"Derin seçim\",\n    \"deepBoxSelect\": \"Kutu içerisinde derin seçim yapın, sürüklemeyi engelleyin\",\n    \"curvedArrow\": \"Eğri ok\",\n    \"curvedLine\": \"Eğri çizgi\",\n    \"documentation\": \"Dokümantasyon\",\n    \"doubleClick\": \"çift-tıklama\",\n    \"drag\": \"sürükle\",\n    \"editor\": \"Düzenleyici\",\n    \"editLineArrowPoints\": \"Çizgi/ok noktalarını düzenle\",\n    \"editText\": \"Etiket / metin düzenle\",\n    \"github\": \"Bir hata mı buldun? Bildir\",\n    \"howto\": \"Rehberlerimizi takip edin\",\n    \"or\": \"veya\",\n    \"preventBinding\": \"Ok bağlamayı önleyin\",\n    \"tools\": \"Araçlar\",\n    \"shortcuts\": \"Klavye kısayolları\",\n    \"textFinish\": \"Düzenlemeyi bitir (metin düzenleyici)\",\n    \"textNewLine\": \"Yeni satır ekle (metin düzenleyici)\",\n    \"title\": \"Yardım\",\n    \"view\": \"Görünüm\",\n    \"zoomToFit\": \"Tüm öğeleri sığdırmak için yakınlaştır\",\n    \"zoomToSelection\": \"Seçime yakınlaş\",\n    \"toggleElementLock\": \"Seçimi Kilitle/çöz\",\n    \"movePageUpDown\": \"Sayfayı yukarı/aşağı kaydır\",\n    \"movePageLeftRight\": \"Sayfayı sola/sağa kaydır\"\n  },\n  \"clearCanvasDialog\": {\n    \"title\": \"Tuvali temizle\"\n  },\n  \"publishDialog\": {\n    \"title\": \"Kitaplığı yayınla\",\n    \"itemName\": \"Öğe adı\",\n    \"authorName\": \"Yazar adı\",\n    \"githubUsername\": \"GıtHub kullanıcı adı\",\n    \"twitterUsername\": \"Twitter kullanıcı adı\",\n    \"libraryName\": \"Kitaplık adı\",\n    \"libraryDesc\": \"Kitaplık açıklaması\",\n    \"website\": \"Web sitesi\",\n    \"placeholder\": {\n      \"authorName\": \"Adınız ya da kullanıcı adınız\",\n      \"libraryName\": \"Kitaplığınızın adı\",\n      \"libraryDesc\": \"İnsanların kullanımını anlamasına yardımcı olmak için kitaplığınızın açıklaması\",\n      \"githubHandle\": \"Github bağlantısı ( tercihe bağlı), kütüphane gözden geçirme için onaylandığında düzenleyebiliesiniz diye\",\n      \"twitterHandle\": \"Twitter kullanıcı adı ( tercihe bağlı), bu sayede Twitter üzerinde paylaşıren çalışmanızı size atfedebiliriz\",\n      \"website\": \"Kişisel web sayfanızı ya da başka bir yeri bağlayın (tercihe bağlı)\"\n    },\n    \"errors\": {\n      \"required\": \"Gerekli\",\n      \"website\": \"Geçerli bir URL girin\"\n    },\n    \"noteDescription\": \"Submit your library to be included in the <link>genel kütüphane reposu</link>diğer insanlar çizimlerinde kullanabilsin diye.\",\n    \"noteGuidelines\": \"Önce kütüphane elle onaylanmalı. şunu okuyun <link>yönergeler</link> onaylamadan önce. gerekli olması halinde iletişim kurmak için ve değişiklik için Github hesabı gerekli, ama çok da illaki olmalı değil.\",\n    \"noteLicense\": \"Bunu onaylayarak, kütüğhanenin şu lisansla yayınlanmasını onaylıyorsunuz <link>MIT Lisans, </link>ki bu kısaca herkesin onu kısıtlama olmaksızın kullanabileceği anlamına gelmektedir.\",\n    \"noteItems\": \"Her kütüphane kendi ismine sahip olmalı ki tarama yapabilelim. Şu kütüphane ögeleri dahil edilecek:\",\n    \"atleastOneLibItem\": \"Lütfen başlamak için en az bir tane kütüphane ögesi seçin\",\n    \"republishWarning\": \"Not: seçilen ögelerden bir kısmı zaten yayınlanmış/gönderilmiş. Yalnızca mevcut kütüphane ve gönderileri güncellerken yeniden gönderme işlemi yapmalısınız.\"\n  },\n  \"publishSuccessDialog\": {\n    \"title\": \"Kütüphane gönderildi\",\n    \"content\": \"Teşekkürler {{authorName}}. Kütüphaneniz gözden geçirme için alındı. Durumu takip edebilirsiniz<link>burada</link>\"\n  },\n  \"confirmDialog\": {\n    \"resetLibrary\": \"Kütüphaneyi sıfırla\",\n    \"removeItemsFromLib\": \"Seçilen ögeleri kütüphaneden kaldır\"\n  },\n  \"imageExportDialog\": {\n    \"header\": \"Resmi dışa aktar\",\n    \"label\": {\n      \"withBackground\": \"Arka plan\",\n      \"onlySelected\": \"Sadece seçilen\",\n      \"darkMode\": \"Karanlık mod\",\n      \"embedScene\": \"Sahne yerleştir\",\n      \"scale\": \"Ölçeklendir\",\n      \"padding\": \"Dış boşluk\"\n    },\n    \"tooltip\": {\n      \"embedScene\": \"Sahne verisi, sahnenin geri yüklenebilmesi için dışarı aktarılan PNG/SVG dosyasına kaydedilecektir. Bu, dışa aktarılan dosya boyutunu arttıracaktır.\"\n    },\n    \"title\": {\n      \"exportToPng\": \"PNG olarak dışa aktar\",\n      \"exportToSvg\": \"SVG olarak dışa aktar\",\n      \"copyPngToClipboard\": \"\"\n    },\n    \"button\": {\n      \"exportToPng\": \"PNG\",\n      \"exportToSvg\": \"SVG\",\n      \"copyPngToClipboard\": \"Panoya kopyala\"\n    }\n  },\n  \"encrypted\": {\n    \"tooltip\": \"Çizimleriniz uçtan-uca şifrelenmiştir, Excalidraw'ın sunucuları bile onları göremez.\",\n    \"link\": \"Excalidraw'da uçtan uca şifreleme hakkında blog yazısı\"\n  },\n  \"stats\": {\n    \"angle\": \"Açı\",\n    \"element\": \"Bileşen\",\n    \"elements\": \"Bileşenler\",\n    \"height\": \"Yükseklik\",\n    \"scene\": \"Sahne\",\n    \"selected\": \"Seçili\",\n    \"storage\": \"Depolama\",\n    \"title\": \"İnekler için istatistikler\",\n    \"total\": \"Toplam\",\n    \"version\": \"Sürüm\",\n    \"versionCopy\": \"Kopyalamak için tıkla\",\n    \"versionNotAvailable\": \"Sürüm mevcut değil\",\n    \"width\": \"Genişlik\"\n  },\n  \"toast\": {\n    \"addedToLibrary\": \"Kütüphaneye eklendi\",\n    \"copyStyles\": \"Stiller kopyalandı.\",\n    \"copyToClipboard\": \"Panoya kopyalandı.\",\n    \"copyToClipboardAsPng\": \"{{exportSelection}} panoya PNG olarak\\n({{exportColorScheme}}) kopyalandı\",\n    \"fileSaved\": \"Dosya kaydedildi.\",\n    \"fileSavedToFilename\": \"{filename} kaydedildi\",\n    \"canvas\": \"tuval\",\n    \"selection\": \"seçim\",\n    \"pasteAsSingleElement\": \"Tekil obje olarak yapıştırmak için veya var olan bir metin editörüne yapıştırmak için {{shortcut}} kullanın\",\n    \"unableToEmbed\": \"\",\n    \"unrecognizedLinkFormat\": \"\"\n  },\n  \"colors\": {\n    \"transparent\": \"Şeffaf\",\n    \"black\": \"Siyah\",\n    \"white\": \"Beyaz\",\n    \"red\": \"Kırmızı\",\n    \"pink\": \"Pembe\",\n    \"grape\": \"Koyu Mor\",\n    \"violet\": \"Menekşe rengi\",\n    \"gray\": \"Gri\",\n    \"blue\": \"Mavi\",\n    \"cyan\": \"Camgöbeği\",\n    \"teal\": \"Deniz mavisi\",\n    \"green\": \"Yeşil\",\n    \"yellow\": \"Sarı\",\n    \"orange\": \"Turuncu\",\n    \"bronze\": \"\"\n  },\n  \"welcomeScreen\": {\n    \"app\": {\n      \"center_heading\": \"\",\n      \"center_heading_plus\": \"Ecalidraw+'a mı gitmek istediniz?\",\n      \"menuHint\": \"Dışa aktar, seçenekler, diller, ...\"\n    },\n    \"defaults\": {\n      \"menuHint\": \"Dışa aktar, seçenekler, ve daha fazlası...\",\n      \"center_heading\": \"\",\n      \"toolbarHint\": \"Bir araç seçin ve çizime başlayın!\",\n      \"helpHint\": \"Kısayollar & yardım\"\n    }\n  },\n  \"colorPicker\": {\n    \"mostUsedCustomColors\": \"En çok kullanılan özel renkler\",\n    \"colors\": \"Renkler\",\n    \"shades\": \"\",\n    \"hexCode\": \"Hex kodu\",\n    \"noShades\": \"\"\n  },\n  \"overwriteConfirm\": {\n    \"action\": {\n      \"exportToImage\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"saveToDisk\": {\n        \"title\": \"\",\n        \"button\": \"Diske Kaydet\",\n        \"description\": \"\"\n      },\n      \"excalidrawPlus\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    },\n    \"modal\": {\n      \"loadFromFile\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      },\n      \"shareableLink\": {\n        \"title\": \"\",\n        \"button\": \"\",\n        \"description\": \"\"\n      }\n    }\n  },\n  \"mermaid\": {\n    \"title\": \"\",\n    \"button\": \"\",\n    \"description\": \"\",\n    \"syntax\": \"\",\n    \"preview\": \"\"\n  }\n}\n"], "mappings": ";;;AACE,aAAU;AAAA,EACR,OAAS;AAAA,EACT,kBAAoB;AAAA,EACpB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,aAAe;AAAA,EACf,YAAc;AAAA,EACd,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,WAAa;AAAA,EACb,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,cAAgB;AAAA,EAChB,QAAU;AAAA,EACV,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,aAAe;AAAA,EACf,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,oBAAsB;AAAA,EACtB,YAAc;AAAA,EACd,SAAW;AAAA,EACX,WAAa;AAAA,EACb,OAAS;AAAA,EACT,OAAS;AAAA,EACT,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,0BAA4B;AAAA,EAC5B,oBAAsB;AAAA,EACtB,4BAA8B;AAAA,EAC9B,mBAAqB;AAAA,EACrB,2BAA6B;AAAA,EAC7B,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,SAAW;AAAA,EACX,QAAU;AAAA,EACV,YAAc;AAAA,EACd,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,WAAa;AAAA,EACb,WAAa;AAAA,EACb,QAAU;AAAA,EACV,YAAc;AAAA,EACd,WAAa;AAAA,EACb,aAAe;AAAA,EACf,cAAgB;AAAA,EAChB,kBAAoB;AAAA,EACpB,eAAiB;AAAA,EACjB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,mBAAqB;AAAA,EACrB,oBAAsB;AAAA,EACtB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,oBAAsB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AAAA,EACX,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,cAAgB;AAAA,EAChB,mBAAqB;AAAA,EACrB,uBAAyB;AAAA,EACzB,WAAa;AAAA,EACb,cAAgB;AAAA,EAChB,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,aAAe;AAAA,EACf,WAAa;AAAA,EACb,YAAc;AAAA,EACd,kBAAoB;AAAA,EACpB,oBAAsB;AAAA,EACtB,wBAA0B;AAAA,EAC1B,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,cAAgB;AAAA,EAChB,UAAY;AAAA,EACZ,OAAS;AAAA,EACT,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,eAAiB;AAAA,EACjB,kBAAoB;AAAA,EACpB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,yBAA2B;AAAA,EAC3B,MAAQ;AAAA,IACN,MAAQ;AAAA,IACR,WAAa;AAAA,IACb,QAAU;AAAA,IACV,aAAe;AAAA,IACf,OAAS;AAAA,IACT,YAAc;AAAA,IACd,OAAS;AAAA,EACX;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,MAAQ;AAAA,EACV;AAAA,EACA,aAAe;AAAA,IACb,MAAQ;AAAA,IACR,QAAU;AAAA,IACV,SAAW;AAAA,IACX,WAAa;AAAA,EACf;AAAA,EACA,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,QAAU;AACZ;AACA,cAAW;AAAA,EACT,SAAW;AAAA,EACX,mBAAqB;AAAA,EACrB,0BAA4B;AAC9B;AACA,cAAW;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,kBAAoB;AAAA,EACpB,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,WAAa;AAAA,EACb,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,cAAgB;AAAA,EAChB,eAAiB;AAAA,EACjB,YAAc;AAAA,EACd,UAAY;AAAA,EACZ,WAAa;AAAA,EACb,SAAW;AAAA,EACX,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,QAAU;AAAA,EACV,OAAS;AAAA,EACT,QAAU;AAAA,EACV,OAAS;AAAA,EACT,gBAAkB;AAAA,EAClB,QAAU;AAAA,EACV,SAAW;AAAA,EACX,6BAA+B;AACjC;AACA,aAAU;AAAA,EACR,YAAc;AAAA,EACd,6BAA+B;AAAA,EAC/B,mCAAqC;AAAA,EACrC,yBAA2B;AAAA,EAC3B,qBAAuB;AAAA,EACvB,yBAA2B;AAAA,EAC3B,yBAA2B;AAAA,EAC3B,eAAiB;AAAA,EACjB,iBAAmB;AAAA,EACnB,yBAA2B;AAAA,EAC3B,0BAA4B;AAAA,EAC5B,sBAAwB;AAAA,EACxB,0BAA4B;AAAA,EAC5B,mBAAqB;AAAA,EACrB,0BAA4B;AAAA,EAC5B,wBAA0B;AAAA,EAC1B,iBAAmB;AAAA,EACnB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,sBAAwB;AAAA,EACxB,sBAAwB;AAC1B;AACA,aAAU;AAAA,EACR,qBAAuB;AAAA,EACvB,kBAAoB;AAAA,EACpB,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,2BAA6B;AAAA,EAC7B,oBAAsB;AAAA,EACtB,kBAAoB;AAAA,EACpB,+BAAiC;AAAA,EACjC,uBAAyB;AAAA,EACzB,0BAA4B;AAAA,IAC1B,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,EACX;AAAA,EACA,yBAA2B;AAAA,IACzB,YAAc;AAAA,IACd,QAAU;AAAA,IACV,OAAS;AAAA,EACX;AAAA,EACA,wBAA0B;AAAA,EAC1B,yBAA2B;AAAA,EAC3B,6BAA+B;AACjC;AACA,cAAW;AAAA,EACT,WAAa;AAAA,EACb,OAAS;AAAA,EACT,WAAa;AAAA,EACb,SAAW;AAAA,EACX,SAAW;AAAA,EACX,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,YAAc;AAAA,EACd,YAAc;AAAA,EACd,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,qBAAuB;AAAA,EACvB,eAAiB;AACnB;AACA,eAAY;AAAA,EACV,eAAiB;AAAA,EACjB,sBAAwB;AAAA,EACxB,QAAU;AACZ;AACA,YAAS;AAAA,EACP,eAAiB;AAAA,EACjB,eAAiB;AAAA,EACjB,UAAY;AAAA,EACZ,MAAQ;AAAA,EACR,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,oBAAsB;AAAA,EACtB,WAAa;AAAA,EACb,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,iBAAmB;AAAA,EACnB,0BAA4B;AAAA,EAC5B,4BAA8B;AAAA,EAC9B,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,mBAAqB;AAAA,EACrB,eAAiB;AAAA,EACjB,cAAgB;AAAA,EAChB,yBAA2B;AAAA,EAC3B,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,mBAAqB;AAAA,EACrB,cAAgB;AAAA,EAChB,iBAAmB;AACrB;AACA,kBAAe;AAAA,EACb,aAAe;AAAA,EACf,oBAAsB;AAAA,EACtB,mBAAqB;AAAA,EACrB,iBAAmB;AAAA,EACnB,kBAAoB;AAAA,EACpB,cAAgB;AAClB;AACA,iBAAc;AAAA,EACZ,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,qBAAuB;AAAA,EACvB,oBAAsB;AAAA,EACtB,sBAAwB;AAAA,EACxB,gBAAkB;AAAA,EAClB,kBAAoB;AAAA,EACpB,YAAc;AAChB;AACA,kBAAe;AAAA,EACb,OAAS;AACX;AACA,mBAAgB;AAAA,EACd,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,cAAgB;AAAA,EAChB,aAAe;AAAA,EACf,4BAA8B;AAAA,EAC9B,uBAAyB;AAAA,EACzB,4BAA8B;AAChC;AACA,iBAAc;AAAA,EACZ,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,YAAc;AAAA,EACd,eAAiB;AAAA,EACjB,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,qBAAuB;AAAA,EACvB,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,IAAM;AAAA,EACN,gBAAkB;AAAA,EAClB,OAAS;AAAA,EACT,WAAa;AAAA,EACb,YAAc;AAAA,EACd,aAAe;AAAA,EACf,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,WAAa;AAAA,EACb,iBAAmB;AAAA,EACnB,mBAAqB;AAAA,EACrB,gBAAkB;AAAA,EAClB,mBAAqB;AACvB;AACA,wBAAqB;AAAA,EACnB,OAAS;AACX;AACA,oBAAiB;AAAA,EACf,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,YAAc;AAAA,EACd,gBAAkB;AAAA,EAClB,iBAAmB;AAAA,EACnB,aAAe;AAAA,EACf,aAAe;AAAA,EACf,SAAW;AAAA,EACX,aAAe;AAAA,IACb,YAAc;AAAA,IACd,aAAe;AAAA,IACf,aAAe;AAAA,IACf,cAAgB;AAAA,IAChB,eAAiB;AAAA,IACjB,SAAW;AAAA,EACb;AAAA,EACA,QAAU;AAAA,IACR,UAAY;AAAA,IACZ,SAAW;AAAA,EACb;AAAA,EACA,iBAAmB;AAAA,EACnB,gBAAkB;AAAA,EAClB,aAAe;AAAA,EACf,WAAa;AAAA,EACb,mBAAqB;AAAA,EACrB,kBAAoB;AACtB;AACA,2BAAwB;AAAA,EACtB,OAAS;AAAA,EACT,SAAW;AACb;AACA,oBAAiB;AAAA,EACf,cAAgB;AAAA,EAChB,oBAAsB;AACxB;AACA,wBAAqB;AAAA,EACnB,QAAU;AAAA,EACV,OAAS;AAAA,IACP,gBAAkB;AAAA,IAClB,cAAgB;AAAA,IAChB,UAAY;AAAA,IACZ,YAAc;AAAA,IACd,OAAS;AAAA,IACT,SAAW;AAAA,EACb;AAAA,EACA,SAAW;AAAA,IACT,YAAc;AAAA,EAChB;AAAA,EACA,OAAS;AAAA,IACP,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AAAA,EACA,QAAU;AAAA,IACR,aAAe;AAAA,IACf,aAAe;AAAA,IACf,oBAAsB;AAAA,EACxB;AACF;AACA,gBAAa;AAAA,EACX,SAAW;AAAA,EACX,MAAQ;AACV;AACA,YAAS;AAAA,EACP,OAAS;AAAA,EACT,SAAW;AAAA,EACX,UAAY;AAAA,EACZ,QAAU;AAAA,EACV,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,SAAW;AAAA,EACX,OAAS;AAAA,EACT,OAAS;AAAA,EACT,SAAW;AAAA,EACX,aAAe;AAAA,EACf,qBAAuB;AAAA,EACvB,OAAS;AACX;AACA,YAAS;AAAA,EACP,gBAAkB;AAAA,EAClB,YAAc;AAAA,EACd,iBAAmB;AAAA,EACnB,sBAAwB;AAAA,EACxB,WAAa;AAAA,EACb,qBAAuB;AAAA,EACvB,QAAU;AAAA,EACV,WAAa;AAAA,EACb,sBAAwB;AAAA,EACxB,eAAiB;AAAA,EACjB,wBAA0B;AAC5B;AACA,aAAU;AAAA,EACR,aAAe;AAAA,EACf,OAAS;AAAA,EACT,OAAS;AAAA,EACT,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,QAAU;AAAA,EACV,QAAU;AACZ;AACA,oBAAiB;AAAA,EACf,KAAO;AAAA,IACL,gBAAkB;AAAA,IAClB,qBAAuB;AAAA,IACvB,UAAY;AAAA,EACd;AAAA,EACA,UAAY;AAAA,IACV,UAAY;AAAA,IACZ,gBAAkB;AAAA,IAClB,aAAe;AAAA,IACf,UAAY;AAAA,EACd;AACF;AACA,kBAAe;AAAA,EACb,sBAAwB;AAAA,EACxB,QAAU;AAAA,EACV,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AACd;AACA,uBAAoB;AAAA,EAClB,QAAU;AAAA,IACR,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,YAAc;AAAA,MACZ,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,gBAAkB;AAAA,MAChB,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP,cAAgB;AAAA,MACd,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,IACA,eAAiB;AAAA,MACf,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,cAAW;AAAA,EACT,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,QAAU;AAAA,EACV,SAAW;AACb;AA3gBF;AAAA,EACE;AAAA,EA6IA;AAAA,EAKA;AAAA,EAsCA;AAAA,EAuBA;AAAA,EA2BA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAwBA;AAAA,EAKA;AAAA,EAQA;AAAA,EAUA;AAAA,EAGA;AAAA,EAWA;AAAA,EA6BA;AAAA,EAGA;AAAA,EA4BA;AAAA,EAIA;AAAA,EAIA;AAAA,EAwBA;AAAA,EAIA;AAAA,EAeA;AAAA,EAaA;AAAA,EAiBA;AAAA,EAaA;AAAA,EAOA;AAAA,EA+BA;AAOF;", "names": []}