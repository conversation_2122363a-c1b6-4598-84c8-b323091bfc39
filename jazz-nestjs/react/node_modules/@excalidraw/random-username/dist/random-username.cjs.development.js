'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var animals = ['aardvark', 'albatross', 'alligator', 'alpaca', 'ant', 'anteater', 'antelope', 'armadillo', 'badger', 'barracuda', 'bat', 'bear', 'bee', 'bison', 'buffalo', 'butterfly', 'camel', 'capybara', 'caribou', 'cat', 'caterpillar', 'cheetah', 'chinchilla', 'cobra', 'cormorant', 'coyote', 'crab', 'crane', 'crocodile', 'deer', 'dinosaur', 'dolphin', 'dove', 'dragonfly', 'duck', 'eagle', 'echidna', 'elephant', 'elk', 'emu', 'falcon', 'ferret', 'finch', 'fish', 'flamingo', 'fly', 'fox', 'gazelle', 'gerbil', 'giraffe', 'goldfinch', 'goldfish', 'gorilla', 'grasshopper', 'gull', 'hamster', 'hare', 'hawk', 'hedgehog', 'herring', 'hippopotamus', 'hornet', 'horse', 'hummingbird', 'ibex', 'jaguar', 'jay', 'jellyfish', 'kangaroo', 'kingfisher', 'koala', 'lark', 'lemur', 'leopard', 'lion', 'llama', 'lobster', 'loris', 'lyrebird', 'magpie', 'manatee', 'mandrill', 'mantis', 'meerkat', 'mink', 'mongoose', 'moose', 'mouse', 'narwhal', 'nightingale', 'octopus', 'okapi', 'opossum', 'ostrich', 'otter', 'owl', 'oyster', 'panther', 'partridge', 'pelican', 'penguin', 'pheasant', 'pony', 'porcupine', 'porpoise', 'quail', 'rabbit', 'ram', 'raven', 'reindeer', 'rhinoceros', 'salamander', 'salmon', 'sandpiper', 'scorpion', 'seahorse', 'seal', 'sparrow', 'spoonbill', 'squid', 'squirrel', 'starling', 'stingray', 'stork', 'swan', 'tiger', 'whale', 'wildcat', 'wolf', 'wolverine', 'wombat', 'woodcock', 'woodpecker', 'yak', 'zebra'];
var adjectives = ['absolute', 'adorable', 'adventurous', 'acclaimed', 'accomplished', 'accurate', 'acrobatic', 'active', 'actual', 'adept', 'admirable', 'admired', 'adolescent', 'adorable', 'adored', 'advanced', 'affectionate', 'agile', 'alert', 'alive', 'altruistic', 'amazing', 'ambitious', 'amused', 'amusing', 'ancient', 'animated', 'antique', 'apprehensive', 'apt', 'arctic', 'artistic', 'assured', 'astonishing', 'athletic', 'attentive', 'attractive', 'authentic', 'authorized', 'automatic', 'awesome', 'beautiful', 'beloved', 'beneficial', 'big', 'blissful', 'blue', 'bold', 'bouncy', 'brave', 'bright', 'brilliant', 'brisk', 'bronze', 'busy', 'calm', 'careful', 'caring', 'celebrated', 'charming', 'cheerful', 'cheery', 'clever', 'colorful', 'compassionate', 'competent', 'complete', 'composed', 'conscious', 'considerate', 'content', 'cool', 'courageous', 'courteous', 'crafty', 'creative', 'cuddly', 'cultivated', 'cultured', 'cute', 'daring', 'dazzling', 'dearest', 'decisive', 'deep', 'defiant', 'definite', 'delightful', 'dependable', 'determined', 'diligent', 'distinct', 'dutiful', 'earnest', 'elegant', 'enchanted', 'enchanting', 'energetic', 'enlightened', 'esteemed', 'euphoric', 'excited', 'fabulous', 'fancy', 'fearless', 'generous', 'gentle', 'genuine', 'glamorous', 'gleaming', 'glittering', 'glorious', 'golden', 'graceful', 'gracious', 'grand', 'great', 'green', 'handmade', 'handsome', 'handy', 'happy', 'harmonious', 'healthy', 'hearty', 'honorable', 'imaginative', 'impassioned', 'impeccable', 'impressive', 'incomparable', 'incredible', 'infamous', 'innocent', 'joyful', 'joyous', 'jubilant', 'knowledgeable', 'lawful', 'legal', 'luminous', 'magnificent', 'majestic', 'major', 'minty', 'modern', 'mysterious', 'optimistic', 'orange', 'orderly', 'ornate', 'original', 'outstanding', 'passionate', 'peaceful', 'pleased', 'positive', 'powerful', 'productive', 'proud', 'purple', 'puzzled', 'real', 'reasonable', 'reliable', 'remarkable', 'respectful', 'responsible', 'rusty', 'serene', 'silky', 'silver', 'sociable', 'solid', 'sophisticated', 'soulful', 'spectacular', 'speedy', 'splendid', 'squiggly', 'stimulating', 'striking', 'striped', 'strong', 'stunning', 'stylish', 'substantial', 'subtle', 'super', 'superb', 'supportive', 'swift', 'tangible', 'teeming', 'terrific', 'thoughtful', 'timely', 'total', 'tremendous', 'true', 'trustworthy', 'truthful', 'ultimate', 'uncommon', 'unique', 'upbeat', 'velvety', 'venerated', 'verifiable', 'vibrant', 'victorious', 'vigilant', 'vigorous', 'violet', 'virtuous', 'vital', 'warm', 'warmhearted', 'watchful', 'whole', 'wise', 'witty', 'wonderful', 'worthwhile', 'worthy', 'zealous'];

var sample = function sample(arr) {
  return arr[Math.floor(Math.random() * arr.length)];
};

var upperFirst = function upperFirst(str) {
  return str[0].toUpperCase() + str.slice(1);
};

var getRandomUsername = function getRandomUsername() {
  return upperFirst(sample(adjectives)) + " " + upperFirst(sample(animals));
};

exports.adjectives = adjectives;
exports.animals = animals;
exports.getRandomUsername = getRandomUsername;
//# sourceMappingURL=random-username.cjs.development.js.map
