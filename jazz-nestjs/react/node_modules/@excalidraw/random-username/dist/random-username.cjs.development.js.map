{"version": 3, "file": "random-username.cjs.development.js", "sources": ["../src/index.ts"], "sourcesContent": ["export const animals = [\n  'aardvark',\n  'albatross',\n  'alligator',\n  'alpaca',\n  'ant',\n  'anteater',\n  'antelope',\n  'armadillo',\n  'badger',\n  'barracuda',\n  'bat',\n  'bear',\n  'bee',\n  'bison',\n  'buffalo',\n  'butterfly',\n  'camel',\n  'capybara',\n  'caribou',\n  'cat',\n  'caterpillar',\n  'cheetah',\n  'chinchilla',\n  'cobra',\n  'cormorant',\n  'coyote',\n  'crab',\n  'crane',\n  'crocodile',\n  'deer',\n  'dinosaur',\n  'dolphin',\n  'dove',\n  'dragonfly',\n  'duck',\n  'eagle',\n  'echidna',\n  'elephant',\n  'elk',\n  'emu',\n  'falcon',\n  'ferret',\n  'finch',\n  'fish',\n  'flamingo',\n  'fly',\n  'fox',\n  'gazelle',\n  'gerbil',\n  'giraffe',\n  'goldfinch',\n  'goldfish',\n  'gorilla',\n  'grasshopper',\n  'gull',\n  'hamster',\n  'hare',\n  'hawk',\n  'hedgehog',\n  'herring',\n  'hippopotamus',\n  'hornet',\n  'horse',\n  'hummingbird',\n  'ibex',\n  'jaguar',\n  'jay',\n  'jellyfish',\n  'kangaroo',\n  'kingfisher',\n  'koala',\n  'lark',\n  'lemur',\n  'leopard',\n  'lion',\n  'llama',\n  'lobster',\n  'loris',\n  'lyrebird',\n  'magpie',\n  'manatee',\n  'mandrill',\n  'mantis',\n  'meerkat',\n  'mink',\n  'mongoose',\n  'moose',\n  'mouse',\n  'narwhal',\n  'nightingale',\n  'octopus',\n  'okapi',\n  'opossum',\n  'ostrich',\n  'otter',\n  'owl',\n  'oyster',\n  'panther',\n  'partridge',\n  'pelican',\n  'penguin',\n  'pheasant',\n  'pony',\n  'porcupine',\n  'porpoise',\n  'quail',\n  'rabbit',\n  'ram',\n  'raven',\n  'reindeer',\n  'rhinoceros',\n  'salamander',\n  'salmon',\n  'sandpiper',\n  'scorpion',\n  'seahorse',\n  'seal',\n  'sparrow',\n  'spoonbill',\n  'squid',\n  'squirrel',\n  'starling',\n  'stingray',\n  'stork',\n  'swan',\n  'tiger',\n  'whale',\n  'wildcat',\n  'wolf',\n  'wolverine',\n  'wombat',\n  'woodcock',\n  'woodpecker',\n  'yak',\n  'zebra',\n];\n\nexport const adjectives = [\n  'absolute',\n  'adorable',\n  'adventurous',\n  'acclaimed',\n  'accomplished',\n  'accurate',\n  'acrobatic',\n  'active',\n  'actual',\n  'adept',\n  'admirable',\n  'admired',\n  'adolescent',\n  'adorable',\n  'adored',\n  'advanced',\n  'affectionate',\n  'agile',\n  'alert',\n  'alive',\n  'altruistic',\n  'amazing',\n  'ambitious',\n  'amused',\n  'amusing',\n  'ancient',\n  'animated',\n  'antique',\n  'apprehensive',\n  'apt',\n  'arctic',\n  'artistic',\n  'assured',\n  'astonishing',\n  'athletic',\n  'attentive',\n  'attractive',\n  'authentic',\n  'authorized',\n  'automatic',\n  'awesome',\n  'beautiful',\n  'beloved',\n  'beneficial',\n  'big',\n  'blissful',\n  'blue',\n  'bold',\n  'bouncy',\n  'brave',\n  'bright',\n  'brilliant',\n  'brisk',\n  'bronze',\n  'busy',\n  'calm',\n  'careful',\n  'caring',\n  'celebrated',\n  'charming',\n  'cheerful',\n  'cheery',\n  'clever',\n  'colorful',\n  'compassionate',\n  'competent',\n  'complete',\n  'composed',\n  'conscious',\n  'considerate',\n  'content',\n  'cool',\n  'courageous',\n  'courteous',\n  'crafty',\n  'creative',\n  'cuddly',\n  'cultivated',\n  'cultured',\n  'cute',\n  'daring',\n  'dazzling',\n  'dearest',\n  'decisive',\n  'deep',\n  'defiant',\n  'definite',\n  'delightful',\n  'dependable',\n  'determined',\n  'diligent',\n  'distinct',\n  'dutiful',\n  'earnest',\n  'elegant',\n  'enchanted',\n  'enchanting',\n  'energetic',\n  'enlightened',\n  'esteemed',\n  'euphoric',\n  'excited',\n  'fabulous',\n  'fancy',\n  'fearless',\n  'generous',\n  'gentle',\n  'genuine',\n  'glamorous',\n  'gleaming',\n  'glittering',\n  'glorious',\n  'golden',\n  'graceful',\n  'gracious',\n  'grand',\n  'great',\n  'green',\n  'handmade',\n  'handsome',\n  'handy',\n  'happy',\n  'harmonious',\n  'healthy',\n  'hearty',\n  'honorable',\n  'imaginative',\n  'impassioned',\n  'impeccable',\n  'impressive',\n  'incomparable',\n  'incredible',\n  'infamous',\n  'innocent',\n  'joyful',\n  'joyous',\n  'jubilant',\n  'knowledgeable',\n  'lawful',\n  'legal',\n  'luminous',\n  'magnificent',\n  'majestic',\n  'major',\n  'minty',\n  'modern',\n  'mysterious',\n  'optimistic',\n  'orange',\n  'orderly',\n  'ornate',\n  'original',\n  'outstanding',\n  'passionate',\n  'peaceful',\n  'pleased',\n  'positive',\n  'powerful',\n  'productive',\n  'proud',\n  'purple',\n  'puzzled',\n  'real',\n  'reasonable',\n  'reliable',\n  'remarkable',\n  'respectful',\n  'responsible',\n  'rusty',\n  'serene',\n  'silky',\n  'silver',\n  'sociable',\n  'solid',\n  'sophisticated',\n  'soulful',\n  'spectacular',\n  'speedy',\n  'splendid',\n  'squiggly',\n  'stimulating',\n  'striking',\n  'striped',\n  'strong',\n  'stunning',\n  'stylish',\n  'substantial',\n  'subtle',\n  'super',\n  'superb',\n  'supportive',\n  'swift',\n  'tangible',\n  'teeming',\n  'terrific',\n  'thoughtful',\n  'timely',\n  'total',\n  'tremendous',\n  'true',\n  'trustworthy',\n  'truthful',\n  'ultimate',\n  'uncommon',\n  'unique',\n  'upbeat',\n  'velvety',\n  'venerated',\n  'verifiable',\n  'vibrant',\n  'victorious',\n  'vigilant',\n  'vigorous',\n  'violet',\n  'virtuous',\n  'vital',\n  'warm',\n  'warmhearted',\n  'watchful',\n  'whole',\n  'wise',\n  'witty',\n  'wonderful',\n  'worthwhile',\n  'worthy',\n  'zealous',\n];\n\nconst sample = (arr: any[]) => {\n  return arr[Math.floor(Math.random() * arr.length)];\n};\n\nconst upperFirst = (str: string) => str[0].toUpperCase() + str.slice(1);\n\nexport const getRandomUsername = () =>\n  `${upperFirst(sample(adjectives))} ${upperFirst(sample(animals))}`;\n"], "names": ["animals", "adjectives", "sample", "arr", "Math", "floor", "random", "length", "upperFirst", "str", "toUpperCase", "slice", "getRandomUsername"], "mappings": ";;;;IAAaA,OAAO,GAAG,CACrB,UADqB,EAErB,WAFqB,EAGrB,WAHqB,EAIrB,QAJqB,EAKrB,KALqB,EAMrB,UANqB,EAOrB,UAPqB,EAQrB,WARqB,EASrB,QATqB,EAUrB,WAVqB,EAWrB,KAXqB,EAYrB,MAZqB,EAarB,KAbqB,EAcrB,OAdqB,EAerB,SAfqB,EAgBrB,WAhBqB,EAiBrB,OAjBqB,EAkBrB,UAlBqB,EAmBrB,SAnBqB,EAoBrB,KApBqB,EAqBrB,aArBqB,EAsBrB,SAtBqB,EAuBrB,YAvBqB,EAwBrB,OAxBqB,EAyBrB,WAzBqB,EA0BrB,QA1BqB,EA2BrB,MA3BqB,EA4BrB,OA5BqB,EA6BrB,WA7BqB,EA8BrB,MA9BqB,EA+BrB,UA/BqB,EAgCrB,SAhCqB,EAiCrB,MAjCqB,EAkCrB,WAlCqB,EAmCrB,MAnCqB,EAoCrB,OApCqB,EAqCrB,SArCqB,EAsCrB,UAtCqB,EAuCrB,KAvCqB,EAwCrB,KAxCqB,EAyCrB,QAzCqB,EA0CrB,QA1CqB,EA2CrB,OA3CqB,EA4CrB,MA5CqB,EA6CrB,UA7CqB,EA8CrB,KA9CqB,EA+CrB,KA/CqB,EAgDrB,SAhDqB,EAiDrB,QAjDqB,EAkDrB,SAlDqB,EAmDrB,WAnDqB,EAoDrB,UApDqB,EAqDrB,SArDqB,EAsDrB,aAtDqB,EAuDrB,MAvDqB,EAwDrB,SAxDqB,EAyDrB,MAzDqB,EA0DrB,MA1DqB,EA2DrB,UA3DqB,EA4DrB,SA5DqB,EA6DrB,cA7DqB,EA8DrB,QA9DqB,EA+DrB,OA/DqB,EAgErB,aAhEqB,EAiErB,MAjEqB,EAkErB,QAlEqB,EAmErB,KAnEqB,EAoErB,WApEqB,EAqErB,UArEqB,EAsErB,YAtEqB,EAuErB,OAvEqB,EAwErB,MAxEqB,EAyErB,OAzEqB,EA0ErB,SA1EqB,EA2ErB,MA3EqB,EA4ErB,OA5EqB,EA6ErB,SA7EqB,EA8ErB,OA9EqB,EA+ErB,UA/EqB,EAgFrB,QAhFqB,EAiFrB,SAjFqB,EAkFrB,UAlFqB,EAmFrB,QAnFqB,EAoFrB,SApFqB,EAqFrB,MArFqB,EAsFrB,UAtFqB,EAuFrB,OAvFqB,EAwFrB,OAxFqB,EAyFrB,SAzFqB,EA0FrB,aA1FqB,EA2FrB,SA3FqB,EA4FrB,OA5FqB,EA6FrB,SA7FqB,EA8FrB,SA9FqB,EA+FrB,OA/FqB,EAgGrB,KAhGqB,EAiGrB,QAjGqB,EAkGrB,SAlGqB,EAmGrB,WAnGqB,EAoGrB,SApGqB,EAqGrB,SArGqB,EAsGrB,UAtGqB,EAuGrB,MAvGqB,EAwGrB,WAxGqB,EAyGrB,UAzGqB,EA0GrB,OA1GqB,EA2GrB,QA3GqB,EA4GrB,KA5GqB,EA6GrB,OA7GqB,EA8GrB,UA9GqB,EA+GrB,YA/GqB,EAgHrB,YAhHqB,EAiHrB,QAjHqB,EAkHrB,WAlHqB,EAmHrB,UAnHqB,EAoHrB,UApHqB,EAqHrB,MArHqB,EAsHrB,SAtHqB,EAuHrB,WAvHqB,EAwHrB,OAxHqB,EAyHrB,UAzHqB,EA0HrB,UA1HqB,EA2HrB,UA3HqB,EA4HrB,OA5HqB,EA6HrB,MA7HqB,EA8HrB,OA9HqB,EA+HrB,OA/HqB,EAgIrB,SAhIqB,EAiIrB,MAjIqB,EAkIrB,WAlIqB,EAmIrB,QAnIqB,EAoIrB,UApIqB,EAqIrB,YArIqB,EAsIrB,KAtIqB,EAuIrB,OAvIqB;IA0IVC,UAAU,GAAG,CACxB,UADwB,EAExB,UAFwB,EAGxB,aAHwB,EAIxB,WAJwB,EAKxB,cALwB,EAMxB,UANwB,EAOxB,WAPwB,EAQxB,QARwB,EASxB,QATwB,EAUxB,OAVwB,EAWxB,WAXwB,EAYxB,SAZwB,EAaxB,YAbwB,EAcxB,UAdwB,EAexB,QAfwB,EAgBxB,UAhBwB,EAiBxB,cAjBwB,EAkBxB,OAlBwB,EAmBxB,OAnBwB,EAoBxB,OApBwB,EAqBxB,YArBwB,EAsBxB,SAtBwB,EAuBxB,WAvBwB,EAwBxB,QAxBwB,EAyBxB,SAzBwB,EA0BxB,SA1BwB,EA2BxB,UA3BwB,EA4BxB,SA5BwB,EA6BxB,cA7BwB,EA8BxB,KA9BwB,EA+BxB,QA/BwB,EAgCxB,UAhCwB,EAiCxB,SAjCwB,EAkCxB,aAlCwB,EAmCxB,UAnCwB,EAoCxB,WApCwB,EAqCxB,YArCwB,EAsCxB,WAtCwB,EAuCxB,YAvCwB,EAwCxB,WAxCwB,EAyCxB,SAzCwB,EA0CxB,WA1CwB,EA2CxB,SA3CwB,EA4CxB,YA5CwB,EA6CxB,KA7CwB,EA8CxB,UA9CwB,EA+CxB,MA/CwB,EAgDxB,MAhDwB,EAiDxB,QAjDwB,EAkDxB,OAlDwB,EAmDxB,QAnDwB,EAoDxB,WApDwB,EAqDxB,OArDwB,EAsDxB,QAtDwB,EAuDxB,MAvDwB,EAwDxB,MAxDwB,EAyDxB,SAzDwB,EA0DxB,QA1DwB,EA2DxB,YA3DwB,EA4DxB,UA5DwB,EA6DxB,UA7DwB,EA8DxB,QA9DwB,EA+DxB,QA/DwB,EAgExB,UAhEwB,EAiExB,eAjEwB,EAkExB,WAlEwB,EAmExB,UAnEwB,EAoExB,UApEwB,EAqExB,WArEwB,EAsExB,aAtEwB,EAuExB,SAvEwB,EAwExB,MAxEwB,EAyExB,YAzEwB,EA0ExB,WA1EwB,EA2ExB,QA3EwB,EA4ExB,UA5EwB,EA6ExB,QA7EwB,EA8ExB,YA9EwB,EA+ExB,UA/EwB,EAgFxB,MAhFwB,EAiFxB,QAjFwB,EAkFxB,UAlFwB,EAmFxB,SAnFwB,EAoFxB,UApFwB,EAqFxB,MArFwB,EAsFxB,SAtFwB,EAuFxB,UAvFwB,EAwFxB,YAxFwB,EAyFxB,YAzFwB,EA0FxB,YA1FwB,EA2FxB,UA3FwB,EA4FxB,UA5FwB,EA6FxB,SA7FwB,EA8FxB,SA9FwB,EA+FxB,SA/FwB,EAgGxB,WAhGwB,EAiGxB,YAjGwB,EAkGxB,WAlGwB,EAmGxB,aAnGwB,EAoGxB,UApGwB,EAqGxB,UArGwB,EAsGxB,SAtGwB,EAuGxB,UAvGwB,EAwGxB,OAxGwB,EAyGxB,UAzGwB,EA0GxB,UA1GwB,EA2GxB,QA3GwB,EA4GxB,SA5GwB,EA6GxB,WA7GwB,EA8GxB,UA9GwB,EA+GxB,YA/GwB,EAgHxB,UAhHwB,EAiHxB,QAjHwB,EAkHxB,UAlHwB,EAmHxB,UAnHwB,EAoHxB,OApHwB,EAqHxB,OArHwB,EAsHxB,OAtHwB,EAuHxB,UAvHwB,EAwHxB,UAxHwB,EAyHxB,OAzHwB,EA0HxB,OA1HwB,EA2HxB,YA3HwB,EA4HxB,SA5HwB,EA6HxB,QA7HwB,EA8HxB,WA9HwB,EA+HxB,aA/HwB,EAgIxB,aAhIwB,EAiIxB,YAjIwB,EAkIxB,YAlIwB,EAmIxB,cAnIwB,EAoIxB,YApIwB,EAqIxB,UArIwB,EAsIxB,UAtIwB,EAuIxB,QAvIwB,EAwIxB,QAxIwB,EAyIxB,UAzIwB,EA0IxB,eA1IwB,EA2IxB,QA3IwB,EA4IxB,OA5IwB,EA6IxB,UA7IwB,EA8IxB,aA9IwB,EA+IxB,UA/IwB,EAgJxB,OAhJwB,EAiJxB,OAjJwB,EAkJxB,QAlJwB,EAmJxB,YAnJwB,EAoJxB,YApJwB,EAqJxB,QArJwB,EAsJxB,SAtJwB,EAuJxB,QAvJwB,EAwJxB,UAxJwB,EAyJxB,aAzJwB,EA0JxB,YA1JwB,EA2JxB,UA3JwB,EA4JxB,SA5JwB,EA6JxB,UA7JwB,EA8JxB,UA9JwB,EA+JxB,YA/JwB,EAgKxB,OAhKwB,EAiKxB,QAjKwB,EAkKxB,SAlKwB,EAmKxB,MAnKwB,EAoKxB,YApKwB,EAqKxB,UArKwB,EAsKxB,YAtKwB,EAuKxB,YAvKwB,EAwKxB,aAxKwB,EAyKxB,OAzKwB,EA0KxB,QA1KwB,EA2KxB,OA3KwB,EA4KxB,QA5KwB,EA6KxB,UA7KwB,EA8KxB,OA9KwB,EA+KxB,eA/KwB,EAgLxB,SAhLwB,EAiLxB,aAjLwB,EAkLxB,QAlLwB,EAmLxB,UAnLwB,EAoLxB,UApLwB,EAqLxB,aArLwB,EAsLxB,UAtLwB,EAuLxB,SAvLwB,EAwLxB,QAxLwB,EAyLxB,UAzLwB,EA0LxB,SA1LwB,EA2LxB,aA3LwB,EA4LxB,QA5LwB,EA6LxB,OA7LwB,EA8LxB,QA9LwB,EA+LxB,YA/LwB,EAgMxB,OAhMwB,EAiMxB,UAjMwB,EAkMxB,SAlMwB,EAmMxB,UAnMwB,EAoMxB,YApMwB,EAqMxB,QArMwB,EAsMxB,OAtMwB,EAuMxB,YAvMwB,EAwMxB,MAxMwB,EAyMxB,aAzMwB,EA0MxB,UA1MwB,EA2MxB,UA3MwB,EA4MxB,UA5MwB,EA6MxB,QA7MwB,EA8MxB,QA9MwB,EA+MxB,SA/MwB,EAgNxB,WAhNwB,EAiNxB,YAjNwB,EAkNxB,SAlNwB,EAmNxB,YAnNwB,EAoNxB,UApNwB,EAqNxB,UArNwB,EAsNxB,QAtNwB,EAuNxB,UAvNwB,EAwNxB,OAxNwB,EAyNxB,MAzNwB,EA0NxB,aA1NwB,EA2NxB,UA3NwB,EA4NxB,OA5NwB,EA6NxB,MA7NwB,EA8NxB,OA9NwB,EA+NxB,WA/NwB,EAgOxB,YAhOwB,EAiOxB,QAjOwB,EAkOxB,SAlOwB;;AAqO1B,IAAMC,MAAM,GAAG,SAATA,MAAS,CAACC,GAAD;AACb,SAAOA,GAAG,CAACC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBH,GAAG,CAACI,MAA/B,CAAD,CAAV;AACD,CAFD;;AAIA,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAACC,GAAD;AAAA,SAAiBA,GAAG,CAAC,CAAD,CAAH,CAAOC,WAAP,KAAuBD,GAAG,CAACE,KAAJ,CAAU,CAAV,CAAxC;AAAA,CAAnB;;IAEaC,iBAAiB,GAAG,SAApBA,iBAAoB;AAAA,SAC5BJ,UAAU,CAACN,MAAM,CAACD,UAAD,CAAP,CADkB,SACMO,UAAU,CAACN,MAAM,CAACF,OAAD,CAAP,CADhB;AAAA;;;;;;"}