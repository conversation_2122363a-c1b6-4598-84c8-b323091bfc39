{"version": 3, "file": "random-username.cjs.production.min.js", "sources": ["../src/index.ts"], "sourcesContent": ["export const animals = [\n  'aardvark',\n  'albatross',\n  'alligator',\n  'alpaca',\n  'ant',\n  'anteater',\n  'antelope',\n  'armadillo',\n  'badger',\n  'barracuda',\n  'bat',\n  'bear',\n  'bee',\n  'bison',\n  'buffalo',\n  'butterfly',\n  'camel',\n  'capybara',\n  'caribou',\n  'cat',\n  'caterpillar',\n  'cheetah',\n  'chinchilla',\n  'cobra',\n  'cormorant',\n  'coyote',\n  'crab',\n  'crane',\n  'crocodile',\n  'deer',\n  'dinosaur',\n  'dolphin',\n  'dove',\n  'dragonfly',\n  'duck',\n  'eagle',\n  'echidna',\n  'elephant',\n  'elk',\n  'emu',\n  'falcon',\n  'ferret',\n  'finch',\n  'fish',\n  'flamingo',\n  'fly',\n  'fox',\n  'gazelle',\n  'gerbil',\n  'giraffe',\n  'goldfinch',\n  'goldfish',\n  'gorilla',\n  'grasshopper',\n  'gull',\n  'hamster',\n  'hare',\n  'hawk',\n  'hedgehog',\n  'herring',\n  'hippopotamus',\n  'hornet',\n  'horse',\n  'hummingbird',\n  'ibex',\n  'jaguar',\n  'jay',\n  'jellyfish',\n  'kangaroo',\n  'kingfisher',\n  'koala',\n  'lark',\n  'lemur',\n  'leopard',\n  'lion',\n  'llama',\n  'lobster',\n  'loris',\n  'lyrebird',\n  'magpie',\n  'manatee',\n  'mandrill',\n  'mantis',\n  'meerkat',\n  'mink',\n  'mongoose',\n  'moose',\n  'mouse',\n  'narwhal',\n  'nightingale',\n  'octopus',\n  'okapi',\n  'opossum',\n  'ostrich',\n  'otter',\n  'owl',\n  'oyster',\n  'panther',\n  'partridge',\n  'pelican',\n  'penguin',\n  'pheasant',\n  'pony',\n  'porcupine',\n  'porpoise',\n  'quail',\n  'rabbit',\n  'ram',\n  'raven',\n  'reindeer',\n  'rhinoceros',\n  'salamander',\n  'salmon',\n  'sandpiper',\n  'scorpion',\n  'seahorse',\n  'seal',\n  'sparrow',\n  'spoonbill',\n  'squid',\n  'squirrel',\n  'starling',\n  'stingray',\n  'stork',\n  'swan',\n  'tiger',\n  'whale',\n  'wildcat',\n  'wolf',\n  'wolverine',\n  'wombat',\n  'woodcock',\n  'woodpecker',\n  'yak',\n  'zebra',\n];\n\nexport const adjectives = [\n  'absolute',\n  'adorable',\n  'adventurous',\n  'acclaimed',\n  'accomplished',\n  'accurate',\n  'acrobatic',\n  'active',\n  'actual',\n  'adept',\n  'admirable',\n  'admired',\n  'adolescent',\n  'adorable',\n  'adored',\n  'advanced',\n  'affectionate',\n  'agile',\n  'alert',\n  'alive',\n  'altruistic',\n  'amazing',\n  'ambitious',\n  'amused',\n  'amusing',\n  'ancient',\n  'animated',\n  'antique',\n  'apprehensive',\n  'apt',\n  'arctic',\n  'artistic',\n  'assured',\n  'astonishing',\n  'athletic',\n  'attentive',\n  'attractive',\n  'authentic',\n  'authorized',\n  'automatic',\n  'awesome',\n  'beautiful',\n  'beloved',\n  'beneficial',\n  'big',\n  'blissful',\n  'blue',\n  'bold',\n  'bouncy',\n  'brave',\n  'bright',\n  'brilliant',\n  'brisk',\n  'bronze',\n  'busy',\n  'calm',\n  'careful',\n  'caring',\n  'celebrated',\n  'charming',\n  'cheerful',\n  'cheery',\n  'clever',\n  'colorful',\n  'compassionate',\n  'competent',\n  'complete',\n  'composed',\n  'conscious',\n  'considerate',\n  'content',\n  'cool',\n  'courageous',\n  'courteous',\n  'crafty',\n  'creative',\n  'cuddly',\n  'cultivated',\n  'cultured',\n  'cute',\n  'daring',\n  'dazzling',\n  'dearest',\n  'decisive',\n  'deep',\n  'defiant',\n  'definite',\n  'delightful',\n  'dependable',\n  'determined',\n  'diligent',\n  'distinct',\n  'dutiful',\n  'earnest',\n  'elegant',\n  'enchanted',\n  'enchanting',\n  'energetic',\n  'enlightened',\n  'esteemed',\n  'euphoric',\n  'excited',\n  'fabulous',\n  'fancy',\n  'fearless',\n  'generous',\n  'gentle',\n  'genuine',\n  'glamorous',\n  'gleaming',\n  'glittering',\n  'glorious',\n  'golden',\n  'graceful',\n  'gracious',\n  'grand',\n  'great',\n  'green',\n  'handmade',\n  'handsome',\n  'handy',\n  'happy',\n  'harmonious',\n  'healthy',\n  'hearty',\n  'honorable',\n  'imaginative',\n  'impassioned',\n  'impeccable',\n  'impressive',\n  'incomparable',\n  'incredible',\n  'infamous',\n  'innocent',\n  'joyful',\n  'joyous',\n  'jubilant',\n  'knowledgeable',\n  'lawful',\n  'legal',\n  'luminous',\n  'magnificent',\n  'majestic',\n  'major',\n  'minty',\n  'modern',\n  'mysterious',\n  'optimistic',\n  'orange',\n  'orderly',\n  'ornate',\n  'original',\n  'outstanding',\n  'passionate',\n  'peaceful',\n  'pleased',\n  'positive',\n  'powerful',\n  'productive',\n  'proud',\n  'purple',\n  'puzzled',\n  'real',\n  'reasonable',\n  'reliable',\n  'remarkable',\n  'respectful',\n  'responsible',\n  'rusty',\n  'serene',\n  'silky',\n  'silver',\n  'sociable',\n  'solid',\n  'sophisticated',\n  'soulful',\n  'spectacular',\n  'speedy',\n  'splendid',\n  'squiggly',\n  'stimulating',\n  'striking',\n  'striped',\n  'strong',\n  'stunning',\n  'stylish',\n  'substantial',\n  'subtle',\n  'super',\n  'superb',\n  'supportive',\n  'swift',\n  'tangible',\n  'teeming',\n  'terrific',\n  'thoughtful',\n  'timely',\n  'total',\n  'tremendous',\n  'true',\n  'trustworthy',\n  'truthful',\n  'ultimate',\n  'uncommon',\n  'unique',\n  'upbeat',\n  'velvety',\n  'venerated',\n  'verifiable',\n  'vibrant',\n  'victorious',\n  'vigilant',\n  'vigorous',\n  'violet',\n  'virtuous',\n  'vital',\n  'warm',\n  'warmhearted',\n  'watchful',\n  'whole',\n  'wise',\n  'witty',\n  'wonderful',\n  'worthwhile',\n  'worthy',\n  'zealous',\n];\n\nconst sample = (arr: any[]) => {\n  return arr[Math.floor(Math.random() * arr.length)];\n};\n\nconst upperFirst = (str: string) => str[0].toUpperCase() + str.slice(1);\n\nexport const getRandomUsername = () =>\n  `${upperFirst(sample(adjectives))} ${upperFirst(sample(animals))}`;\n"], "names": ["animals", "adjectives", "sample", "arr", "Math", "floor", "random", "length", "upperFirst", "str", "toUpperCase", "slice"], "mappings": "wEAAaA,EAAU,CACrB,WACA,YACA,YACA,SACA,MACA,WACA,WACA,YACA,SACA,YACA,MACA,OACA,MACA,QACA,UACA,YACA,QACA,WACA,UACA,MACA,cACA,UACA,aACA,QACA,YACA,SACA,OACA,QACA,YACA,OACA,WACA,UACA,OACA,YACA,OACA,QACA,UACA,WACA,MACA,MACA,SACA,SACA,QACA,OACA,WACA,MACA,MACA,UACA,SACA,UACA,YACA,WACA,UACA,cACA,OACA,UACA,OACA,OACA,WACA,UACA,eACA,SACA,QACA,cACA,OACA,SACA,MACA,YACA,WACA,aACA,QACA,OACA,QACA,UACA,OACA,QACA,UACA,QACA,WACA,SACA,UACA,WACA,SACA,UACA,OACA,WACA,QACA,QACA,UACA,cACA,UACA,QACA,UACA,UACA,QACA,MACA,SACA,UACA,YACA,UACA,UACA,WACA,OACA,YACA,WACA,QACA,SACA,MACA,QACA,WACA,aACA,aACA,SACA,YACA,WACA,WACA,OACA,UACA,YACA,QACA,WACA,WACA,WACA,QACA,OACA,QACA,QACA,UACA,OACA,YACA,SACA,WACA,aACA,MACA,SAGWC,EAAa,CACxB,WACA,WACA,cACA,YACA,eACA,WACA,YACA,SACA,SACA,QACA,YACA,UACA,aACA,WACA,SACA,WACA,eACA,QACA,QACA,QACA,aACA,UACA,YACA,SACA,UACA,UACA,WACA,UACA,eACA,MACA,SACA,WACA,UACA,cACA,WACA,YACA,aACA,YACA,aACA,YACA,UACA,YACA,UACA,aACA,MACA,WACA,OACA,OACA,SACA,QACA,SACA,YACA,QACA,SACA,OACA,OACA,UACA,SACA,aACA,WACA,WACA,SACA,SACA,WACA,gBACA,YACA,WACA,WACA,YACA,cACA,UACA,OACA,aACA,YACA,SACA,WACA,SACA,aACA,WACA,OACA,SACA,WACA,UACA,WACA,OACA,UACA,WACA,aACA,aACA,aACA,WACA,WACA,UACA,UACA,UACA,YACA,aACA,YACA,cACA,WACA,WACA,UACA,WACA,QACA,WACA,WACA,SACA,UACA,YACA,WACA,aACA,WACA,SACA,WACA,WACA,QACA,QACA,QACA,WACA,WACA,QACA,QACA,aACA,UACA,SACA,YACA,cACA,cACA,aACA,aACA,eACA,aACA,WACA,WACA,SACA,SACA,WACA,gBACA,SACA,QACA,WACA,cACA,WACA,QACA,QACA,SACA,aACA,aACA,SACA,UACA,SACA,WACA,cACA,aACA,WACA,UACA,WACA,WACA,aACA,QACA,SACA,UACA,OACA,aACA,WACA,aACA,aACA,cACA,QACA,SACA,QACA,SACA,WACA,QACA,gBACA,UACA,cACA,SACA,WACA,WACA,cACA,WACA,UACA,SACA,WACA,UACA,cACA,SACA,QACA,SACA,aACA,QACA,WACA,UACA,WACA,aACA,SACA,QACA,aACA,OACA,cACA,WACA,WACA,WACA,SACA,SACA,UACA,YACA,aACA,UACA,aACA,WACA,WACA,SACA,WACA,QACA,OACA,cACA,WACA,QACA,OACA,QACA,YACA,aACA,SACA,WAGIC,EAAS,SAACC,UACPA,EAAIC,KAAKC,MAAMD,KAAKE,SAAWH,EAAII,UAGtCC,EAAa,SAACC,UAAgBA,EAAI,GAAGC,cAAgBD,EAAIE,MAAM,qEAEpC,kBAC5BH,EAAWN,EAAOD,QAAgBO,EAAWN,EAAOF"}