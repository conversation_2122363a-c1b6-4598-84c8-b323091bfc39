{"version": "1.1.0", "license": "MIT", "main": "dist/index.js", "typings": "dist/index.d.ts", "files": ["dist", "src"], "repository": "https://github.com/excalidraw/random-username", "publishConfig": {"access": "public"}, "engines": {"node": ">=10"}, "scripts": {"start": "tsdx watch", "build": "tsdx build", "test": "tsdx test", "lint": "tsdx lint", "prepare": "tsdx build", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {}, "husky": {"hooks": {"pre-commit": "tsdx lint"}}, "prettier": {"printWidth": 80, "semi": true, "singleQuote": true, "trailingComma": "es5"}, "name": "@excalidraw/random-username", "author": "dwelle", "module": "dist/random-username.esm.js", "size-limit": [{"path": "dist/random-username.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/random-username.esm.js", "limit": "10 KB"}], "devDependencies": {"@size-limit/preset-small-lib": "^6.0.3", "husky": "^7.0.2", "size-limit": "^6.0.3", "tsdx": "^0.14.1", "tslib": "^2.3.1", "typescript": "^4.4.4"}}