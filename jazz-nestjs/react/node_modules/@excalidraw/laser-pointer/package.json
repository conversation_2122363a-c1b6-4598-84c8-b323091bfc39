{"name": "@excalidraw/laser-pointer", "version": "1.3.1", "description": "Generate outline for laser pointer tool", "source": "src/index.ts", "main": "dist/cjs.js", "module": "dist/esm.js", "types": "dist/types.d.ts", "scripts": {"build": "parcel build", "dev": "parcel watch", "test": "playwright test", "test:server": "parcel build && serve .", "prepublishOnly": "parcel build"}, "keywords": ["excalidraw", "laserpointer"], "license": "MIT", "publishConfig": {"access": "public"}, "devDependencies": {"@parcel/packager-ts": "^2.9.3", "@parcel/transformer-typescript-types": "^2.9.3", "@playwright/test": "^1.38.0", "@types/node": "^20.6.2", "parcel": "^2.9.3", "serve": "^14.2.1", "typescript": "^5.2.2"}}