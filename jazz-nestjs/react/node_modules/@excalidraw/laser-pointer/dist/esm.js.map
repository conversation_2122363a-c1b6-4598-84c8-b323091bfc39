{"mappings": ";;;;;;;AEEO,SAAS,0CAAI,CAAC,IAAI,IAAI,GAAU,EAAE,CAAC,IAAI,IAAI,GAAU;IAC1D,OAAO;QAAC,KAAK;QAAI,KAAK;QAAI,KAAK;KAAG;AACpC;AAEO,SAAS,0CAAI,CAAC,IAAI,IAAI,GAAU,EAAE,CAAC,IAAI,IAAI,GAAU;IAC1D,OAAO;QAAC,KAAK;QAAI,KAAK;QAAI,KAAK;KAAG;AACpC;AAEO,SAAS,0CAAK,CAAC,GAAG,GAAG,EAAS,EAAE,CAAS;IAC9C,OAAO;QAAC,IAAI;QAAG,IAAI;QAAG,IAAI;KAAE;AAC9B;AAEO,SAAS,0CAAK,CAAC,GAAG,GAAG,EAAS;IACnC,OAAO;QAAC,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;QAAI,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;QAAI;KAAE;AAC5E;AAEO,SAAS,0CAAI,CAAC,GAAG,GAAG,EAAS,EAAE,GAAW;IAC/C,OAAO;QAAC,KAAK,GAAG,CAAC,OAAO,IAAI,KAAK,GAAG,CAAC,OAAO;QAAG,KAAK,GAAG,CAAC,OAAO,IAAI,KAAK,GAAG,CAAC,OAAO;QAAG;KAAE;AAC1F;AAEO,SAAS,0CAAM,CAAQ,EAAE,CAAQ,EAAE,CAAS;IACjD,OAAO,0CAAI,GAAG,0CAAK,0CAAI,GAAG,IAAI;AAChC;AAEO,SAAS,0CAAK,CAAS,EAAE,CAAS,EAAE,CAAS;IAClD,OAAO,IAAI,AAAC,CAAA,IAAI,CAAA,IAAK;AACvB;AAEO,SAAS,0CAAM,CAAQ,EAAE,EAAS,EAAE,EAAS;IAClD,OAAO,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AACvF;AAEO,SAAS,0CAAU,CAAS;IACjC,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAC1C;AAEO,SAAS,0CAAI,CAAC,GAAG,EAAS;IAC/B,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AACjC;AAEO,SAAS,0CAAK,CAAC,IAAI,GAAU,EAAE,CAAC,IAAI,GAAU;IACnD,OAAO,KAAK,IAAI,CAAC,AAAC,CAAA,KAAK,EAAC,KAAM,IAAI,AAAC,CAAA,KAAK,EAAC,KAAM;AACjD;AAEO,SAAS,0CACd,KAAY,EACZ,SAAgB,EAChB,MAAc;IAEd,OAAO;QACL,0CAAI,OAAO,0CAAK,0CAAK,0CAAI,WAAW,KAAK,EAAE,GAAG,KAAK;QACnD,0CAAI,OAAO,0CAAK,0CAAK,0CAAI,WAAW,CAAC,KAAK,EAAE,GAAG,KAAK;KACrD;AACH;AAEO,SAAS,0CAAU,EAAW;IACnC,IAAI,GAAG,MAAM,GAAG,GAAG,OAAO;IAE1B,IAAI,MAAM;IAEV,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,GAAG,GAAG,IAClC,OAAO,0CAAK,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;IAG9B,OAAO,0CAAK,EAAE,CAAC,GAAG,MAAM,GAAE,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE;IAE/C,OAAO;AACT;AAEO,MAAM,4CAAQ,CAAC,GAAW,KAAa,MAAgB,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK;AAEnF,SAAS,0CAAuB,EAAS,EAAE,EAAS,EAAE,EAAS;IACpE,MAAM,OAAO,0CAAK,IAAI;IAEtB,IAAI,SAAS,GAAG,OAAO,0CAAK,IAAI;IAEhC,MAAM,IAAI,0CAAM,AAAC,CAAA,AAAC,CAAA,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,AAAD,IAAM,CAAA,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,AAAD,IAAK,AAAC,CAAA,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,AAAD,IAAM,CAAA,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,AAAD,CAAC,IAAK,QAAQ,GAAG,GAAG;IAExG,MAAM,KAAY;QAAC,EAAE,CAAC,EAAE,GAAG,IAAK,CAAA,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,AAAD;QAAI,EAAE,CAAC,EAAE,GAAG,IAAK,CAAA,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,AAAD;QAAI,EAAE,CAAC,EAAE;KAAC;IAEnF,OAAO,0CAAK,IAAI;AAClB;;;;ACjFO,SAAS,0CAAe,MAAe,EAAE,OAAe;IAC7D,IAAI,YAAY,GACd,OAAO;IAGT,IAAI,OAAO,MAAM,IAAI,GACnB,OAAO;IAGT,MAAM,QAAQ,MAAM,CAAC,EAAE;IACvB,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;IAEtC,MAAM,CAAC,aAAa,SAAS,GAAG,OAAO,MAAM,CAC3C,CAAC,CAAC,aAAa,SAAS,EAAE,OAAO;QAC/B,MAAM,WAAW,CAAA,GAAA,yCAAqB,EAAE,OAAO,OAAO;QAEtD,OAAO,WAAW,cAAc;YAAC;YAAU;SAAM,GAAG;YAAC;YAAa;SAAS;IAC7E,GACA;QAAC;QAAG;KAAG;IAGT,IAAI,eAAe,SAAS;QAC1B,MAAM,gBAAgB,MAAM,CAAC,SAAS;QAEtC,OAAO;eACF,0CAAe;gBAAC;mBAAU,OAAO,KAAK,CAAC,GAAG;gBAAW;aAAc,EAAE,SAAS,KAAK,CAAC,GAAG;YAC1F;eACG,0CAAe;gBAAC;mBAAkB,OAAO,KAAK,CAAC,UAAU;gBAAK;aAAK,EAAE,SAAS,KAAK,CAAC;SACxF;IACH,OACE,OAAO;QAAC;QAAO;KAAK;AAExB;;;AFXO,MAAM;;aACJ,WAAgC;YACrC,MAAM;YACN,YAAY;YACZ,UAAU;YACV,eAAe;YACf,UAAU;YAEV,aAAa,IAAM;QACrB;;;aAEO,YAAY;YACjB,yBAAyB;YACzB,yBAAyB,CAAC,IAAe,IAAI,KAAK,MAAM;YACxD,eAAe;QACjB;;IAGA,YAAY,OAAqC,CAAE;aAInD,iBAA0B,EAAE;aAEpB,eAAwB,EAAE;aAC1B,aAAsB,EAAE;aAExB,UAAU;QARhB,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,0CAAa,QAAQ,EAAE;IAC1D;IASA,IAAY,YAAmB;QAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE;IACvG;IAEA,SAAS,KAAY,EAAE;QACrB,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE;QAErE,IAAI,aAAa,SAAS,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,EACrE;QAGF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QAEzB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACvB;QACF;QAEA,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,GAC5B,QAAQ,0CAAQ,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU;QAGpE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QAErB,IAAI,0CAAY,IAAI,CAAC,UAAU,IAAI,0CAAa,SAAS,CAAC,aAAa,EACrE,IAAI,CAAC,aAAa;IAEtB;IAEA,QAAQ;QACN,IAAI,CAAC,aAAa;IACpB;IAEA,gBAAgB;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,QAC7D,MAAM,IAAI,MAAM;aACX;YACL,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU;YACzC,IAAI,CAAC,UAAU,GAAG,EAAE;QACtB;IACF;IAEQ,QACN,YAAgC,EAChC,QAAgB,EAChB,KAAa,EACb,WAAmB,EACnB,aAAqB,EACrB;QACA,OACE,AAAC,CAAA,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,AAAD,IACjC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;YACvB,UAAU;YACV,eAAe;YACf,cAAc;YACd,aAAa;QACf;IAEJ;IAEA,iBAAiB,YAAiC,EAAW;QAC3D,IAAI,IAAI,CAAC,OAAO,EACd,OAAO,EAAE;QAGX,IAAI,SAAS;eAAI,IAAI,CAAC,YAAY;eAAK,IAAI,CAAC,UAAU;SAAC;QAEvD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,SAC9D,SAAS,CAAA,GAAA,yCAAa,EAAE,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ;QAGvD,MAAM,MAAM,OAAO,MAAM;QAEzB,IAAI,QAAQ,GACV,OAAO,EAAE;QAGX,IAAI,QAAQ,GAAG;YACb,MAAM,IAAI,MAAM,CAAC,EAAE;YAEnB,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK;YAEtD,IAAI,OAAO,KACT,OAAO,EAAE;YAGX,MAAM,KAAc,EAAE;YAEtB,IAAK,IAAI,QAAQ,GAAG,SAAS,KAAK,EAAE,GAAG,GAAG,SAAS,KAAK,EAAE,GAAG,GAC3D,GAAG,IAAI,CAAC,0CAAM,GAAG,0CAAO,0CAAM;gBAAC;gBAAG;gBAAG;aAAE,EAAW,QAAQ;YAG5D,GAAG,IAAI,CAAC,0CAAM,GAAG,0CAAO;gBAAC;gBAAG;gBAAG;aAAE,EAAW,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK;YAErF,OAAO;QACT;QAEA,IAAI,QAAQ,GAAG;YACb,MAAM,IAAI,MAAM,CAAC,EAAE;YACnB,MAAM,IAAI,MAAM,CAAC,EAAE;YAEnB,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK;YACvD,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK;YAEvD,IAAI,QAAQ,OAAO,QAAQ,KACzB,OAAO,EAAE;YAGX,MAAM,KAAc,EAAE;YAEtB,MAAM,SAAS,0CAAQ,GAAG;gBAAC,CAAC,CAAC,EAAE;gBAAE,CAAC,CAAC,EAAE,GAAG;gBAAK,CAAC,CAAC,EAAE;aAAC,EAAW;YAE7D,IAAK,IAAI,QAAQ,QAAQ,SAAS,KAAK,EAAE,GAAG,QAAQ,SAAS,KAAK,EAAE,GAAG,GACrE,GAAG,IAAI,CAAC,0CAAM,GAAG,0CAAO,0CAAM;gBAAC;gBAAG;gBAAG;aAAE,EAAW,QAAQ;YAG5D,IAAK,IAAI,QAAQ,KAAK,EAAE,GAAG,QAAQ,SAAS,KAAK,EAAE,GAAG,IAAI,QAAQ,SAAS,KAAK,EAAE,GAAG,GACnF,GAAG,IAAI,CAAC,0CAAM,GAAG,0CAAO,0CAAM;gBAAC;gBAAG;gBAAG;aAAE,EAAW,QAAQ;YAG5D,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE;YAEb,OAAO;QACT;QAEA,MAAM,gBAAyB,EAAE;QACjC,MAAM,iBAA0B,EAAE;QAElC,IAAI,QAAQ;QACZ,IAAI,YAAY;QAEhB,IAAI,oBAAoB;QACxB,IAAI,gBAAgB;QAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,GAAG,IAAK;YAChC,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,EACrB,IAAI,MAAM,CAAC,EAAE,EACb,IAAI,MAAM,CAAC,IAAI,EAAE;YAEnB,IAAI,WAAW,CAAC,CAAC,EAAE;YAEnB,MAAM,IAAI,0CAAO,GAAG;YACpB,iBAAiB;YACjB,QAAQ,YAAY,AAAC,CAAA,IAAI,SAAQ,IAAK;YAEtC,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,cAAc,UAAU,GAAG,KAAK;YAE3D,IAAI,UAAU,GAAG;gBACf,oBAAoB,IAAI;gBACxB;YACF;YAEA,MAAM,QAAQ,0CAAO,0CAAM,GAAG;YAC9B,MAAM,QAAQ,0CAAO,0CAAM,GAAG;YAC9B,MAAM,UAAU,0CAAM,OAAO,KAAK,EAAE,GAAG;YACvC,MAAM,UAAU,0CAAM,OAAO,CAAC,KAAK,EAAE,GAAG;YACxC,MAAM,UAAU,0CAAM,OAAO,KAAK,EAAE,GAAG;YACvC,MAAM,UAAU,0CAAM,OAAO,CAAC,KAAK,EAAE,GAAG;YAExC,MAAM,OAAO,0CAAM,GAAG,0CAAO,SAAS;YACtC,MAAM,OAAO,0CAAM,GAAG,0CAAO,SAAS;YACtC,MAAM,OAAO,0CAAM,GAAG,0CAAO,SAAS;YACtC,MAAM,OAAO,0CAAM,GAAG,0CAAO,SAAS;YAEtC,MAAM,QAAQ,0CAAM,SAAS;YAC7B,MAAM,QAAQ,0CAAM,SAAS;YAE7B,MAAM,OAAO,0CAAM,GAAG,0CAAO,0CAAM,WAAW,IAAI,QAAQ,0CAAO,QAAQ;YACzE,MAAM,OAAO,0CAAM,GAAG,0CAAO,0CAAM,WAAW,IAAI,QAAQ,0CAAO,QAAQ;YAEzE,MAAM,SAAS,0CAAY,0CAAQ,GAAG,GAAG;YACzC,MAAM,UACJ,AAAC,0CAAa,SAAS,CAAC,uBAAuB,GAAG,MAClD,KAAK,EAAE,GACP,0CAAa,SAAS,CAAC,uBAAuB,CAAC;YAEjD,IAAI,KAAK,GAAG,CAAC,UAAU,SAAS;gBAC9B,MAAM,SAAS,KAAK,GAAG,CAAC,0CAAY,KAAK,EAAE,GAAG,SAAS,aAAa;;gBAEpE,IAAI,WAAW,GACb;gBAGF,IAAI,SAAS,GAAG;oBACd,eAAe,IAAI,CAAC,MAAM;oBAE1B,IAAK,IAAI,QAAQ,GAAG,SAAS,QAAQ,SAAS,SAAS,EACrD,cAAc,IAAI,CAAC,0CAAM,GAAG,0CAAM,0CAAO,SAAS,QAAQ;oBAG5D,IAAK,IAAI,QAAQ,QAAQ,SAAS,GAAG,SAAS,SAAS,EACrD,eAAe,IAAI,CAAC,0CAAM,GAAG,0CAAM,0CAAO,SAAS,QAAQ;oBAG7D,eAAe,IAAI,CAAC,MAAM;gBAC5B,OAAO;oBACL,cAAc,IAAI,CAAC,MAAM;oBAEzB,IAAK,IAAI,QAAQ,GAAG,SAAS,QAAQ,SAAS,SAAS,EACrD,eAAe,IAAI,CAAC,0CAAM,GAAG,0CAAM,0CAAO,SAAS,CAAC,QAAQ,CAAC;oBAG/D,IAAK,IAAI,QAAQ,QAAQ,SAAS,GAAG,SAAS,SAAS,EACrD,cAAc,IAAI,CAAC,0CAAM,GAAG,0CAAM,0CAAO,SAAS,CAAC,QAAQ,CAAC;oBAE9D,cAAc,IAAI,CAAC,MAAM;gBAC3B;YACF,OAAO;gBACL,cAAc,IAAI,CAAC;gBACnB,eAAe,IAAI,CAAC;YACtB;YAEA,YAAY;QACd;QAEA,IAAI,qBAAqB,MAAM,GAAG;YAChC,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;gBACzB,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;gBAEzB,MAAM,KAAc,EAAE;gBAEtB,IAAK,IAAI,QAAQ,GAAG,SAAS,KAAK,EAAE,GAAG,GAAG,SAAS,KAAK,EAAE,GAAG,GAC3D,GAAG,IAAI,CAAC,0CAAM,GAAG,0CAAO,0CAAM;oBAAC;oBAAG;oBAAG;iBAAE,EAAW,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI;gBAG7E,GAAG,IAAI,CAAC,0CAAM,GAAG,0CAAO;oBAAC;oBAAG;oBAAG;iBAAE,EAAW,IAAI,CAAC,OAAO,CAAC,IAAI;gBAE7D,OAAO;YACT,OACE,OAAO,EAAE;QAEb;QAEA,MAAM,QAAQ,MAAM,CAAC,kBAAkB;QACvC,MAAM,SAAS,MAAM,CAAC,oBAAoB,EAAE;QAC5C,MAAM,cAAc,MAAM,CAAC,MAAM,EAAE;QACnC,MAAM,WAAW,MAAM,CAAC,MAAM,EAAE;QAEhC,MAAM,QAAQ,0CAAO,0CAAM,QAAQ;QACnC,MAAM,QAAQ,0CAAO,0CAAM,aAAa;QAExC,MAAM,UAAU,0CAAM,OAAO,CAAC,KAAK,EAAE,GAAG;QACxC,MAAM,UAAU,0CAAM,OAAO,KAAK,EAAE,GAAG;QAEvC,MAAM,eAAe,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE,EAAE,GAAG,KAAK;QAClE,MAAM,WAAoB,EAAE;QAE5B,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC,QAAQ,GACpC,IAAI,CAAC,OAAO,CAAC,IAAI,GACjB,IAAI,CAAC,OAAO,CAAC,cAAc,WAAW,CAAC,EAAE,EAAE,MAAM,GAAG,KAAK;QAE7D,MAAM,SAAkB,EAAE;QAE1B,IAAI,eAAe,GAAG;YACpB,IAAK,IAAI,QAAQ,GAAG,SAAS,KAAK,EAAE,EAAE,SAAS,KAAK,EAAE,GAAG,GACvD,SAAS,OAAO,CAAC,0CAAM,OAAO,0CAAM,0CAAO,SAAS,eAAe,CAAC;YAGtE,SAAS,OAAO,CAAC,0CAAM,OAAO,0CAAO,SAAS,CAAC;QACjD,OACE,SAAS,IAAI,CAAC;QAGhB,IAAK,IAAI,QAAQ,GAAG,SAAS,KAAK,EAAE,GAAG,GAAG,SAAS,KAAK,EAAE,GAAG,GAC3D,OAAO,IAAI,CAAC,0CAAM,UAAU,0CAAM,0CAAO,SAAS,CAAC,aAAa,CAAC;QAGnE,MAAM,gBAAgB;eAAI;eAAa;eAAkB,OAAO,OAAO;eAAO,eAAe,OAAO;SAAG;QAEvG,IAAI,SAAS,MAAM,GAAG,GACpB,cAAc,IAAI,CAAC,QAAQ,CAAC,EAAE;QAGhC,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,UAC9D,OAAO,CAAA,GAAA,yCAAa,EAAE,eAAe,IAAI,CAAC,OAAO,CAAC,QAAQ;QAG5D,OAAO;IACT;AACF", "sources": ["src/index.ts", "src/state.ts", "src/math.ts", "src/simplify.ts"], "sourcesContent": ["export * from './state'\nexport type { Point } from './math'\n", "import type { Point } from './math'\nimport * as m from './math'\nimport { do<PERSON><PERSON><PERSON><PERSON><PERSON> } from './simplify'\n\nexport type SizeMappingDetails = {\n  pressure: number\n  runningLength: number\n  currentIndex: number\n  totalLength: number\n}\n\nexport type LaserPointerOptions = {\n  size: number\n\n  streamline: number\n  simplify: number\n  simplifyPhase: 'tail' | 'output' | 'input'\n\n  keepHead: boolean\n\n  sizeMapping: (details: SizeMappingDetails) => number\n}\n\nexport class LaserPointer {\n  static defaults: LaserPointerOptions = {\n    size: 2,\n    streamline: 0.45,\n    simplify: 0.1,\n    simplifyPhase: 'output',\n    keepHead: false,\n\n    sizeMapping: () => 1,\n  }\n\n  static constants = {\n    cornerDetectionMaxAngle: 75,\n    cornerDetectionVariance: (s: number) => (s > 35 ? 0.5 : 1),\n    maxTailLength: 50,\n  }\n\n  options: LaserPointerOptions\n  constructor(options: Partial<LaserPointerOptions>) {\n    this.options = Object.assign({}, LaserPointer.defaults, options)\n  }\n\n  originalPoints: Point[] = []\n\n  private stablePoints: Point[] = []\n  private tailPoints: Point[] = []\n\n  private isFresh = true\n\n  private get lastPoint(): Point {\n    return this.tailPoints[this.tailPoints.length - 1] ?? this.stablePoints[this.stablePoints.length - 1]\n  }\n\n  addPoint(point: Point) {\n    const lastPoint = this.originalPoints[this.originalPoints.length - 1]\n\n    if (lastPoint && lastPoint[0] === point[0] && lastPoint[1] === point[1]) {\n      return\n    }\n\n    this.originalPoints.push(point)\n\n    if (this.isFresh) {\n      this.isFresh = false\n      this.stablePoints.push(point)\n      return\n    }\n\n    if (this.options.streamline > 0) {\n      point = m.plerp(this.lastPoint, point, 1 - this.options.streamline)\n    }\n\n    this.tailPoints.push(point)\n\n    if (m.runLength(this.tailPoints) > LaserPointer.constants.maxTailLength) {\n      this.stabilizeTail()\n    }\n  }\n\n  close() {\n    this.stabilizeTail()\n  }\n\n  stabilizeTail() {\n    if (this.options.simplify > 0 && this.options.simplifyPhase == 'tail') {\n      throw new Error('Not implemented yet')\n    } else {\n      this.stablePoints.push(...this.tailPoints)\n      this.tailPoints = []\n    }\n  }\n\n  private getSize(\n    sizeOverride: number | undefined,\n    pressure: number,\n    index: number,\n    totalLength: number,\n    runningLength: number\n  ) {\n    return (\n      (sizeOverride ?? this.options.size) *\n      this.options.sizeMapping({\n        pressure: pressure,\n        runningLength: runningLength,\n        currentIndex: index,\n        totalLength: totalLength,\n      })\n    )\n  }\n\n  getStrokeOutline(sizeOverride?: number | undefined): Point[] {\n    if (this.isFresh) {\n      return []\n    }\n\n    let points = [...this.stablePoints, ...this.tailPoints]\n\n    if (this.options.simplify > 0 && this.options.simplifyPhase === 'input') {\n      points = douglasPeucker(points, this.options.simplify)\n    }\n\n    const len = points.length\n\n    if (len === 0) {\n      return []\n    }\n\n    if (len === 1) {\n      const c = points[0]\n\n      const size = this.getSize(sizeOverride, c[2], 0, len, 0)\n\n      if (size < 0.5) {\n        return []\n      }\n\n      const ps: Point[] = []\n\n      for (let theta = 0; theta <= Math.PI * 2; theta += Math.PI / 16) {\n        ps.push(m.add(c, m.smul(m.rot([1, 0, 0] as Point, theta), size)))\n      }\n\n      ps.push(m.add(c, m.smul([1, 0, 0] as Point, this.getSize(sizeOverride, c[2], 0, len, 0))))\n\n      return ps\n    }\n\n    if (len === 2) {\n      const c = points[0]\n      const n = points[1]\n\n      const cSize = this.getSize(sizeOverride, c[2], 0, len, 0)\n      const nSize = this.getSize(sizeOverride, n[2], 0, len, 0)\n\n      if (cSize < 0.5 || nSize < 0.5) {\n        return []\n      }\n\n      const ps: Point[] = []\n\n      const pAngle = m.angle(c, [c[0], c[1] - 100, c[2]] as Point, n)\n\n      for (let theta = pAngle; theta <= Math.PI + pAngle; theta += Math.PI / 16) {\n        ps.push(m.add(c, m.smul(m.rot([1, 0, 0] as Point, theta), cSize)))\n      }\n\n      for (let theta = Math.PI + pAngle; theta <= Math.PI * 2 + pAngle; theta += Math.PI / 16) {\n        ps.push(m.add(n, m.smul(m.rot([1, 0, 0] as Point, theta), nSize)))\n      }\n\n      ps.push(ps[0])\n\n      return ps\n    }\n\n    const forwardPoints: Point[] = []\n    const backwardPoints: Point[] = []\n\n    let speed = 0\n    let prevSpeed = 0\n\n    let visibleStartIndex = 0\n    let runningLength = 0\n\n    for (let i = 1; i < len - 1; i++) {\n      const p = points[i - 1],\n        c = points[i],\n        n = points[i + 1]\n\n      let pressure = c[2]\n\n      const d = m.dist(p, c)\n      runningLength += d\n      speed = prevSpeed + (d - prevSpeed) * 0.2\n\n      const cSize = this.getSize(sizeOverride, pressure, i, len, runningLength)\n\n      if (cSize === 0) {\n        visibleStartIndex = i + 1\n        continue\n      }\n\n      const dirPC = m.norm(m.sub(p, c))\n      const dirNC = m.norm(m.sub(n, c))\n      const p1dirPC = m.rot(dirPC, Math.PI / 2)\n      const p2dirPC = m.rot(dirPC, -Math.PI / 2)\n      const p1dirNC = m.rot(dirNC, Math.PI / 2)\n      const p2dirNC = m.rot(dirNC, -Math.PI / 2)\n\n      const p1PC = m.add(c, m.smul(p1dirPC, cSize))\n      const p2PC = m.add(c, m.smul(p2dirPC, cSize))\n      const p1NC = m.add(c, m.smul(p1dirNC, cSize))\n      const p2NC = m.add(c, m.smul(p2dirNC, cSize))\n\n      const ftdir = m.add(p1dirPC, p2dirNC)\n      const btdir = m.add(p2dirPC, p1dirNC)\n\n      const paPC = m.add(c, m.smul(m.mag(ftdir) === 0 ? dirPC : m.norm(ftdir), cSize))\n      const paNC = m.add(c, m.smul(m.mag(btdir) === 0 ? dirNC : m.norm(btdir), cSize))\n\n      const cAngle = m.normAngle(m.angle(c, p, n))\n      const D_ANGLE =\n        (LaserPointer.constants.cornerDetectionMaxAngle / 180) *\n        Math.PI *\n        LaserPointer.constants.cornerDetectionVariance(speed)\n\n      if (Math.abs(cAngle) < D_ANGLE) {\n        const tAngle = Math.abs(m.normAngle(Math.PI - cAngle)) // turn angle\n\n        if (tAngle === 0) {\n          continue\n        }\n\n        if (cAngle < 0) {\n          backwardPoints.push(p2PC, paNC)\n\n          for (let theta = 0; theta <= tAngle; theta += tAngle / 4) {\n            forwardPoints.push(m.add(c, m.rot(m.smul(p1dirPC, cSize), theta)))\n          }\n\n          for (let theta = tAngle; theta >= 0; theta -= tAngle / 4) {\n            backwardPoints.push(m.add(c, m.rot(m.smul(p1dirPC, cSize), theta)))\n          }\n\n          backwardPoints.push(paNC, p1NC)\n        } else {\n          forwardPoints.push(p1PC, paPC)\n\n          for (let theta = 0; theta <= tAngle; theta += tAngle / 4) {\n            backwardPoints.push(m.add(c, m.rot(m.smul(p1dirPC, -cSize), -theta)))\n          }\n\n          for (let theta = tAngle; theta >= 0; theta -= tAngle / 4) {\n            forwardPoints.push(m.add(c, m.rot(m.smul(p1dirPC, -cSize), -theta)))\n          }\n          forwardPoints.push(paPC, p2NC)\n        }\n      } else {\n        forwardPoints.push(paPC)\n        backwardPoints.push(paNC)\n      }\n\n      prevSpeed = speed\n    }\n\n    if (visibleStartIndex >= len - 2) {\n      if (this.options.keepHead) {\n        const c = points[len - 1]\n\n        const ps: Point[] = []\n\n        for (let theta = 0; theta <= Math.PI * 2; theta += Math.PI / 16) {\n          ps.push(m.add(c, m.smul(m.rot([1, 0, 0] as Point, theta), this.options.size)))\n        }\n\n        ps.push(m.add(c, m.smul([1, 0, 0] as Point, this.options.size)))\n\n        return ps\n      } else {\n        return []\n      }\n    }\n\n    const first = points[visibleStartIndex]\n    const second = points[visibleStartIndex + 1]\n    const penultimate = points[len - 2]\n    const ultimate = points[len - 1]\n\n    const dirFS = m.norm(m.sub(second, first))\n    const dirPU = m.norm(m.sub(penultimate, ultimate))\n\n    const ppdirFS = m.rot(dirFS, -Math.PI / 2)\n    const ppdirPU = m.rot(dirPU, Math.PI / 2)\n\n    const startCapSize = this.getSize(sizeOverride, first[2], 0, len, 0)\n    const startCap: Point[] = []\n\n    const endCapSize = this.options.keepHead\n      ? this.options.size\n      : this.getSize(sizeOverride, penultimate[2], len - 2, len, runningLength)\n\n    const endCap: Point[] = []\n\n    if (startCapSize > 1) {\n      for (let theta = 0; theta <= Math.PI; theta += Math.PI / 16) {\n        startCap.unshift(m.add(first, m.rot(m.smul(ppdirFS, startCapSize), -theta)))\n      }\n\n      startCap.unshift(m.add(first, m.smul(ppdirFS, -startCapSize)))\n    } else {\n      startCap.push(first)\n    }\n\n    for (let theta = 0; theta <= Math.PI * 3; theta += Math.PI / 16) {\n      endCap.push(m.add(ultimate, m.rot(m.smul(ppdirPU, -endCapSize), -theta)))\n    }\n\n    const strokeOutline = [...startCap, ...forwardPoints, ...endCap.reverse(), ...backwardPoints.reverse()]\n\n    if (startCap.length > 0) {\n      strokeOutline.push(startCap[0])\n    }\n\n    if (this.options.simplify > 0 && this.options.simplifyPhase === 'output') {\n      return douglasPeucker(strokeOutline, this.options.simplify)\n    }\n\n    return strokeOutline\n  }\n}\n", "export type Point = [x: number, y: number, r: number]\n\nexport function add([ax, ay, ar]: Point, [bx, by, br]: Point): Point {\n  return [ax + bx, ay + by, ar + br]\n}\n\nexport function sub([ax, ay, ar]: Point, [bx, by, br]: Point): Point {\n  return [ax - bx, ay - by, ar - br]\n}\n\nexport function smul([x, y, r]: Point, s: number): Point {\n  return [x * s, y * s, r * s]\n}\n\nexport function norm([x, y, r]: Point): Point {\n  return [x / Math.sqrt(x ** 2 + y ** 2), y / Math.sqrt(x ** 2 + y ** 2), r]\n}\n\nexport function rot([x, y, r]: Point, rad: number): Point {\n  return [Math.cos(rad) * x - Math.sin(rad) * y, Math.sin(rad) * x + Math.cos(rad) * y, r]\n}\n\nexport function plerp(a: Point, b: Point, t: number): Point {\n  return add(a, smul(sub(b, a), t))\n}\n\nexport function lerp(a: number, b: number, t: number): number {\n  return a + (b - a) * t\n}\n\nexport function angle(p: Point, p1: Point, p2: Point) {\n  return Math.atan2(p2[1] - p[1], p2[0] - p[0]) - Math.atan2(p1[1] - p[1], p1[0] - p[0])\n}\n\nexport function normAngle(a: number) {\n  return Math.atan2(Math.sin(a), Math.cos(a))\n}\n\nexport function mag([x, y]: Point) {\n  return Math.sqrt(x ** 2 + y ** 2)\n}\n\nexport function dist([ax, ay]: Point, [bx, by]: Point): number {\n  return Math.sqrt((bx - ax) ** 2 + (by - ay) ** 2)\n}\n\nexport function getCircleAndPerpendicularLineIntersectionsAtPoint(\n  point: Point,\n  direction: Point,\n  radius: number\n): [Point, Point] {\n  return [\n    add(point, smul(norm(rot(direction, Math.PI / 2)), radius)),\n    add(point, smul(norm(rot(direction, -Math.PI / 2)), radius)),\n  ]\n}\n\nexport function runLength(ps: Point[]): number {\n  if (ps.length < 2) return 0\n\n  let len = 0\n\n  for (let i = 1; i <= ps.length - 1; i++) {\n    len += dist(ps[i - 1], ps[i])\n  }\n\n  len += dist(ps[ps.length -2], ps[ps.length - 1])\n\n  return len\n}\n\nexport const clamp = (v: number, min: number, max: number) => Math.max(min, Math.min(max, v))\n\nexport function distancePointToSegment(p3: Point, p1: Point, p2: Point) {\n  const sMag = dist(p1, p2)\n\n  if (sMag === 0) return dist(p3, p1)\n\n  const u = clamp(((p3[0] - p1[0]) * (p2[0] - p1[0]) + (p3[1] - p1[1]) * (p2[1] - p1[1])) / sMag ** 2, 0, 1)\n\n  const pi: Point = [p1[0] + u * (p2[0] - p1[0]), p1[1] + u * (p2[1] - p1[1]), p3[2]]\n\n  return dist(pi, p3)\n}\n", "import { Point, distancePointToSegment } from './math'\n\nexport function do<PERSON><PERSON><PERSON><PERSON><PERSON>(points: Point[], epsilon: number): Point[] {\n  if (epsilon === 0) {\n    return points\n  }\n\n  if (points.length <= 2) {\n    return points\n  }\n\n  const first = points[0]\n  const last = points[points.length - 1]\n\n  const [maxDistance, maxIndex] = points.reduce(\n    ([maxDistance, maxIndex], point, index) => {\n      const distance = distancePointToSegment(point, first, last)\n\n      return distance > maxDistance ? [distance, index] : [maxDistance, maxIndex]\n    },\n    [0, -1]\n  )\n\n  if (maxDistance >= epsilon) {\n    const maxIndexPoint = points[maxIndex]\n\n    return [\n      ...doug<PERSON><PERSON><PERSON><PERSON>([first, ...points.slice(1, maxIndex), maxIndexPoint], epsilon).slice(0, -1),\n      maxIndexPoint,\n      ...douglasPeucker([maxIndexPoint, ...points.slice(maxIndex, -1), last], epsilon).slice(1),\n    ]\n  } else {\n    return [first, last]\n  }\n}\n"], "names": [], "version": 3, "file": "esm.js.map"}