{"name": "@excalidraw/markdown-to-text", "version": "0.1.2", "description": "Parse the markdown and returns a string", "main": "dist/index.js", "types": "dist/index.d.ts", "author": {"email": "<EMAIL>", "name": "<PERSON>", "url": "https://danestves.com"}, "keywords": ["markdown", "text", "ts"], "repository": {"type": "git", "url": "https://github.com/danestves/markdown-to-text.git"}, "homepage": "https://github.com/danestves/markdown-to-text.git", "scripts": {"build": "npm run test && tsc", "prepublish": "npm run test && tsc", "test": "env TS_NODE_COMPILER_OPTIONS='{\"module\": \"commonjs\" }' mocha -r ts-node/register 'test/**/*.ts'"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.2.1", "should": "^13.2.3", "ts-node": "^9.1.1", "typescript": "^4.1.3", "@types/chai": "^4.2.14", "@types/mocha": "^8.2.0"}, "license": "MIT"}