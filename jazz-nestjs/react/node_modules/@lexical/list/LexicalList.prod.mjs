/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{$getNearestNodeOfType as t,removeClassNamesFromElement as e,addClassNamesToElement as r,isHTMLElement as n,mergeRegister as s}from"@lexical/utils";import{$getSelection as i,$isRangeSelection as o,$isRootOrShadowRoot as l,$createParagraphNode as c,$isElementNode as a,$isLeafNode as h,$setPointFromCaret as g,$normalizeCaret as u,$getChildCaret as d,ElementNode as f,$isParagraphNode as p,$applyNodeReplacement as _,$createTextNode as m,createCommand as y,COMMAND_PRIORITY_LOW as T,INSERT_PARAGRAPH_COMMAND as S,$isTextNode as C,TextNode as x}from"lexical";function v(t,...e){const r=new URL("https://lexical.dev/docs/error"),n=new URLSearchParams;n.append("code",t);for(const t of e)n.append("v",t);throw r.search=n.toString(),Error(`Minified Lexical error #${t}; visit ${r.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}function k(t){let e=1,r=t.getParent();for(;null!=r;){if($(r)){const t=r.getParent();if(Y(t)){e++,r=t.getParent();continue}v(40)}return e}return e}function b(t){let e=t.getParent();Y(e)||v(40);let r=e;for(;null!==r;)r=r.getParent(),Y(r)&&(e=r);return e}function N(t){let e=[];const r=t.getChildren().filter($);for(let t=0;t<r.length;t++){const n=r[t],s=n.getFirstChild();Y(s)?e=e.concat(N(s)):e.push(n)}return e}function F(t){return $(t)&&Y(t.getFirstChild())}function O(t){return z().append(t)}function P(t,e){return $(t)&&(0===e.length||1===e.length&&t.is(e[0])&&0===t.getChildrenSize())}function A(t){const e=i();if(null!==e){let r=e.getNodes();if(o(e)){const n=e.getStartEndPoints();null===n&&v(143);const[s]=n,i=s.getNode(),o=i.getParent();if(l(i)){const t=i.getFirstChild();if(t)r=t.selectStart().getNodes();else{const t=c();i.append(t),r=t.select().getNodes()}}else if(P(i,r)){const e=X(t);if(l(o)){i.replace(e);const t=z();a(i)&&(t.setFormat(i.getFormatType()),t.setIndent(i.getIndent())),e.append(t)}else if($(i)){const t=i.getParentOrThrow();L(e,t.getChildren()),t.replace(e)}return}}const n=new Set;for(let e=0;e<r.length;e++){const s=r[e];if(!a(s)||!s.isEmpty()||$(s)||n.has(s.getKey())){if(h(s)){let e=s.getParent();for(;null!=e;){const r=e.getKey();if(Y(e)){if(!n.has(r)){const s=X(t);L(s,e.getChildren()),e.replace(s),n.add(r)}break}{const s=e.getParent();if(l(s)&&!n.has(r)){n.add(r),E(e,t);break}e=s}}}}else E(s,t)}}}function L(t,e){t.splice(t.getChildrenSize(),0,e)}function E(t,e){if(Y(t))return t;const r=t.getPreviousSibling(),n=t.getNextSibling(),s=z();let i;if(L(s,t.getChildren()),Y(r)&&e===r.getListType())r.append(s),Y(n)&&e===n.getListType()&&(L(r,n.getChildren()),n.remove()),i=r;else if(Y(n)&&e===n.getListType())n.getFirstChildOrThrow().insertBefore(s),i=n;else{const r=X(e);r.append(s),t.replace(r),i=r}return s.setFormat(t.getFormatType()),s.setIndent(t.getIndent()),t.remove(),i}function M(t,e){const r=t.getLastChild(),n=e.getFirstChild();r&&n&&F(r)&&F(n)&&(M(r.getFirstChild(),n.getFirstChild()),n.remove());const s=e.getChildren();s.length>0&&t.append(...s),e.remove()}function w(){const e=i();if(o(e)){const r=new Set,n=e.getNodes(),s=e.anchor.getNode();if(P(s,n))r.add(b(s));else for(let e=0;e<n.length;e++){const s=n[e];if(h(s)){const e=t(s,W);null!=e&&r.add(b(e))}}for(const t of r){let r=t;const n=N(t);for(const t of n){const n=c().setTextStyle(e.style).setTextFormat(e.format);L(n,t.getChildren()),r.insertAfter(n),r=n,t.__key===e.anchor.key&&g(e.anchor,u(d(n,"next"))),t.__key===e.focus.key&&g(e.focus,u(d(n,"next"))),t.remove()}t.remove()}}}function I(t){const e=new Set;if(F(t)||e.has(t.getKey()))return;const r=t.getParent(),n=t.getNextSibling(),s=t.getPreviousSibling();if(F(n)&&F(s)){const r=s.getFirstChild();if(Y(r)){r.append(t);const s=n.getFirstChild();if(Y(s)){L(r,s.getChildren()),n.remove(),e.add(n.getKey())}}}else if(F(n)){const e=n.getFirstChild();if(Y(e)){const r=e.getFirstChild();null!==r&&r.insertBefore(t)}}else if(F(s)){const e=s.getFirstChild();Y(e)&&e.append(t)}else if(Y(r)){const e=z().setTextFormat(r.getTextFormat()).setTextStyle(r.getTextStyle()),i=X(r.getListType()).setTextFormat(r.getTextFormat()).setTextStyle(r.getTextStyle());e.append(i),i.append(t),s?s.insertAfter(e):n?n.insertBefore(e):r.append(e)}}function D(t){if(F(t))return;const e=t.getParent(),r=e?e.getParent():void 0;if(Y(r?r.getParent():void 0)&&$(r)&&Y(e)){const n=e?e.getFirstChild():void 0,s=e?e.getLastChild():void 0;if(t.is(n))r.insertBefore(t),e.isEmpty()&&r.remove();else if(t.is(s))r.insertAfter(t),e.isEmpty()&&r.remove();else{const n=e.getListType(),s=z(),i=X(n);s.append(i),t.getPreviousSiblings().forEach((t=>i.append(t)));const o=z(),l=X(n);o.append(l),L(l,t.getNextSiblings()),r.insertBefore(s),r.insertAfter(o),r.replace(t)}}}function J(){const t=i();if(!o(t)||!t.isCollapsed())return!1;const e=t.anchor.getNode();if(!$(e)||0!==e.getChildrenSize())return!1;const r=b(e),n=e.getParent();Y(n)||v(40);const s=n.getParent();let a;if(l(s))a=c(),r.insertAfter(a);else{if(!$(s))return!1;a=z(),s.insertAfter(a)}a.setTextStyle(t.style).setTextFormat(t.format).select();const h=e.getNextSiblings();if(h.length>0){const t=X(n.getListType());if($(a)){const e=z();e.append(t),a.insertAfter(e)}else a.insertAfter(t);t.append(...h)}return function(t){let e=t;for(;null==e.getNextSibling()&&null==e.getPreviousSibling();){const t=e.getParent();if(null==t||!$(t)&&!Y(t))break;e=t}e.remove()}(e),!0}function R(...t){const e=[];for(const r of t)if(r&&"string"==typeof r)for(const[t]of r.matchAll(/\S+/g))e.push(t);return e}class W extends f{static getType(){return"listitem"}static clone(t){return new W(t.__value,t.__checked,t.__key)}constructor(t,e,r){super(r),this.__value=void 0===t?1:t,this.__checked=e}createDOM(t){const e=document.createElement("li"),r=this.getParent();Y(r)&&"check"===r.getListType()&&K(e,this,null),e.value=this.__value,B(e,t.theme,this);const n=this.__style||this.__textStyle;return n&&(e.style.cssText=n),e}updateDOM(t,e,r){const n=this.getParent();Y(n)&&"check"===n.getListType()&&K(e,this,t),e.value=this.__value,B(e,r.theme,this);const s=t.__style||t.__textStyle,i=this.__style||this.__textStyle;return s!==i&&(e.style.cssText=i,""===i&&e.removeAttribute("style")),!1}static transform(){return t=>{if($(t)||v(144),null==t.__checked)return;const e=t.getParent();Y(e)&&"check"!==e.getListType()&&null!=t.getChecked()&&t.setChecked(void 0)}}static importDOM(){return{li:()=>({conversion:V,priority:0})}}static importJSON(t){return z().updateFromJSON(t)}updateFromJSON(t){return super.updateFromJSON(t).setValue(t.value).setChecked(t.checked)}exportDOM(t){const e=this.createDOM(t._config);e.style.textAlign=this.getFormatType();const r=this.getDirection();return r&&(e.dir=r),{element:e}}exportJSON(){return{...super.exportJSON(),checked:this.getChecked(),value:this.getValue()}}append(...t){for(let e=0;e<t.length;e++){const r=t[e];if(a(r)&&this.canMergeWith(r)){const t=r.getChildren();this.append(...t),r.remove()}else super.append(r)}return this}replace(t,e){if($(t))return super.replace(t);this.setIndent(0);const r=this.getParentOrThrow();if(!Y(r))return t;if(r.__first===this.getKey())r.insertBefore(t);else if(r.__last===this.getKey())r.insertAfter(t);else{const e=X(r.getListType());let n=this.getNextSibling();for(;n;){const t=n;n=n.getNextSibling(),e.append(t)}r.insertAfter(t),t.insertAfter(e)}return e&&(a(t)||v(139),this.getChildren().forEach((e=>{t.append(e)}))),this.remove(),0===r.getChildrenSize()&&r.remove(),t}insertAfter(t,e=!0){const r=this.getParentOrThrow();if(Y(r)||v(39),$(t))return super.insertAfter(t,e);const n=this.getNextSiblings();if(r.insertAfter(t,e),0!==n.length){const s=X(r.getListType());n.forEach((t=>s.append(t))),t.insertAfter(s,e)}return t}remove(t){const e=this.getPreviousSibling(),r=this.getNextSibling();super.remove(t),e&&r&&F(e)&&F(r)&&(M(e.getFirstChild(),r.getFirstChild()),r.remove())}insertNewAfter(t,e=!0){const r=z().updateFromJSON(this.exportJSON()).setChecked(!this.getChecked()&&void 0);return this.insertAfter(r,e),r}collapseAtStart(t){const e=c();this.getChildren().forEach((t=>e.append(t)));const r=this.getParentOrThrow(),n=r.getParentOrThrow(),s=$(n);if(1===r.getChildrenSize())if(s)r.remove(),n.select();else{r.insertBefore(e),r.remove();const n=t.anchor,s=t.focus,i=e.getKey();"element"===n.type&&n.getNode().is(this)&&n.set(i,n.offset,"element"),"element"===s.type&&s.getNode().is(this)&&s.set(i,s.offset,"element")}else r.insertBefore(e),this.remove();return!0}getValue(){return this.getLatest().__value}setValue(t){const e=this.getWritable();return e.__value=t,e}getChecked(){const t=this.getLatest();let e;const r=this.getParent();return Y(r)&&(e=r.getListType()),"check"===e?Boolean(t.__checked):void 0}setChecked(t){const e=this.getWritable();return e.__checked=t,e}toggleChecked(){const t=this.getWritable();return t.setChecked(!t.__checked)}getIndent(){const t=this.getParent();if(null===t||!this.isAttached())return this.getLatest().__indent;let e=t.getParentOrThrow(),r=0;for(;$(e);)e=e.getParentOrThrow().getParentOrThrow(),r++;return r}setIndent(t){"number"!=typeof t&&v(117),(t=Math.floor(t))>=0||v(199);let e=this.getIndent();for(;e!==t;)e<t?(I(this),e++):(D(this),e--);return this}canInsertAfter(t){return $(t)}canReplaceWith(t){return $(t)}canMergeWith(t){return $(t)||p(t)}extractWithChild(t,e){if(!o(e))return!1;const r=e.anchor.getNode(),n=e.focus.getNode();return this.isParentOf(r)&&this.isParentOf(n)&&this.getTextContent().length===e.getTextContent().length}isParentRequired(){return!0}createParentElementNode(){return X("bullet")}canMergeWhenEmpty(){return!0}}function B(t,n,s){const i=[],o=[],l=n.list,c=l?l.listitem:void 0;let a;if(l&&l.nested&&(a=l.nested.listitem),void 0!==c&&i.push(...R(c)),l){const t=s.getParent(),e=Y(t)&&"check"===t.getListType(),r=s.getChecked();e&&!r||o.push(l.listitemUnchecked),e&&r||o.push(l.listitemChecked),e&&i.push(r?l.listitemChecked:l.listitemUnchecked)}if(void 0!==a){const t=R(a);s.getChildren().some((t=>Y(t)))?i.push(...t):o.push(...t)}o.length>0&&e(t,...o),i.length>0&&r(t,...i)}function K(t,e,r,n){Y(e.getFirstChild())?(t.removeAttribute("role"),t.removeAttribute("tabIndex"),t.removeAttribute("aria-checked")):(t.setAttribute("role","checkbox"),t.setAttribute("tabIndex","-1"),r&&e.__checked===r.__checked||t.setAttribute("aria-checked",e.getChecked()?"true":"false"))}function V(t){if(t.classList.contains("task-list-item"))for(const e of t.children)if("INPUT"===e.tagName)return U(e);const e=t.getAttribute("aria-checked");return{node:z("true"===e||"false"!==e&&void 0)}}function U(t){if(!("checkbox"===t.getAttribute("type")))return{node:null};return{node:z(t.hasAttribute("checked"))}}function z(t){return _(new W(void 0,t))}function $(t){return t instanceof W}class q extends f{static getType(){return"list"}static clone(t){const e=t.__listType||Q[t.__tag];return new q(e,t.__start,t.__key)}constructor(t="number",e=1,r){super(r);const n=Q[t]||t;this.__listType=n,this.__tag="number"===n?"ol":"ul",this.__start=e}getTag(){return this.__tag}setListType(t){const e=this.getWritable();return e.__listType=t,e.__tag="number"===t?"ol":"ul",e}getListType(){return this.__listType}getStart(){return this.__start}setStart(t){const e=this.getWritable();return e.__start=t,e}createDOM(t,e){const r=this.__tag,n=document.createElement(r);return 1!==this.__start&&n.setAttribute("start",String(this.__start)),n.__lexicalListType=this.__listType,H(n,t.theme,this),n}updateDOM(t,e,r){return t.__tag!==this.__tag||(H(e,r.theme,this),!1)}static transform(){return t=>{Y(t)||v(163),function(t){const e=t.getNextSibling();Y(e)&&t.getListType()===e.getListType()&&M(t,e)}(t),function(t){const e="check"!==t.getListType();let r=t.getStart();for(const n of t.getChildren())$(n)&&(n.getValue()!==r&&n.setValue(r),e&&null!=n.getLatest().__checked&&n.setChecked(void 0),Y(n.getFirstChild())||r++)}(t)}}static importDOM(){return{ol:()=>({conversion:G,priority:0}),ul:()=>({conversion:G,priority:0})}}static importJSON(t){return X().updateFromJSON(t)}updateFromJSON(t){return super.updateFromJSON(t).setListType(t.listType).setStart(t.start)}exportDOM(t){const e=this.createDOM(t._config,t);return n(e)&&(1!==this.__start&&e.setAttribute("start",String(this.__start)),"check"===this.__listType&&e.setAttribute("__lexicalListType","check")),{element:e}}exportJSON(){return{...super.exportJSON(),listType:this.getListType(),start:this.getStart(),tag:this.getTag()}}canBeEmpty(){return!1}canIndent(){return!1}append(...t){for(let e=0;e<t.length;e++){const r=t[e];if($(r))super.append(r);else{const t=z();if(Y(r))t.append(r);else if(a(r))if(r.isInline())t.append(r);else{const e=m(r.getTextContent());t.append(e)}else t.append(r);super.append(t)}}return this}extractWithChild(t){return $(t)}}function H(t,n,s){const i=[],o=[],l=n.list;if(void 0!==l){const t=l[`${s.__tag}Depth`]||[],e=k(s)-1,r=e%t.length,n=t[r],c=l[s.__tag];let a;const h=l.nested,g=l.checklist;if(void 0!==h&&h.list&&(a=h.list),void 0!==c&&i.push(c),void 0!==g&&"check"===s.__listType&&i.push(g),void 0!==n){i.push(...R(n));for(let e=0;e<t.length;e++)e!==r&&o.push(s.__tag+e)}if(void 0!==a){const t=R(a);e>1?i.push(...t):o.push(...t)}}o.length>0&&e(t,...o),i.length>0&&r(t,...i)}function j(t){const e=[];for(let r=0;r<t.length;r++){const n=t[r];if($(n)){e.push(n);const t=n.getChildren();t.length>1&&t.forEach((t=>{Y(t)&&e.push(O(t))}))}else e.push(O(n))}return e}function G(t){const e=t.nodeName.toLowerCase();let r=null;if("ol"===e){r=X("number",t.start)}else"ul"===e&&(r=function(t){if("check"===t.getAttribute("__lexicallisttype")||t.classList.contains("contains-task-list"))return!0;for(const e of t.childNodes)if(n(e)&&e.hasAttribute("aria-checked"))return!0;return!1}(t)?X("check"):X("bullet"));return{after:j,node:r}}const Q={ol:"number",ul:"bullet"};function X(t="number",e=1){return _(new q(t,e))}function Y(t){return t instanceof q}const Z=y("INSERT_UNORDERED_LIST_COMMAND"),tt=y("INSERT_ORDERED_LIST_COMMAND"),et=y("INSERT_CHECK_LIST_COMMAND"),rt=y("REMOVE_LIST_COMMAND");function nt(t){return s(t.registerCommand(tt,(()=>(A("number"),!0)),T),t.registerCommand(Z,(()=>(A("bullet"),!0)),T),t.registerCommand(rt,(()=>(w(),!0)),T),t.registerCommand(S,(()=>J()),T),t.registerNodeTransform(W,(t=>{const e=t.getFirstChild();if(e){if(C(e)){const r=e.getStyle(),n=e.getFormat();t.getTextStyle()!==r&&t.setTextStyle(r),t.getTextFormat()!==n&&t.setTextFormat(n)}}else{const e=i();o(e)&&(e.style!==t.getTextStyle()||e.format!==t.getTextFormat())&&e.isCollapsed()&&t.is(e.anchor.getNode())&&t.setTextStyle(e.style).setTextFormat(e.format)}})),t.registerNodeTransform(x,(t=>{const e=t.getParent();if($(e)&&t.is(e.getFirstChild())){const r=t.getStyle(),n=t.getFormat();r===e.getTextStyle()&&n===e.getTextFormat()||e.setTextStyle(r).setTextFormat(n)}})))}function st(t,e){t.update((()=>A(e)))}function it(t){t.update((()=>w()))}export{z as $createListItemNode,X as $createListNode,k as $getListDepth,J as $handleListInsertParagraph,A as $insertList,$ as $isListItemNode,Y as $isListNode,w as $removeList,et as INSERT_CHECK_LIST_COMMAND,tt as INSERT_ORDERED_LIST_COMMAND,Z as INSERT_UNORDERED_LIST_COMMAND,W as ListItemNode,q as ListNode,rt as REMOVE_LIST_COMMAND,st as insertList,nt as registerList,it as removeList};
