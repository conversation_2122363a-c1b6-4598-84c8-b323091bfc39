/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("@lexical/utils"),t=require("lexical");function r(e,...t){const r=new URL("https://lexical.dev/docs/error"),n=new URLSearchParams;n.append("code",e);for(const e of t)n.append("v",e);throw r.search=n.toString(),Error(`Minified Lexical error #${e}; visit ${r.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}function n(e){let t=1,n=e.getParent();for(;null!=n;){if(v(n)){const e=n.getParent();if(E(e)){t++,n=e.getParent();continue}r(40)}return t}return t}function s(e){let t=e.getParent();E(t)||r(40);let n=t;for(;null!==n;)n=n.getParent(),E(n)&&(t=n);return t}function i(e){let t=[];const r=e.getChildren().filter(v);for(let e=0;e<r.length;e++){const n=r[e],s=n.getFirstChild();E(s)?t=t.concat(i(s)):t.push(n)}return t}function o(e){return v(e)&&E(e.getFirstChild())}function l(e){return x().append(e)}function a(e,t){return v(e)&&(0===t.length||1===t.length&&e.is(t[0])&&0===e.getChildrenSize())}function c(e){const n=t.$getSelection();if(null!==n){let s=n.getNodes();if(t.$isRangeSelection(n)){const i=n.getStartEndPoints();null===i&&r(143);const[o]=i,l=o.getNode(),c=l.getParent();if(t.$isRootOrShadowRoot(l)){const e=l.getFirstChild();if(e)s=e.selectStart().getNodes();else{const e=t.$createParagraphNode();l.append(e),s=e.select().getNodes()}}else if(a(l,s)){const r=A(e);if(t.$isRootOrShadowRoot(c)){l.replace(r);const e=x();t.$isElementNode(l)&&(e.setFormat(l.getFormatType()),e.setIndent(l.getIndent())),r.append(e)}else if(v(l)){const e=l.getParentOrThrow();h(r,e.getChildren()),e.replace(r)}return}}const i=new Set;for(let r=0;r<s.length;r++){const n=s[r];if(!t.$isElementNode(n)||!n.isEmpty()||v(n)||i.has(n.getKey())){if(t.$isLeafNode(n)){let r=n.getParent();for(;null!=r;){const n=r.getKey();if(E(r)){if(!i.has(n)){const t=A(e);h(t,r.getChildren()),r.replace(t),i.add(n)}break}{const s=r.getParent();if(t.$isRootOrShadowRoot(s)&&!i.has(n)){i.add(n),g(r,e);break}r=s}}}}else g(n,e)}}}function h(e,t){e.splice(e.getChildrenSize(),0,t)}function g(e,t){if(E(e))return e;const r=e.getPreviousSibling(),n=e.getNextSibling(),s=x();let i;if(h(s,e.getChildren()),E(r)&&t===r.getListType())r.append(s),E(n)&&t===n.getListType()&&(h(r,n.getChildren()),n.remove()),i=r;else if(E(n)&&t===n.getListType())n.getFirstChildOrThrow().insertBefore(s),i=n;else{const r=A(t);r.append(s),e.replace(r),i=r}return s.setFormat(e.getFormatType()),s.setIndent(e.getIndent()),e.remove(),i}function d(e,t){const r=e.getLastChild(),n=t.getFirstChild();r&&n&&o(r)&&o(n)&&(d(r.getFirstChild(),n.getFirstChild()),n.remove());const s=t.getChildren();s.length>0&&e.append(...s),t.remove()}function u(){const r=t.$getSelection();if(t.$isRangeSelection(r)){const n=new Set,o=r.getNodes(),l=r.anchor.getNode();if(a(l,o))n.add(s(l));else for(let r=0;r<o.length;r++){const i=o[r];if(t.$isLeafNode(i)){const t=e.$getNearestNodeOfType(i,T);null!=t&&n.add(s(t))}}for(const e of n){let n=e;const s=i(e);for(const e of s){const s=t.$createParagraphNode().setTextStyle(r.style).setTextFormat(r.format);h(s,e.getChildren()),n.insertAfter(s),n=s,e.__key===r.anchor.key&&t.$setPointFromCaret(r.anchor,t.$normalizeCaret(t.$getChildCaret(s,"next"))),e.__key===r.focus.key&&t.$setPointFromCaret(r.focus,t.$normalizeCaret(t.$getChildCaret(s,"next"))),e.remove()}e.remove()}}}function p(e){const t=new Set;if(o(e)||t.has(e.getKey()))return;const r=e.getParent(),n=e.getNextSibling(),s=e.getPreviousSibling();if(o(n)&&o(s)){const r=s.getFirstChild();if(E(r)){r.append(e);const s=n.getFirstChild();if(E(s)){h(r,s.getChildren()),n.remove(),t.add(n.getKey())}}}else if(o(n)){const t=n.getFirstChild();if(E(t)){const r=t.getFirstChild();null!==r&&r.insertBefore(e)}}else if(o(s)){const t=s.getFirstChild();E(t)&&t.append(e)}else if(E(r)){const t=x().setTextFormat(r.getTextFormat()).setTextStyle(r.getTextStyle()),i=A(r.getListType()).setTextFormat(r.getTextFormat()).setTextStyle(r.getTextStyle());t.append(i),i.append(e),s?s.insertAfter(t):n?n.insertBefore(t):r.append(t)}}function f(e){if(o(e))return;const t=e.getParent(),r=t?t.getParent():void 0;if(E(r?r.getParent():void 0)&&v(r)&&E(t)){const n=t?t.getFirstChild():void 0,s=t?t.getLastChild():void 0;if(e.is(n))r.insertBefore(e),t.isEmpty()&&r.remove();else if(e.is(s))r.insertAfter(e),t.isEmpty()&&r.remove();else{const n=t.getListType(),s=x(),i=A(n);s.append(i),e.getPreviousSiblings().forEach((e=>i.append(e)));const o=x(),l=A(n);o.append(l),h(l,e.getNextSiblings()),r.insertBefore(s),r.insertAfter(o),r.replace(e)}}}function m(){const e=t.$getSelection();if(!t.$isRangeSelection(e)||!e.isCollapsed())return!1;const n=e.anchor.getNode();if(!v(n)||0!==n.getChildrenSize())return!1;const i=s(n),o=n.getParent();E(o)||r(40);const l=o.getParent();let a;if(t.$isRootOrShadowRoot(l))a=t.$createParagraphNode(),i.insertAfter(a);else{if(!v(l))return!1;a=x(),l.insertAfter(a)}a.setTextStyle(e.style).setTextFormat(e.format).select();const c=n.getNextSiblings();if(c.length>0){const e=A(o.getListType());if(v(a)){const t=x();t.append(e),a.insertAfter(t)}else a.insertAfter(e);e.append(...c)}return function(e){let t=e;for(;null==t.getNextSibling()&&null==t.getPreviousSibling();){const e=t.getParent();if(null==e||!v(e)&&!E(e))break;t=e}t.remove()}(n),!0}function _(...e){const t=[];for(const r of e)if(r&&"string"==typeof r)for(const[e]of r.matchAll(/\S+/g))t.push(e);return t}class T extends t.ElementNode{static getType(){return"listitem"}static clone(e){return new T(e.__value,e.__checked,e.__key)}constructor(e,t,r){super(r),this.__value=void 0===e?1:e,this.__checked=t}createDOM(e){const t=document.createElement("li"),r=this.getParent();E(r)&&"check"===r.getListType()&&N(t,this,null),t.value=this.__value,C(t,e.theme,this);const n=this.__style||this.__textStyle;return n&&(t.style.cssText=n),t}updateDOM(e,t,r){const n=this.getParent();E(n)&&"check"===n.getListType()&&N(t,this,e),t.value=this.__value,C(t,r.theme,this);const s=e.__style||e.__textStyle,i=this.__style||this.__textStyle;return s!==i&&(t.style.cssText=i,""===i&&t.removeAttribute("style")),!1}static transform(){return e=>{if(v(e)||r(144),null==e.__checked)return;const t=e.getParent();E(t)&&"check"!==t.getListType()&&null!=e.getChecked()&&e.setChecked(void 0)}}static importDOM(){return{li:()=>({conversion:S,priority:0})}}static importJSON(e){return x().updateFromJSON(e)}updateFromJSON(e){return super.updateFromJSON(e).setValue(e.value).setChecked(e.checked)}exportDOM(e){const t=this.createDOM(e._config);t.style.textAlign=this.getFormatType();const r=this.getDirection();return r&&(t.dir=r),{element:t}}exportJSON(){return{...super.exportJSON(),checked:this.getChecked(),value:this.getValue()}}append(...e){for(let r=0;r<e.length;r++){const n=e[r];if(t.$isElementNode(n)&&this.canMergeWith(n)){const e=n.getChildren();this.append(...e),n.remove()}else super.append(n)}return this}replace(e,n){if(v(e))return super.replace(e);this.setIndent(0);const s=this.getParentOrThrow();if(!E(s))return e;if(s.__first===this.getKey())s.insertBefore(e);else if(s.__last===this.getKey())s.insertAfter(e);else{const t=A(s.getListType());let r=this.getNextSibling();for(;r;){const e=r;r=r.getNextSibling(),t.append(e)}s.insertAfter(e),e.insertAfter(t)}return n&&(t.$isElementNode(e)||r(139),this.getChildren().forEach((t=>{e.append(t)}))),this.remove(),0===s.getChildrenSize()&&s.remove(),e}insertAfter(e,t=!0){const n=this.getParentOrThrow();if(E(n)||r(39),v(e))return super.insertAfter(e,t);const s=this.getNextSiblings();if(n.insertAfter(e,t),0!==s.length){const r=A(n.getListType());s.forEach((e=>r.append(e))),e.insertAfter(r,t)}return e}remove(e){const t=this.getPreviousSibling(),r=this.getNextSibling();super.remove(e),t&&r&&o(t)&&o(r)&&(d(t.getFirstChild(),r.getFirstChild()),r.remove())}insertNewAfter(e,t=!0){const r=x().updateFromJSON(this.exportJSON()).setChecked(!this.getChecked()&&void 0);return this.insertAfter(r,t),r}collapseAtStart(e){const r=t.$createParagraphNode();this.getChildren().forEach((e=>r.append(e)));const n=this.getParentOrThrow(),s=n.getParentOrThrow(),i=v(s);if(1===n.getChildrenSize())if(i)n.remove(),s.select();else{n.insertBefore(r),n.remove();const t=e.anchor,s=e.focus,i=r.getKey();"element"===t.type&&t.getNode().is(this)&&t.set(i,t.offset,"element"),"element"===s.type&&s.getNode().is(this)&&s.set(i,s.offset,"element")}else n.insertBefore(r),this.remove();return!0}getValue(){return this.getLatest().__value}setValue(e){const t=this.getWritable();return t.__value=e,t}getChecked(){const e=this.getLatest();let t;const r=this.getParent();return E(r)&&(t=r.getListType()),"check"===t?Boolean(e.__checked):void 0}setChecked(e){const t=this.getWritable();return t.__checked=e,t}toggleChecked(){const e=this.getWritable();return e.setChecked(!e.__checked)}getIndent(){const e=this.getParent();if(null===e||!this.isAttached())return this.getLatest().__indent;let t=e.getParentOrThrow(),r=0;for(;v(t);)t=t.getParentOrThrow().getParentOrThrow(),r++;return r}setIndent(e){"number"!=typeof e&&r(117),(e=Math.floor(e))>=0||r(199);let t=this.getIndent();for(;t!==e;)t<e?(p(this),t++):(f(this),t--);return this}canInsertAfter(e){return v(e)}canReplaceWith(e){return v(e)}canMergeWith(e){return v(e)||t.$isParagraphNode(e)}extractWithChild(e,r){if(!t.$isRangeSelection(r))return!1;const n=r.anchor.getNode(),s=r.focus.getNode();return this.isParentOf(n)&&this.isParentOf(s)&&this.getTextContent().length===r.getTextContent().length}isParentRequired(){return!0}createParentElementNode(){return A("bullet")}canMergeWhenEmpty(){return!0}}function C(t,r,n){const s=[],i=[],o=r.list,l=o?o.listitem:void 0;let a;if(o&&o.nested&&(a=o.nested.listitem),void 0!==l&&s.push(..._(l)),o){const e=n.getParent(),t=E(e)&&"check"===e.getListType(),r=n.getChecked();t&&!r||i.push(o.listitemUnchecked),t&&r||i.push(o.listitemChecked),t&&s.push(r?o.listitemChecked:o.listitemUnchecked)}if(void 0!==a){const e=_(a);n.getChildren().some((e=>E(e)))?s.push(...e):i.push(...e)}i.length>0&&e.removeClassNamesFromElement(t,...i),s.length>0&&e.addClassNamesToElement(t,...s)}function N(e,t,r,n){E(t.getFirstChild())?(e.removeAttribute("role"),e.removeAttribute("tabIndex"),e.removeAttribute("aria-checked")):(e.setAttribute("role","checkbox"),e.setAttribute("tabIndex","-1"),r&&t.__checked===r.__checked||e.setAttribute("aria-checked",t.getChecked()?"true":"false"))}function S(e){if(e.classList.contains("task-list-item"))for(const t of e.children)if("INPUT"===t.tagName)return y(t);const t=e.getAttribute("aria-checked");return{node:x("true"===t||"false"!==t&&void 0)}}function y(e){if(!("checkbox"===e.getAttribute("type")))return{node:null};return{node:x(e.hasAttribute("checked"))}}function x(e){return t.$applyNodeReplacement(new T(void 0,e))}function v(e){return e instanceof T}class O extends t.ElementNode{static getType(){return"list"}static clone(e){const t=e.__listType||b[e.__tag];return new O(t,e.__start,e.__key)}constructor(e="number",t=1,r){super(r);const n=b[e]||e;this.__listType=n,this.__tag="number"===n?"ol":"ul",this.__start=t}getTag(){return this.__tag}setListType(e){const t=this.getWritable();return t.__listType=e,t.__tag="number"===e?"ol":"ul",t}getListType(){return this.__listType}getStart(){return this.__start}setStart(e){const t=this.getWritable();return t.__start=e,t}createDOM(e,t){const r=this.__tag,n=document.createElement(r);return 1!==this.__start&&n.setAttribute("start",String(this.__start)),n.__lexicalListType=this.__listType,L(n,e.theme,this),n}updateDOM(e,t,r){return e.__tag!==this.__tag||(L(t,r.theme,this),!1)}static transform(){return e=>{E(e)||r(163),function(e){const t=e.getNextSibling();E(t)&&e.getListType()===t.getListType()&&d(e,t)}(e),function(e){const t="check"!==e.getListType();let r=e.getStart();for(const n of e.getChildren())v(n)&&(n.getValue()!==r&&n.setValue(r),t&&null!=n.getLatest().__checked&&n.setChecked(void 0),E(n.getFirstChild())||r++)}(e)}}static importDOM(){return{ol:()=>({conversion:P,priority:0}),ul:()=>({conversion:P,priority:0})}}static importJSON(e){return A().updateFromJSON(e)}updateFromJSON(e){return super.updateFromJSON(e).setListType(e.listType).setStart(e.start)}exportDOM(t){const r=this.createDOM(t._config,t);return e.isHTMLElement(r)&&(1!==this.__start&&r.setAttribute("start",String(this.__start)),"check"===this.__listType&&r.setAttribute("__lexicalListType","check")),{element:r}}exportJSON(){return{...super.exportJSON(),listType:this.getListType(),start:this.getStart(),tag:this.getTag()}}canBeEmpty(){return!1}canIndent(){return!1}append(...e){for(let r=0;r<e.length;r++){const n=e[r];if(v(n))super.append(n);else{const e=x();if(E(n))e.append(n);else if(t.$isElementNode(n))if(n.isInline())e.append(n);else{const r=t.$createTextNode(n.getTextContent());e.append(r)}else e.append(n);super.append(e)}}return this}extractWithChild(e){return v(e)}}function L(t,r,s){const i=[],o=[],l=r.list;if(void 0!==l){const e=l[`${s.__tag}Depth`]||[],t=n(s)-1,r=t%e.length,a=e[r],c=l[s.__tag];let h;const g=l.nested,d=l.checklist;if(void 0!==g&&g.list&&(h=g.list),void 0!==c&&i.push(c),void 0!==d&&"check"===s.__listType&&i.push(d),void 0!==a){i.push(..._(a));for(let t=0;t<e.length;t++)t!==r&&o.push(s.__tag+t)}if(void 0!==h){const e=_(h);t>1?i.push(...e):o.push(...e)}}o.length>0&&e.removeClassNamesFromElement(t,...o),i.length>0&&e.addClassNamesToElement(t,...i)}function k(e){const t=[];for(let r=0;r<e.length;r++){const n=e[r];if(v(n)){t.push(n);const e=n.getChildren();e.length>1&&e.forEach((e=>{E(e)&&t.push(l(e))}))}else t.push(l(n))}return t}function P(t){const r=t.nodeName.toLowerCase();let n=null;if("ol"===r){n=A("number",t.start)}else"ul"===r&&(n=function(t){if("check"===t.getAttribute("__lexicallisttype")||t.classList.contains("contains-task-list"))return!0;for(const r of t.childNodes)if(e.isHTMLElement(r)&&r.hasAttribute("aria-checked"))return!0;return!1}(t)?A("check"):A("bullet"));return{after:k,node:n}}const b={ol:"number",ul:"bullet"};function A(e="number",r=1){return t.$applyNodeReplacement(new O(e,r))}function E(e){return e instanceof O}const F=t.createCommand("INSERT_UNORDERED_LIST_COMMAND"),R=t.createCommand("INSERT_ORDERED_LIST_COMMAND"),$=t.createCommand("INSERT_CHECK_LIST_COMMAND"),M=t.createCommand("REMOVE_LIST_COMMAND");exports.$createListItemNode=x,exports.$createListNode=A,exports.$getListDepth=n,exports.$handleListInsertParagraph=m,exports.$insertList=c,exports.$isListItemNode=v,exports.$isListNode=E,exports.$removeList=u,exports.INSERT_CHECK_LIST_COMMAND=$,exports.INSERT_ORDERED_LIST_COMMAND=R,exports.INSERT_UNORDERED_LIST_COMMAND=F,exports.ListItemNode=T,exports.ListNode=O,exports.REMOVE_LIST_COMMAND=M,exports.insertList=function(e,t){e.update((()=>c(t)))},exports.registerList=function(r){return e.mergeRegister(r.registerCommand(R,(()=>(c("number"),!0)),t.COMMAND_PRIORITY_LOW),r.registerCommand(F,(()=>(c("bullet"),!0)),t.COMMAND_PRIORITY_LOW),r.registerCommand(M,(()=>(u(),!0)),t.COMMAND_PRIORITY_LOW),r.registerCommand(t.INSERT_PARAGRAPH_COMMAND,(()=>m()),t.COMMAND_PRIORITY_LOW),r.registerNodeTransform(T,(e=>{const r=e.getFirstChild();if(r){if(t.$isTextNode(r)){const t=r.getStyle(),n=r.getFormat();e.getTextStyle()!==t&&e.setTextStyle(t),e.getTextFormat()!==n&&e.setTextFormat(n)}}else{const r=t.$getSelection();t.$isRangeSelection(r)&&(r.style!==e.getTextStyle()||r.format!==e.getTextFormat())&&r.isCollapsed()&&e.is(r.anchor.getNode())&&e.setTextStyle(r.style).setTextFormat(r.format)}})),r.registerNodeTransform(t.TextNode,(e=>{const t=e.getParent();if(v(t)&&e.is(t.getFirstChild())){const r=e.getStyle(),n=e.getFormat();r===t.getTextStyle()&&n===t.getTextFormat()||t.setTextStyle(r).setTextFormat(n)}})))},exports.removeList=function(e){e.update((()=>u()))};
