/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import { addClassNamesToElement } from '@lexical/utils';
import { TextNode, $applyNodeReplacement } from 'lexical';

/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */


/** @noInheritDoc */
class HashtagNode extends TextNode {
  static getType() {
    return 'hashtag';
  }
  static clone(node) {
    return new HashtagNode(node.__text, node.__key);
  }
  createDOM(config) {
    const element = super.createDOM(config);
    addClassNamesToElement(element, config.theme.hashtag);
    return element;
  }
  static importJSON(serializedNode) {
    return $createHashtagNode().updateFromJSON(serializedNode);
  }
  canInsertTextBefore() {
    return false;
  }
  isTextEntity() {
    return true;
  }
}

/**
 * Generates a HashtagNode, which is a string following the format of a # followed by some text, eg. #lexical.
 * @param text - The text used inside the HashtagNode.
 * @returns - The HashtagNode with the embedded text.
 */
function $createHashtagNode(text = '') {
  return $applyNodeReplacement(new HashtagNode(text));
}

/**
 * Determines if node is a HashtagNode.
 * @param node - The node to be checked.
 * @returns true if node is a HashtagNode, false otherwise.
 */
function $isHashtagNode(node) {
  return node instanceof HashtagNode;
}

export { $createHashtagNode, $isHashtagNode, HashtagNode };
