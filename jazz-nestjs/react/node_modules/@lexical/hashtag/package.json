{"name": "@lexical/hashtag", "description": "This package contains the functionality for Lexical hashtags.", "keywords": ["lexical", "editor", "rich-text", "hashtag"], "license": "MIT", "version": "0.27.2", "main": "LexicalHashtag.js", "types": "index.d.ts", "dependencies": {"@lexical/utils": "0.27.2", "lexical": "0.27.2"}, "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-hashtag"}, "module": "LexicalHashtag.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalHashtag.dev.mjs", "production": "./LexicalHashtag.prod.mjs", "node": "./LexicalHashtag.node.mjs", "default": "./LexicalHashtag.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalHashtag.dev.js", "production": "./LexicalHashtag.prod.js", "default": "./LexicalHashtag.js"}}}}