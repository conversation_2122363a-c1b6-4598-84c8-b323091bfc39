/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */
import type {
  EditorConfig,
  LexicalNode,
  NodeKey,
  RangeSelection,
  SerializedElementNode,
} from 'lexical';
import {ElementNode} from 'lexical';
declare export class OverflowNode extends ElementNode {
  static getType(): string;
  static clone(node: OverflowNode): OverflowNode;
  constructor(key?: NodeKey): void;
  createDOM(config: EditorConfig): HTMLElement;
  insertNewAfter(selection: RangeSelection): null | LexicalNode;
  excludeFromCopy(): boolean;
  static importJSON(serializedNode: SerializedOverflowNode): OverflowNode;
}
declare export function $createOverflowNode(): OverflowNode;
declare export function $isOverflowNode(
  node: ?LexicalNode,
): node is OverflowNode;

export type SerializedOverflowNode = SerializedElementNode;
