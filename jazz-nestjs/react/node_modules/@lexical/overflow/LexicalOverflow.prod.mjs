/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{ElementNode as e,$applyNodeReplacement as r}from"lexical";class t extends e{static getType(){return"overflow"}static clone(e){return new t(e.__key)}static importJSON(e){return n().updateFromJSON(e)}static importDOM(){return null}createDOM(e){const r=document.createElement("span"),t=e.theme.characterLimit;return"string"==typeof t&&(r.className=t),r}updateDOM(e,r){return!1}insertNewAfter(e,r=!0){return this.getParentOrThrow().insertNewAfter(e,r)}excludeFromCopy(){return!0}static transform(){return e=>{o(e)||function(e,...r){const t=new URL("https://lexical.dev/docs/error"),n=new URLSearchParams;n.append("code",e);for(const e of r)n.append("v",e);throw t.search=n.toString(),Error(`Minified Lexical error #${e}; visit ${t.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}(204),e.isEmpty()&&e.remove()}}}function n(){return r(new t)}function o(e){return e instanceof t}export{n as $createOverflowNode,o as $isOverflowNode,t as OverflowNode};
