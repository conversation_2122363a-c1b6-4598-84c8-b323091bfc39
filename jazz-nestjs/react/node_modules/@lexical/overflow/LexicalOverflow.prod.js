/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("lexical");class r extends e.ElementNode{static getType(){return"overflow"}static clone(e){return new r(e.__key)}static importJSON(e){return t().updateFromJSON(e)}static importDOM(){return null}createDOM(e){const r=document.createElement("span"),t=e.theme.characterLimit;return"string"==typeof t&&(r.className=t),r}updateDOM(e,r){return!1}insertNewAfter(e,r=!0){return this.getParentOrThrow().insertNewAfter(e,r)}excludeFromCopy(){return!0}static transform(){return e=>{n(e)||function(e,...r){const t=new URL("https://lexical.dev/docs/error"),n=new URLSearchParams;n.append("code",e);for(const e of r)n.append("v",e);throw t.search=n.toString(),Error(`Minified Lexical error #${e}; visit ${t.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}(204),e.isEmpty()&&e.remove()}}}function t(){return e.$applyNodeReplacement(new r)}function n(e){return e instanceof r}exports.$createOverflowNode=t,exports.$isOverflowNode=n,exports.OverflowNode=r;
