{"name": "@lexical/overflow", "description": "This package contains selection overflow helpers and nodes for Lexical.", "keywords": ["lexical", "editor", "rich-text", "overflow"], "license": "MIT", "version": "0.27.2", "main": "LexicalOverflow.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-overflow"}, "module": "LexicalOverflow.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalOverflow.dev.mjs", "production": "./LexicalOverflow.prod.mjs", "node": "./LexicalOverflow.node.mjs", "default": "./LexicalOverflow.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalOverflow.dev.js", "production": "./LexicalOverflow.prod.js", "default": "./LexicalOverflow.js"}}}, "dependencies": {"lexical": "0.27.2"}}