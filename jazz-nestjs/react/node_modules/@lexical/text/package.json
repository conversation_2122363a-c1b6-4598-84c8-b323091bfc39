{"name": "@lexical/text", "description": "This package contains utilities and helpers for handling Lexical text.", "keywords": ["lexical", "editor", "rich-text", "list", "text"], "license": "MIT", "version": "0.27.2", "main": "LexicalText.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-text"}, "module": "LexicalText.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalText.dev.mjs", "production": "./LexicalText.prod.mjs", "node": "./LexicalText.node.mjs", "default": "./LexicalText.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalText.dev.js", "production": "./LexicalText.prod.js", "default": "./LexicalText.js"}}}, "dependencies": {"lexical": "0.27.2"}}