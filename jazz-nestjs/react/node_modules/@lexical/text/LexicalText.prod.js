/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var t=require("lexical");function e(){return t.$getRoot().getTextContent()}function n(t,n=!0){if(t)return!1;let r=e();return n&&(r=r.trim()),""===r}function r(e){if(!n(e,!1))return!1;const r=t.$getRoot().getChildren(),o=r.length;if(o>1)return!1;for(let e=0;e<o;e++){const n=r[e];if(t.$isDecoratorNode(n))return!1;if(t.$isElementNode(n)){if(!t.$isParagraphNode(n))return!1;if(0!==n.__indent)return!1;const r=n.getChildren(),o=r.length;for(let n=0;n<o;n++){const n=r[e];if(!t.$isTextNode(n))return!1}}}return!0}function o(t,...e){const n=new URL("https://lexical.dev/docs/error"),r=new URLSearchParams;r.append("code",t);for(const t of e)r.append("v",t);throw n.search=r.toString(),Error(`Minified Lexical error #${t}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}exports.$canShowPlaceholder=r,exports.$canShowPlaceholderCurry=function(t){return()=>r(t)},exports.$findTextIntersectionFromCharacters=function(e,n){let r=e.getFirstChild(),o=0;t:for(;null!==r;){if(t.$isElementNode(r)){const t=r.getFirstChild();if(null!==t){r=t;continue}}else if(t.$isTextNode(r)){const t=r.getTextContentSize();if(o+t>n)return{node:r,offset:n-o};o+=t}const e=r.getNextSibling();if(null!==e){r=e;continue}let i=r.getParent();for(;null!==i;){const t=i.getNextSibling();if(null!==t){r=t;continue t}i=i.getParent()}break}return null},exports.$isRootTextContentEmpty=n,exports.$isRootTextContentEmptyCurry=function(t,e){return()=>n(t,e)},exports.$rootTextContent=e,exports.registerLexicalTextEntity=function(e,n,r,i){const s=t=>t instanceof r,l=e=>{const n=t.$createTextNode(e.getTextContent());n.setFormat(e.getFormat()),e.replace(n)};return[e.registerNodeTransform(t.TextNode,(e=>{if(!e.isSimpleText())return;let r,u=e.getPreviousSibling(),c=e.getTextContent(),f=e;if(t.$isTextNode(u)){const t=u.getTextContent(),r=n(t+c);if(s(u)){if(null===r||0!==(t=>t.getLatest().__mode)(u))return void l(u);{const n=r.end-t.length;if(n>0){const r=t+c.slice(0,n);if(u.select(),u.setTextContent(r),n===c.length)e.remove();else{const t=c.slice(n);e.setTextContent(t)}return}}}else if(null===r||r.start<t.length)return}let d=0;for(;;){r=n(c);let e,a=null===r?"":c.slice(r.end);if(c=a,""===a){const e=f.getNextSibling();if(t.$isTextNode(e)){a=f.getTextContent()+e.getTextContent();const t=n(a);if(null===t)return void(s(e)?l(e):e.markDirty());if(0!==t.start)return}}if(null===r)return;if(0===r.start&&t.$isTextNode(u)&&u.isTextEntity()){d+=r.end;continue}0===r.start?[e,f]=f.splitText(r.end):[,e,f]=f.splitText(r.start+d,r.end+d),void 0===e&&o(165,"nodeToReplace");const x=i(e);if(x.setFormat(e.getFormat()),e.replace(x),null==f)return;d=0,u=x}})),e.registerNodeTransform(r,(e=>{const r=e.getTextContent(),o=n(r);if(null===o||0!==o.start)return void l(e);if(r.length>o.end)return void e.splitText(o.end);const i=e.getPreviousSibling();t.$isTextNode(i)&&i.isTextEntity()&&(l(i),l(e));const u=e.getNextSibling();t.$isTextNode(u)&&u.isTextEntity()&&(l(u),s(e)&&l(e))}))]};
