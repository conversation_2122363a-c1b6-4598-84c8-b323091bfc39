/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{$getRoot as t,$isDecoratorNode as e,$isElementNode as n,$isParagraphNode as r,$isTextNode as i,TextNode as o,$createTextNode as l}from"lexical";function s(){return t().getTextContent()}function f(t,e=!0){if(t)return!1;let n=s();return e&&(n=n.trim()),""===n}function u(t,e){return()=>f(t,e)}function c(o){if(!f(o,!1))return!1;const l=t().getChildren(),s=l.length;if(s>1)return!1;for(let t=0;t<s;t++){const o=l[t];if(e(o))return!1;if(n(o)){if(!r(o))return!1;if(0!==o.__indent)return!1;const e=o.getChildren(),n=e.length;for(let r=0;r<n;r++){const n=e[t];if(!i(n))return!1}}}return!0}function g(t){return()=>c(t)}function a(t,e){let r=t.getFirstChild(),o=0;t:for(;null!==r;){if(n(r)){const t=r.getFirstChild();if(null!==t){r=t;continue}}else if(i(r)){const t=r.getTextContentSize();if(o+t>e)return{node:r,offset:e-o};o+=t}const t=r.getNextSibling();if(null!==t){r=t;continue}let l=r.getParent();for(;null!==l;){const t=l.getNextSibling();if(null!==t){r=t;continue t}l=l.getParent()}break}return null}function d(t,...e){const n=new URL("https://lexical.dev/docs/error"),r=new URLSearchParams;r.append("code",t);for(const t of e)r.append("v",t);throw n.search=r.toString(),Error(`Minified Lexical error #${t}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}function x(t,e,n,r){const s=t=>t instanceof n,f=t=>{const e=l(t.getTextContent());e.setFormat(t.getFormat()),t.replace(e)};return[t.registerNodeTransform(o,(t=>{if(!t.isSimpleText())return;let n,o=t.getPreviousSibling(),l=t.getTextContent(),u=t;if(i(o)){const n=o.getTextContent(),r=e(n+l);if(s(o)){if(null===r||0!==(t=>t.getLatest().__mode)(o))return void f(o);{const e=r.end-n.length;if(e>0){const r=n+l.slice(0,e);if(o.select(),o.setTextContent(r),e===l.length)t.remove();else{const n=l.slice(e);t.setTextContent(n)}return}}}else if(null===r||r.start<n.length)return}let c=0;for(;;){n=e(l);let t,g=null===n?"":l.slice(n.end);if(l=g,""===g){const t=u.getNextSibling();if(i(t)){g=u.getTextContent()+t.getTextContent();const n=e(g);if(null===n)return void(s(t)?f(t):t.markDirty());if(0!==n.start)return}}if(null===n)return;if(0===n.start&&i(o)&&o.isTextEntity()){c+=n.end;continue}0===n.start?[t,u]=u.splitText(n.end):[,t,u]=u.splitText(n.start+c,n.end+c),void 0===t&&d(165,"nodeToReplace");const a=r(t);if(a.setFormat(t.getFormat()),t.replace(a),null==u)return;c=0,o=a}})),t.registerNodeTransform(n,(t=>{const n=t.getTextContent(),r=e(n);if(null===r||0!==r.start)return void f(t);if(n.length>r.end)return void t.splitText(r.end);const o=t.getPreviousSibling();i(o)&&o.isTextEntity()&&(f(o),f(t));const l=t.getNextSibling();i(l)&&l.isTextEntity()&&(f(l),s(t)&&f(t))}))]}export{c as $canShowPlaceholder,g as $canShowPlaceholderCurry,a as $findTextIntersectionFromCharacters,f as $isRootTextContentEmpty,u as $isRootTextContentEmptyCurry,s as $rootTextContent,x as registerLexicalTextEntity};
