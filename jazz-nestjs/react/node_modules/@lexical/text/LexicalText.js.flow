/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */
import type {ElementNode, LexicalEditor, RootNode, TextNode} from 'lexical';
export type TextNodeWithOffset = {
  node: TextNode,
  offset: number,
};
declare export function $findTextIntersectionFromCharacters(
  root: RootNode,
  targetCharacters: number,
): null | {
  node: TextNode,
  offset: number,
};
declare export function $isRootTextContentEmpty(
  isEditorComposing: boolean,
  trim?: boolean,
): boolean;
declare export function $isRootTextContentEmptyCurry(
  isEditorComposing: boolean,
  trim?: boolean,
): () => boolean;
declare export function $rootTextContent(): string;
declare export function $canShowPlaceholder(isComposing: boolean): boolean;
declare export function $canShowPlaceholderCurry(
  isEditorComposing: boolean,
): () => boolean;
export type EntityMatch = {
  end: number,
  start: number,
};
declare export function registerLexicalTextEntity<N: TextNode>(
  editor: LexicalEditor,
  getMatch: (text: string) => null | EntityMatch,
  targetNode: Class<N>,
  createNode: (textNode: TextNode) => N,
): Array<() => void>;
