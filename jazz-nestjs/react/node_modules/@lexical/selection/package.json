{"name": "@lexical/selection", "description": "This package contains utilities and helpers for handling Lexical selection.", "keywords": ["lexical", "editor", "rich-text", "list", "selection"], "license": "MIT", "version": "0.27.2", "main": "LexicalSelection.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-selection"}, "module": "LexicalSelection.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalSelection.dev.mjs", "production": "./LexicalSelection.prod.mjs", "node": "./LexicalSelection.node.mjs", "default": "./LexicalSelection.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalSelection.dev.js", "production": "./LexicalSelection.prod.js", "default": "./LexicalSelection.js"}}}, "dependencies": {"lexical": "0.27.2"}}