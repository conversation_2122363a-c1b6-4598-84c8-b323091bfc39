/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("lexical");function t(e,...t){const n=new URL("https://lexical.dev/docs/error"),o=new URLSearchParams;o.append("code",e);for(const e of t)o.append("v",e);throw n.search=o.toString(),Error(`Minified Lexical error #${e}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}const n=new Map;function o(e){let t=e;for(;null!=t;){if(t.nodeType===Node.TEXT_NODE)return t;t=t.firstChild}return null}function s(e){const t=e.parentNode;if(null==t)throw new Error("Should never happen");return[t,Array.from(t.childNodes).indexOf(e)]}function r(e){const t={};if(!e)return t;const n=e.split(";");for(const e of n)if(""!==e){const[n,o]=e.split(/:([^]+)/);n&&o&&(t[n.trim()]=o.trim())}return t}function i(e){let t=n.get(e);return void 0===t&&(t=r(e),n.set(e,t)),t}function l(e){let t="";for(const n in e)n&&(t+=`${n}: ${e[n]};`);return t}function c(t,n,o){let s=n.getNode(),r=o;if(e.$isElementNode(s)){const e=s.getDescendantByIndex(n.offset);null!==e&&(s=e)}for(;r>0&&null!==s;){if(e.$isElementNode(s)){const e=s.getLastDescendant();null!==e&&(s=e)}let o=s.getPreviousSibling(),i=0;if(null===o){let e=s.getParentOrThrow(),t=e.getPreviousSibling();for(;null===t;){if(e=e.getParent(),null===e){o=null;break}t=e.getPreviousSibling()}null!==e&&(i=e.isInline()?0:2,o=t)}let l=s.getTextContent();""===l&&e.$isElementNode(s)&&!s.isInline()&&(l="\n\n");const c=l.length;if(!e.$isTextNode(s)||r>=c){const t=s.getParent();s.remove(),null==t||0!==t.getChildrenSize()||e.$isRootNode(t)||t.remove(),r-=c+i,s=o}else{const o=s.getKey(),i=t.getEditorState().read((()=>{const t=e.$getNodeByKey(o);return e.$isTextNode(t)&&t.isSimpleText()?t.getTextContent():null})),f=c-r,a=l.slice(0,f);if(null!==i&&i!==l){const t=e.$getPreviousSelection();let n=s;if(s.isSimpleText())s.setTextContent(i);else{const t=e.$createTextNode(i);s.replace(t),n=t}if(e.$isRangeSelection(t)&&t.isCollapsed()){const e=t.anchor.offset;n.select(e,e)}}else if(s.isSimpleText()){const e=n.key===o;let t=n.offset;t<r&&(t=c);const i=e?t-r:0,l=e?t:f;if(e&&0===i){const[e]=s.splitText(i,l);e.remove()}else{const[,e]=s.splitText(i,l);e.remove()}}else{const t=e.$createTextNode(a);s.replace(t)}r=0}}}function f(o,s){(e.$isRangeSelection(o)?o.isCollapsed():e.$isTextNode(o)||e.$isElementNode(o))||t(280);const r=i(e.$isRangeSelection(o)?o.style:e.$isTextNode(o)?o.getStyle():o.getTextStyle()),c=Object.entries(s).reduce(((e,[t,n])=>("function"==typeof n?e[t]=n(r[t],o):null===n?delete e[t]:e[t]=n,e)),{...r}),f=l(c);e.$isRangeSelection(o)||e.$isTextNode(o)?o.setStyle(f):o.setTextStyle(f),n.set(f,c)}function a(t){const n=e.$getSelection();if(!n)return;const o=new Map;if(e.$isRangeSelection(n))for(const t of e.$caretRangeFromSelection(n).getTextSlices())t&&o.set(t.caret.origin.getKey(),t.getSliceIndices());const s=n.getNodes();for(const n of s){if(!e.$isTextNode(n)||!n.canHaveFormat())continue;const[s,i]=(r=n,o.get(r.getKey())||[0,r.getTextContentSize()]);if(i!==s)if(e.$isTokenOrSegmented(n)||0===s&&i===n.getTextContentSize())t(n);else{t(n.splitText(s,i)[0===s?0:1])}}var r;e.$isRangeSelection(n)&&"text"===n.anchor.type&&"text"===n.focus.type&&n.anchor.key===n.focus.key&&d(n)}function d(e){if(e.isBackward()){const{anchor:t,focus:n}=e,{key:o,offset:s,type:r}=t;t.set(n.key,n.offset,n.type),n.set(o,s,r)}}function g(e,t){const n=e.getFormatType(),o=e.getIndent();n!==t.getFormatType()&&t.setFormat(n),o!==t.getIndent()&&t.setIndent(o)}function u(e){return e.getNode().isAttached()}function p(t){let n=t;for(;null!==n&&!e.$isRootOrShadowRoot(n);){const e=n.getLatest(),t=n.getParent();0===e.getChildrenSize()&&n.remove(!0),n=t}}function h(n,o,s,r,i=null){if(0===o.length)return;const l=o[0],c=new Map,f=[];let a=e.$isElementNode(l)?l:l.getParentOrThrow();a.isInline()&&(a=a.getParentOrThrow());let d=!1;for(;null!==a;){const t=a.getPreviousSibling();if(null!==t){a=t,d=!0;break}if(a=a.getParentOrThrow(),e.$isRootOrShadowRoot(a))break}const g=new Set;for(let t=0;t<s;t++){const n=o[t];e.$isElementNode(n)&&0===n.getChildrenSize()&&g.add(n.getKey())}const h=new Set;for(let n=0;n<s;n++){const s=o[n];let i=s.getParent();if(null!==i&&i.isInline()&&(i=i.getParent()),null!==i&&e.$isLeafNode(s)&&!h.has(s.getKey())){const t=i.getKey();if(void 0===c.get(t)){const n=r();n.setFormat(i.getFormatType()),n.setIndent(i.getIndent()),f.push(n),c.set(t,n),i.getChildren().forEach((t=>{n.append(t),h.add(t.getKey()),e.$isElementNode(t)&&t.getChildrenKeys().forEach((e=>h.add(e)))})),p(i)}}else if(g.has(s.getKey())){e.$isElementNode(s)||t(179);const n=r();n.setFormat(s.getFormatType()),n.setIndent(s.getIndent()),f.push(n),s.remove(!0)}}if(null!==i)for(let e=0;e<f.length;e++){const t=f[e];i.append(t)}let $=null;if(e.$isRootOrShadowRoot(a))if(d)if(null!==i)a.insertAfter(i);else for(let e=f.length-1;e>=0;e--){const t=f[e];a.insertAfter(t)}else{const t=a.getFirstChild();if(e.$isElementNode(t)&&(a=t),null===t)if(i)a.append(i);else for(let e=0;e<f.length;e++){const t=f[e];a.append(t),$=t}else if(null!==i)t.insertBefore(i);else for(let e=0;e<f.length;e++){const n=f[e];t.insertBefore(n),$=n}}else if(i)a.insertAfter(i);else for(let e=f.length-1;e>=0;e--){const t=f[e];a.insertAfter(t),$=t}const S=e.$getPreviousSelection();e.$isRangeSelection(S)&&u(S.anchor)&&u(S.focus)?e.$setSelection(S.clone()):null!==$?$.selectEnd():n.dirty=!0}function $(e,t,n,o){e.modify(t?"extend":"move",n,o)}function S(t){const n=t.anchor.getNode();return"rtl"===(e.$isRootNode(n)?n:n.getParentOrThrow()).getDirection()}function y(e,t,n){const o=i(e.getStyle());return null!==o&&o[t]||n}function m(e,t){let n=e;for(;null!==n&&null!==n.getParent()&&!t(n);)n=n.getParentOrThrow();return t(n)?n:null}const x=c;exports.$cloneWithProperties=e.$cloneWithProperties,exports.$selectAll=e.$selectAll,exports.$addNodeStyle=function(e){const t=e.getStyle(),o=r(t);n.set(t,o)},exports.$copyBlockFormatIndent=g,exports.$ensureForwardRangeSelection=d,exports.$forEachSelectedTextNode=a,exports.$getSelectionStyleValueForProperty=function(t,n,o=""){let s=null;const r=t.getNodes(),l=t.anchor,c=t.focus,f=t.isBackward(),a=f?c.offset:l.offset,d=f?c.getNode():l.getNode();if(e.$isRangeSelection(t)&&t.isCollapsed()&&""!==t.style){const e=i(t.style);if(null!==e&&n in e)return e[n]}for(let t=0;t<r.length;t++){const i=r[t];if((0===t||0!==a||!i.is(d))&&e.$isTextNode(i)){const e=y(i,n,o);if(null===s)s=e;else if(s!==e){s="";break}}}return null===s?o:s},exports.$isAtNodeEnd=function(n){if("text"===n.type)return n.offset===n.getNode().getTextContentSize();const o=n.getNode();return e.$isElementNode(o)||t(177),n.offset===o.getChildrenSize()},exports.$isParentElementRTL=S,exports.$moveCaretSelection=$,exports.$moveCharacter=function(e,t,n){const o=S(e);$(e,t,n?!o:o,"character")},exports.$patchStyleText=function(t,n){if(e.$isRangeSelection(t)&&t.isCollapsed()){f(t,n);const o=t.anchor.getNode();e.$isElementNode(o)&&o.isEmpty()&&f(o,n)}a((e=>{f(e,n)}))},exports.$setBlocksType=function(t,n,o=g){if(null===t)return;const s=t.getStartEndPoints(),r=new Map;let i=null;if(s){const[t,n]=s;i=e.$createRangeSelection(),i.anchor.set(t.key,t.offset,t.type),i.focus.set(n.key,n.offset,n.type);const o=m(t.getNode(),e.INTERNAL_$isBlock),l=m(n.getNode(),e.INTERNAL_$isBlock);e.$isElementNode(o)&&r.set(o.getKey(),o),e.$isElementNode(l)&&r.set(l.getKey(),l)}for(const n of t.getNodes())e.$isElementNode(n)&&e.INTERNAL_$isBlock(n)&&r.set(n.getKey(),n);for(const[e,t]of r){const s=n();o(t,s),t.replace(s,!0),i&&(e===i.anchor.key&&i.anchor.set(s.getKey(),i.anchor.offset,i.anchor.type),e===i.focus.key&&i.focus.set(s.getKey(),i.focus.offset,i.focus.type))}i&&t.is(e.$getSelection())&&e.$setSelection(i)},exports.$shouldOverrideDefaultCharacterSelection=function(t,n){const o=e.$caretFromPoint(t.focus,n?"previous":"next");if(e.$isExtendableTextPointCaret(o))return!1;for(const t of e.$extendCaretToRange(o)){if(e.$isChildCaret(t))return!t.origin.isInline();if(!e.$isElementNode(t.origin)){if(e.$isDecoratorNode(t.origin))return!0;break}}return!1},exports.$sliceSelectedTextNodeContent=function(t,n){const o=t.getStartEndPoints();if(n.isSelected(t)&&!n.isSegmented()&&!n.isToken()&&null!==o){const[s,r]=o,i=t.isBackward(),l=s.getNode(),c=r.getNode(),f=n.is(l),a=n.is(c);if(f||a){const[o,s]=e.$getCharacterOffsets(t),r=l.is(c),f=n.is(i?c:l),a=n.is(i?l:c);let d,g=0;if(r)g=o>s?s:o,d=o>s?o:s;else if(f){g=i?s:o,d=void 0}else if(a){g=0,d=i?o:s}return n.__text=n.__text.slice(g,d),n}}return n},exports.$trimTextContentFromAnchor=c,exports.$wrapNodes=function(t,n,o=null){const s=t.getStartEndPoints(),r=s?s[0]:null,i=t.getNodes(),l=i.length;if(null!==r&&(0===l||1===l&&"element"===r.type&&0===r.getNode().getChildrenSize())){const e="text"===r.type?r.getNode().getParentOrThrow():r.getNode(),t=e.getChildren();let s=n();return s.setFormat(e.getFormatType()),s.setIndent(e.getIndent()),t.forEach((e=>s.append(e))),o&&(s=o.append(s)),void e.replace(s)}let c=null,f=[];for(let s=0;s<l;s++){const r=i[s];e.$isRootOrShadowRoot(r)?(h(t,f,f.length,n,o),f=[],c=r):null===c||null!==c&&e.$hasAncestor(r,c)?f.push(r):(h(t,f,f.length,n,o),f=[r])}h(t,f,f.length,n,o)},exports.createDOMRange=function(t,n,r,i,l){const c=n.getKey(),f=i.getKey(),a=document.createRange();let d=t.getElementByKey(c),g=t.getElementByKey(f),u=r,p=l;if(e.$isTextNode(n)&&(d=o(d)),e.$isTextNode(i)&&(g=o(g)),void 0===n||void 0===i||null===d||null===g)return null;"BR"===d.nodeName&&([d,u]=s(d)),"BR"===g.nodeName&&([g,p]=s(g));const h=d.firstChild;d===g&&null!=h&&"BR"===h.nodeName&&0===u&&0===p&&(p=1);try{a.setStart(d,u),a.setEnd(g,p)}catch(e){return null}return!a.collapsed||u===p&&c===f||(a.setStart(g,p),a.setEnd(d,u)),a},exports.createRectsFromDOMRange=function(e,t){const n=e.getRootElement();if(null===n)return[];const o=n.getBoundingClientRect(),s=getComputedStyle(n),r=parseFloat(s.paddingLeft)+parseFloat(s.paddingRight),i=Array.from(t.getClientRects());let l,c=i.length;i.sort(((e,t)=>{const n=e.top-t.top;return Math.abs(n)<=3?e.left-t.left:n}));for(let e=0;e<c;e++){const t=i[e],n=l&&l.top<=t.top&&l.top+l.height>t.top&&l.left+l.width>t.left,s=t.width+r===o.width;n||s?(i.splice(e--,1),c--):l=t}return i},exports.getCSSFromStyleObject=l,exports.getStyleObjectFromCSS=i,exports.trimTextContentFromAnchor=x;
