/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{$isTextNode as e,$getCharacterOffsets as t,$isElementNode as n,$isRootNode as o,$getNodeByKey as l,$getPreviousSelection as r,$createTextNode as s,$isRangeSelection as i,$getSelection as c,$caretRangeFromSelection as f,$isTokenOrSegmented as u,$createRangeSelection as g,INTERNAL_$isBlock as a,$setSelection as d,$isRootOrShadowRoot as p,$hasAncestor as h,$isLeafNode as y,$caretFromPoint as m,$isExtendableTextPointCaret as S,$extendCaretToRange as x,$isChildCaret as T,$isDecoratorNode as v}from"lexical";export{$cloneWithProperties,$selectAll}from"lexical";function N(e,...t){const n=new URL("https://lexical.dev/docs/error"),o=new URLSearchParams;o.append("code",e);for(const e of t)o.append("v",e);throw n.search=o.toString(),Error(`Minified Lexical error #${e}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}const w=new Map;function C(e){let t=e;for(;null!=t;){if(t.nodeType===Node.TEXT_NODE)return t;t=t.firstChild}return null}function P(e){const t=e.parentNode;if(null==t)throw new Error("Should never happen");return[t,Array.from(t.childNodes).indexOf(e)]}function k(t,n,o,l,r){const s=n.getKey(),i=l.getKey(),c=document.createRange();let f=t.getElementByKey(s),u=t.getElementByKey(i),g=o,a=r;if(e(n)&&(f=C(f)),e(l)&&(u=C(u)),void 0===n||void 0===l||null===f||null===u)return null;"BR"===f.nodeName&&([f,g]=P(f)),"BR"===u.nodeName&&([u,a]=P(u));const d=f.firstChild;f===u&&null!=d&&"BR"===d.nodeName&&0===g&&0===a&&(a=1);try{c.setStart(f,g),c.setEnd(u,a)}catch(e){return null}return!c.collapsed||g===a&&s===i||(c.setStart(u,a),c.setEnd(f,g)),c}function E(e,t){const n=e.getRootElement();if(null===n)return[];const o=n.getBoundingClientRect(),l=getComputedStyle(n),r=parseFloat(l.paddingLeft)+parseFloat(l.paddingRight),s=Array.from(t.getClientRects());let i,c=s.length;s.sort(((e,t)=>{const n=e.top-t.top;return Math.abs(n)<=3?e.left-t.left:n}));for(let e=0;e<c;e++){const t=s[e],n=i&&i.top<=t.top&&i.top+i.height>t.top&&i.left+i.width>t.left,l=t.width+r===o.width;n||l?(s.splice(e--,1),c--):i=t}return s}function K(e){const t={};if(!e)return t;const n=e.split(";");for(const e of n)if(""!==e){const[n,o]=e.split(/:([^]+)/);n&&o&&(t[n.trim()]=o.trim())}return t}function I(e){let t=w.get(e);return void 0===t&&(t=K(e),w.set(e,t)),t}function F(e){let t="";for(const n in e)n&&(t+=`${n}: ${e[n]};`);return t}function B(e,n){const o=e.getStartEndPoints();if(n.isSelected(e)&&!n.isSegmented()&&!n.isToken()&&null!==o){const[l,r]=o,s=e.isBackward(),i=l.getNode(),c=r.getNode(),f=n.is(i),u=n.is(c);if(f||u){const[o,l]=t(e),r=i.is(c),f=n.is(s?c:i),u=n.is(s?i:c);let g,a=0;if(r)a=o>l?l:o,g=o>l?o:l;else if(f){a=s?l:o,g=void 0}else if(u){a=0,g=s?o:l}return n.__text=n.__text.slice(a,g),n}}return n}function b(e){if("text"===e.type)return e.offset===e.getNode().getTextContentSize();const t=e.getNode();return n(t)||N(177),e.offset===t.getChildrenSize()}function O(t,c,f){let u=c.getNode(),g=f;if(n(u)){const e=u.getDescendantByIndex(c.offset);null!==e&&(u=e)}for(;g>0&&null!==u;){if(n(u)){const e=u.getLastDescendant();null!==e&&(u=e)}let f=u.getPreviousSibling(),a=0;if(null===f){let e=u.getParentOrThrow(),t=e.getPreviousSibling();for(;null===t;){if(e=e.getParent(),null===e){f=null;break}t=e.getPreviousSibling()}null!==e&&(a=e.isInline()?0:2,f=t)}let d=u.getTextContent();""===d&&n(u)&&!u.isInline()&&(d="\n\n");const p=d.length;if(!e(u)||g>=p){const e=u.getParent();u.remove(),null==e||0!==e.getChildrenSize()||o(e)||e.remove(),g-=p+a,u=f}else{const n=u.getKey(),o=t.getEditorState().read((()=>{const t=l(n);return e(t)&&t.isSimpleText()?t.getTextContent():null})),f=p-g,a=d.slice(0,f);if(null!==o&&o!==d){const e=r();let t=u;if(u.isSimpleText())u.setTextContent(o);else{const e=s(o);u.replace(e),t=e}if(i(e)&&e.isCollapsed()){const n=e.anchor.offset;t.select(n,n)}}else if(u.isSimpleText()){const e=c.key===n;let t=c.offset;t<g&&(t=p);const o=e?t-g:0,l=e?t:f;if(e&&0===o){const[e]=u.splitText(o,l);e.remove()}else{const[,e]=u.splitText(o,l);e.remove()}}else{const e=s(a);u.replace(e)}g=0}}}function R(e){const t=e.getStyle(),n=K(t);w.set(t,n)}function z(t,o){(i(t)?t.isCollapsed():e(t)||n(t))||N(280);const l=I(i(t)?t.style:e(t)?t.getStyle():t.getTextStyle()),r=Object.entries(o).reduce(((e,[n,o])=>("function"==typeof o?e[n]=o(l[n],t):null===o?delete e[n]:e[n]=o,e)),{...l}),s=F(r);i(t)||e(t)?t.setStyle(s):t.setTextStyle(s),w.set(s,r)}function A(e,t){if(i(e)&&e.isCollapsed()){z(e,t);const o=e.anchor.getNode();n(o)&&o.isEmpty()&&z(o,t)}L((e=>{z(e,t)}))}function L(t){const n=c();if(!n)return;const o=new Map;if(i(n))for(const e of f(n).getTextSlices())e&&o.set(e.caret.origin.getKey(),e.getSliceIndices());const l=n.getNodes();for(const n of l){if(!e(n)||!n.canHaveFormat())continue;const[l,s]=(r=n,o.get(r.getKey())||[0,r.getTextContentSize()]);if(s!==l)if(u(n)||0===l&&s===n.getTextContentSize())t(n);else{t(n.splitText(l,s)[0===l?0:1])}}var r;i(n)&&"text"===n.anchor.type&&"text"===n.focus.type&&n.anchor.key===n.focus.key&&M(n)}function M(e){if(e.isBackward()){const{anchor:t,focus:n}=e,{key:o,offset:l,type:r}=t;t.set(n.key,n.offset,n.type),n.set(o,l,r)}}function $(e,t){const n=e.getFormatType(),o=e.getIndent();n!==t.getFormatType()&&t.setFormat(n),o!==t.getIndent()&&t.setIndent(o)}function _(e,t,o=$){if(null===e)return;const l=e.getStartEndPoints(),r=new Map;let s=null;if(l){const[e,t]=l;s=g(),s.anchor.set(e.key,e.offset,e.type),s.focus.set(t.key,t.offset,t.type);const o=V(e.getNode(),a),i=V(t.getNode(),a);n(o)&&r.set(o.getKey(),o),n(i)&&r.set(i.getKey(),i)}for(const t of e.getNodes())n(t)&&a(t)&&r.set(t.getKey(),t);for(const[e,n]of r){const l=t();o(n,l),n.replace(l,!0),s&&(e===s.anchor.key&&s.anchor.set(l.getKey(),s.anchor.offset,s.anchor.type),e===s.focus.key&&s.focus.set(l.getKey(),s.focus.offset,s.focus.type))}s&&e.is(c())&&d(s)}function D(e){return e.getNode().isAttached()}function U(e){let t=e;for(;null!==t&&!p(t);){const e=t.getLatest(),n=t.getParent();0===e.getChildrenSize()&&t.remove(!0),t=n}}function j(e,t,n=null){const o=e.getStartEndPoints(),l=o?o[0]:null,r=e.getNodes(),s=r.length;if(null!==l&&(0===s||1===s&&"element"===l.type&&0===l.getNode().getChildrenSize())){const e="text"===l.type?l.getNode().getParentOrThrow():l.getNode(),o=e.getChildren();let r=t();return r.setFormat(e.getFormatType()),r.setIndent(e.getIndent()),o.forEach((e=>r.append(e))),n&&(r=n.append(r)),void e.replace(r)}let i=null,c=[];for(let o=0;o<s;o++){const l=r[o];p(l)?(H(e,c,c.length,t,n),c=[],i=l):null===i||null!==i&&h(l,i)?c.push(l):(H(e,c,c.length,t,n),c=[l])}H(e,c,c.length,t,n)}function H(e,t,o,l,s=null){if(0===t.length)return;const c=t[0],f=new Map,u=[];let g=n(c)?c:c.getParentOrThrow();g.isInline()&&(g=g.getParentOrThrow());let a=!1;for(;null!==g;){const e=g.getPreviousSibling();if(null!==e){g=e,a=!0;break}if(g=g.getParentOrThrow(),p(g))break}const h=new Set;for(let e=0;e<o;e++){const o=t[e];n(o)&&0===o.getChildrenSize()&&h.add(o.getKey())}const m=new Set;for(let e=0;e<o;e++){const o=t[e];let r=o.getParent();if(null!==r&&r.isInline()&&(r=r.getParent()),null!==r&&y(o)&&!m.has(o.getKey())){const e=r.getKey();if(void 0===f.get(e)){const t=l();t.setFormat(r.getFormatType()),t.setIndent(r.getIndent()),u.push(t),f.set(e,t),r.getChildren().forEach((e=>{t.append(e),m.add(e.getKey()),n(e)&&e.getChildrenKeys().forEach((e=>m.add(e)))})),U(r)}}else if(h.has(o.getKey())){n(o)||N(179);const e=l();e.setFormat(o.getFormatType()),e.setIndent(o.getIndent()),u.push(e),o.remove(!0)}}if(null!==s)for(let e=0;e<u.length;e++){const t=u[e];s.append(t)}let S=null;if(p(g))if(a)if(null!==s)g.insertAfter(s);else for(let e=u.length-1;e>=0;e--){const t=u[e];g.insertAfter(t)}else{const e=g.getFirstChild();if(n(e)&&(g=e),null===e)if(s)g.append(s);else for(let e=0;e<u.length;e++){const t=u[e];g.append(t),S=t}else if(null!==s)e.insertBefore(s);else for(let t=0;t<u.length;t++){const n=u[t];e.insertBefore(n),S=n}}else if(s)g.insertAfter(s);else for(let e=u.length-1;e>=0;e--){const t=u[e];g.insertAfter(t),S=t}const x=r();i(x)&&D(x.anchor)&&D(x.focus)?d(x.clone()):null!==S?S.selectEnd():e.dirty=!0}function W(e,t){const o=m(e.focus,t?"previous":"next");if(S(o))return!1;for(const e of x(o)){if(T(e))return!e.origin.isInline();if(!n(e.origin)){if(v(e.origin))return!0;break}}return!1}function X(e,t,n,o){e.modify(t?"extend":"move",n,o)}function q(e){const t=e.anchor.getNode();return"rtl"===(o(t)?t:t.getParentOrThrow()).getDirection()}function G(e,t,n){const o=q(e);X(e,t,n?!o:o,"character")}function J(e,t,n){const o=I(e.getStyle());return null!==o&&o[t]||n}function Q(t,n,o=""){let l=null;const r=t.getNodes(),s=t.anchor,c=t.focus,f=t.isBackward(),u=f?c.offset:s.offset,g=f?c.getNode():s.getNode();if(i(t)&&t.isCollapsed()&&""!==t.style){const e=I(t.style);if(null!==e&&n in e)return e[n]}for(let t=0;t<r.length;t++){const s=r[t];if((0===t||0!==u||!s.is(g))&&e(s)){const e=J(s,n,o);if(null===l)l=e;else if(l!==e){l="";break}}}return null===l?o:l}function V(e,t){let n=e;for(;null!==n&&null!==n.getParent()&&!t(n);)n=n.getParentOrThrow();return t(n)?n:null}const Y=O;export{R as $addNodeStyle,$ as $copyBlockFormatIndent,M as $ensureForwardRangeSelection,L as $forEachSelectedTextNode,Q as $getSelectionStyleValueForProperty,b as $isAtNodeEnd,q as $isParentElementRTL,X as $moveCaretSelection,G as $moveCharacter,A as $patchStyleText,_ as $setBlocksType,W as $shouldOverrideDefaultCharacterSelection,B as $sliceSelectedTextNodeContent,O as $trimTextContentFromAnchor,j as $wrapNodes,k as createDOMRange,E as createRectsFromDOMRange,F as getCSSFromStyleObject,I as getStyleObjectFromCSS,Y as trimTextContentFromAnchor};
