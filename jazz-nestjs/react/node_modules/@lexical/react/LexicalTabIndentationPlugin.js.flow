/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */

import type {LexicalEditor} from 'lexical';

declare export function registerTabIndentation(
  editor: LexicalEditor,
  maxIndent?: number,
): () => void;

type Props = $ReadOnly<{
  maxIndent?: number,
}>;

declare export function TabIndentationPlugin(props: Props): null;
