/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{useLexicalComposerContext as e}from"@lexical/react/LexicalComposerContext";import{useLayoutEffect as n,useEffect as t,useMemo as i,useState as r,useRef as o}from"react";const c="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?n:t;function u(e){return{initialValueFn:()=>e.isEditable(),subscribe:n=>e.registerEditableListener(n)}}function a(){return function(n){const[t]=e(),u=i((()=>n(t)),[t,n]),[a,l]=r((()=>u.initialValueFn())),d=o(a);return c((()=>{const{initialValueFn:e,subscribe:n}=u,t=e();return d.current!==t&&(d.current=t,l(t)),n((e=>{d.current=e,l(e)}))}),[u,n]),a}(u)}export{a as useLexicalEditable};
