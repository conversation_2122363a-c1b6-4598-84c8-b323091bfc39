/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("@lexical/react/LexicalComposerContext");function t(e){var t=Object.create(null);if(e)for(var r in e)t[r]=e[r];return t.default=e,t}var r=t(require("react"));exports.EditorRefPlugin=function({editorRef:t}){const[o]=e.useLexicalComposerContext();return r.useEffect((()=>{"function"==typeof t?t(o):"object"==typeof t&&(t.current=o)}),[o]),null};
