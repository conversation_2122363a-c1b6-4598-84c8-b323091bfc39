/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */

import type {InitialEditorStateType} from '@lexical/react/LexicalComposer';
import type {ExcludedProperties, ProviderAwareness} from '@lexical/yjs';
import type {Doc, RelativePosition} from 'yjs';

export interface Provider {
  connect(): void | Promise<void>;
  disconnect(): void;
  awareness: ProviderAwareness;
  on(type: 'sync', cb: (isSynced: boolean) => void): void;
  on(type: 'status', cb: ({status: string}) => void): void;
  // $FlowFixMe: temp
  on(type: 'update', cb: (any) => void): void;
  on(type: 'reload', cb: (doc: Doc) => void): void;
  off(type: 'sync', cb: (isSynced: boolean) => void): void;
  // $FlowFixMe: temp
  off(type: 'update', cb: (any) => void): void;
  off(type: 'status', cb: ({status: string}) => void): void;
  off(type: 'reload', cb: (doc: Doc) => void): void;
}

export type ProviderFactory = (
  id: string,
  yjsDocMap: Map<string, Doc>,
) => Provider;

export type CursorsContainerRef = {current: null | HTMLElement};

declare export function CollaborationPlugin(arg0: {
  id: string,
  providerFactory: (
    // eslint-disable-next-line no-shadow
    id: string,
    yjsDocMap: Map<string, Doc>,
  ) => Provider,
  shouldBootstrap: boolean,
  username?: string,
  cursorColor?: string,
  cursorsContainerRef?: CursorsContainerRef,
  initialEditorState?: InitialEditorStateType,
  excludedProperties?: ExcludedProperties,
}): React.Node;
