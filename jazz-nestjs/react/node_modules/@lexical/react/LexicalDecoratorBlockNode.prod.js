/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var r=require("lexical");class t extends r.DecoratorNode{constructor(r,t){super(t),this.__format=r||""}exportJSON(){return{...super.exportJSON(),format:this.__format||""}}updateFromJSON(r){return super.updateFromJSON(r).setFormat(r.format||"")}canIndent(){return!1}createDOM(){return document.createElement("div")}updateDOM(){return!1}setFormat(r){const t=this.getWritable();return t.__format=r,t}isInline(){return!1}}exports.$isDecoratorBlockNode=function(r){return r instanceof t},exports.DecoratorBlockNode=t;
