/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("@lexical/react/LexicalComposerContext"),t=require("@lexical/react/LexicalDecoratorBlockNode"),r=require("@lexical/react/useLexicalNodeSelection"),o=require("@lexical/utils"),i=require("lexical"),c=require("react"),l=require("react/jsx-runtime");exports.BlockWithAlignableContents=function({children:s,format:a,nodeKey:n,className:u}){const[N]=e.useLexicalComposerContext(),[C,d,m]=r.useLexicalNodeSelection(n),f=c.useRef(null),M=c.useCallback((e=>{const t=i.$getSelection();return C&&i.$isNodeSelection(t)&&(e.preventDefault(),t.getNodes().forEach((e=>{i.$isDecoratorNode(e)&&e.remove()}))),!1}),[C]);return c.useEffect((()=>o.mergeRegister(N.registerCommand(i.FORMAT_ELEMENT_COMMAND,(e=>{if(C){const r=i.$getSelection();if(i.$isNodeSelection(r)){const r=i.$getNodeByKey(n);t.$isDecoratorBlockNode(r)&&r.setFormat(e)}else if(i.$isRangeSelection(r)){const i=r.getNodes();for(const r of i)if(t.$isDecoratorBlockNode(r))r.setFormat(e);else{o.$getNearestBlockElementAncestorOrThrow(r).setFormat(e)}}return!0}return!1}),i.COMMAND_PRIORITY_LOW),N.registerCommand(i.CLICK_COMMAND,(e=>e.target===f.current&&(e.preventDefault(),e.shiftKey||m(),d(!C),!0)),i.COMMAND_PRIORITY_LOW),N.registerCommand(i.KEY_DELETE_COMMAND,M,i.COMMAND_PRIORITY_LOW),N.registerCommand(i.KEY_BACKSPACE_COMMAND,M,i.COMMAND_PRIORITY_LOW))),[m,N,C,n,M,d]),l.jsx("div",{className:[u.base,C?u.focus:null].filter(Boolean).join(" "),ref:f,style:{textAlign:a||void 0},children:s})};
