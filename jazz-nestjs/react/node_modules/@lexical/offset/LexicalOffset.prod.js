/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("lexical");function t(e,...t){const n=new URL("https://lexical.dev/docs/error"),s=new URLSearchParams;s.append("code",e);for(const e of t)s.append("v",e);throw n.search=s.toString(),Error(`Minified Lexical error #${e}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}class n{constructor(e,t,n=1){this._offsetMap=e,this._firstNode=t,this._blockOffsetSize=n}createSelectionFromOffsets(t,n,r){const l=this._firstNode;if(null===l)return null;let i=t,f=n,c=o(l,i,this._blockOffsetSize),u=o(l,f,this._blockOffsetSize);if(void 0!==r&&(i=s(i,c,r,this,this._blockOffsetSize),c=o(l,i,this._blockOffsetSize),f=s(f,u,r,this,this._blockOffsetSize),u=o(l,f,this._blockOffsetSize)),null===c||null===u)return null;let a=c.key,d=u.key;const p=e.$getNodeByKey(a),g=e.$getNodeByKey(d);if(null===p||null===g)return null;let h=0,y=0,x="element",v="element";if("text"===c.type){h=i-c.start,x="text";const t=p.getNextSibling();i!==f&&h===p.getTextContentSize()&&e.$isTextNode(t)&&(h=0,a=t.__key)}else"inline"===c.type&&(a=p.getParentOrThrow().getKey(),h=f>c.start?c.end:c.start);"text"===u.type?(y=f-u.start,v="text"):"inline"===u.type&&(d=g.getParentOrThrow().getKey(),y=f>u.start?u.end:u.start);const _=e.$createRangeSelection();return null===_?null:(_.anchor.set(a,h,x),_.focus.set(d,y,v),_)}getOffsetsFromSelection(e){const t=e.anchor,n=e.focus,s=this._offsetMap,o=t.offset,r=n.offset;let l=-1,i=-1;if("text"===t.type){const e=s.get(t.key);void 0!==e&&(l=e.start+o)}else{const e=t.getNode().getDescendantByIndex(o);if(null!==e){const t=s.get(e.getKey());if(void 0!==t){l=e.getIndexWithinParent()!==o?t.end:t.start}}}if("text"===n.type){const e=s.get(n.key);void 0!==e&&(i=e.start+n.offset)}else{const e=n.getNode().getDescendantByIndex(r);if(null!==e){const t=s.get(e.getKey());if(void 0!==t){i=e.getIndexWithinParent()!==r?t.end:t.start}}}return[l,i]}}function s(e,t,n,s,r){const l=n._offsetMap,i=s._offsetMap,f=new Set;let c=e,u=t;for(;null!==u;){const e=u.key,t=l.get(e),n=u.end-u.start;if(f.add(e),void 0===t)c+=n;else{const e=t.end-t.start;e!==n&&(c+=n-e)}const s=u.prev;if(null!==s){u=s;continue}let o=u.parent;for(;null!==o;){let e=o.prev;if(null!==e){const t=e.key,n=l.get(t),s=e.end-e.start;if(f.add(t),void 0===n)c+=s;else{const e=n.end-n.start;e!==s&&(c+=s-e)}e=e.prev}o=o.parent}break}const a=n._firstNode;if(null!==a){u=o(a,e,r);let t=!1;for(;null!==u;){if(!f.has(u.key)){t=!0;break}u=u.parent}if(!t)for(;null!==u;){const e=u.key;if(!f.has(e)){const t=i.get(e),n=u.end-u.start;if(void 0===t)c-=n;else{const e=t.end-t.start;n!==e&&(c+=e-n)}}u=u.prev}}return c}function o(e,t,n){let s=e;for(;null!==s;){if(t<s.end+("element"!==s.type||0===n?1:0)){const e=s.child;if(null!==e){s=e;continue}return s}const e=s.next;if(null===e)break;s=e}return null}function r(e,t,n,s,o,r){return{child:e,end:s,key:o,next:null,parent:r,prev:null,start:n,type:t}}function l(n,s,o,l,c,u){const a=l.get(s);void 0===a&&t(3);const d=n.offset;if(e.$isElementNode(a)){const e=f(a,l),t=0===e.length,p=t?null:i(n,e,null,l,c,u);n.prevIsBlock&&!t||(n.prevIsBlock=!0,n.offset+=u);const g=r(p,"element",d,d,s,o);null!==p&&(p.parent=g);const h=n.offset;return g.end=h,c.set(s,g),g}n.prevIsBlock=!1;const p=e.$isTextNode(a),g=p?a.__text.length:1,h=r(null,p?"text":"inline",d,n.offset+=g,s,o);return c.set(s,h),h}function i(e,t,n,s,o,r){let i=null,f=null;const c=t.length;for(let u=0;u<c;u++){const c=l(e,t[u],n,s,o,r);null===f?i=c:(c.prev=f,f.next=c),f=c}return i}function f(n,s){const o=[];let r=n.__first;for(;null!==r;){const n=null===s?e.$getNodeByKey(r):s.get(r);null==n&&t(174),o.push(r),r=n.__next}return o}const c=f;exports.$createChildrenArray=f,exports.$createOffsetView=function(e,t=1,s){const o=(s||e._pendingEditorState||e._editorState)._nodeMap,r=o.get("root"),l=new Map,c=i({offset:0,prevIsBlock:!1},f(r,o),null,o,l,t);return new n(l,c,t)},exports.OffsetView=n,exports.createChildrenArray=c;
