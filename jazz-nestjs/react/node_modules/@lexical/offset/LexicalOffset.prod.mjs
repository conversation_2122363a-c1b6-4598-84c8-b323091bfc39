/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{$getNodeByKey as t,$isTextNode as e,$createRangeSelection as n,$isElementNode as l}from"lexical";function o(t,...e){const n=new URL("https://lexical.dev/docs/error"),l=new URLSearchParams;l.append("code",t);for(const t of e)l.append("v",t);throw n.search=l.toString(),Error(`Minified Lexical error #${t}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}class s{constructor(t,e,n=1){this._offsetMap=t,this._firstNode=e,this._blockOffsetSize=n}createSelectionFromOffsets(l,o,s){const f=this._firstNode;if(null===f)return null;let c=l,u=o,a=i(f,c,this._blockOffsetSize),d=i(f,u,this._blockOffsetSize);if(void 0!==s&&(c=r(c,a,s,this,this._blockOffsetSize),a=i(f,c,this._blockOffsetSize),u=r(u,d,s,this,this._blockOffsetSize),d=i(f,u,this._blockOffsetSize)),null===a||null===d)return null;let p=a.key,g=d.key;const h=t(p),_=t(g);if(null===h||null===_)return null;let k=0,v=0,y="element",x="element";if("text"===a.type){k=c-a.start,y="text";const t=h.getNextSibling();c!==u&&k===h.getTextContentSize()&&e(t)&&(k=0,p=t.__key)}else"inline"===a.type&&(p=h.getParentOrThrow().getKey(),k=u>a.start?a.end:a.start);"text"===d.type?(v=u-d.start,x="text"):"inline"===d.type&&(g=_.getParentOrThrow().getKey(),v=u>d.start?d.end:d.start);const S=n();return null===S?null:(S.anchor.set(p,k,y),S.focus.set(g,v,x),S)}getOffsetsFromSelection(t){const e=t.anchor,n=t.focus,l=this._offsetMap,o=e.offset,s=n.offset;let r=-1,i=-1;if("text"===e.type){const t=l.get(e.key);void 0!==t&&(r=t.start+o)}else{const t=e.getNode().getDescendantByIndex(o);if(null!==t){const e=l.get(t.getKey());if(void 0!==e){r=t.getIndexWithinParent()!==o?e.end:e.start}}}if("text"===n.type){const t=l.get(n.key);void 0!==t&&(i=t.start+n.offset)}else{const t=n.getNode().getDescendantByIndex(s);if(null!==t){const e=l.get(t.getKey());if(void 0!==e){i=t.getIndexWithinParent()!==s?e.end:e.start}}}return[r,i]}}function r(t,e,n,l,o){const s=n._offsetMap,r=l._offsetMap,f=new Set;let c=t,u=e;for(;null!==u;){const t=u.key,e=s.get(t),n=u.end-u.start;if(f.add(t),void 0===e)c+=n;else{const t=e.end-e.start;t!==n&&(c+=n-t)}const l=u.prev;if(null!==l){u=l;continue}let o=u.parent;for(;null!==o;){let t=o.prev;if(null!==t){const e=t.key,n=s.get(e),l=t.end-t.start;if(f.add(e),void 0===n)c+=l;else{const t=n.end-n.start;t!==l&&(c+=l-t)}t=t.prev}o=o.parent}break}const a=n._firstNode;if(null!==a){u=i(a,t,o);let e=!1;for(;null!==u;){if(!f.has(u.key)){e=!0;break}u=u.parent}if(!e)for(;null!==u;){const t=u.key;if(!f.has(t)){const e=r.get(t),n=u.end-u.start;if(void 0===e)c-=n;else{const t=e.end-e.start;n!==t&&(c+=t-n)}}u=u.prev}}return c}function i(t,e,n){let l=t;for(;null!==l;){if(e<l.end+("element"!==l.type||0===n?1:0)){const t=l.child;if(null!==t){l=t;continue}return l}const t=l.next;if(null===t)break;l=t}return null}function f(t,e,n,l,o,s){return{child:t,end:l,key:o,next:null,parent:s,prev:null,start:n,type:e}}function c(t,n,s,r,i,c){const d=r.get(n);void 0===d&&o(3);const p=t.offset;if(l(d)){const e=a(d,r),l=0===e.length,o=l?null:u(t,e,null,r,i,c);t.prevIsBlock&&!l||(t.prevIsBlock=!0,t.offset+=c);const g=f(o,"element",p,p,n,s);null!==o&&(o.parent=g);const h=t.offset;return g.end=h,i.set(n,g),g}t.prevIsBlock=!1;const g=e(d),h=g?d.__text.length:1,_=f(null,g?"text":"inline",p,t.offset+=h,n,s);return i.set(n,_),_}function u(t,e,n,l,o,s){let r=null,i=null;const f=e.length;for(let u=0;u<f;u++){const f=c(t,e[u],n,l,o,s);null===i?r=f:(f.prev=i,i.next=f),i=f}return r}function a(e,n){const l=[];let s=e.__first;for(;null!==s;){const e=null===n?t(s):n.get(s);null==e&&o(174),l.push(s),s=e.__next}return l}const d=a;function p(t,e=1,n){const l=(n||t._pendingEditorState||t._editorState)._nodeMap,o=l.get("root"),r=new Map,i=u({offset:0,prevIsBlock:!1},a(o,l),null,l,r,e);return new s(r,i,e)}export{a as $createChildrenArray,p as $createOffsetView,s as OffsetView,d as createChildrenArray};
