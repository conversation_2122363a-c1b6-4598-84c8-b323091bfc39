{"name": "@lexical/dragon", "description": "This package contains compatibility with Dragon NaturallySpeaking accessibility tools.", "keywords": ["lexical", "editor", "rich-text", "dragon", "accessibility"], "license": "MIT", "version": "0.27.2", "main": "LexicalDragon.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-dragon"}, "module": "LexicalDragon.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalDragon.dev.mjs", "production": "./LexicalDragon.prod.mjs", "node": "./LexicalDragon.node.mjs", "default": "./LexicalDragon.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalDragon.dev.js", "production": "./LexicalDragon.prod.js", "default": "./LexicalDragon.js"}}}, "dependencies": {"lexical": "0.27.2"}}