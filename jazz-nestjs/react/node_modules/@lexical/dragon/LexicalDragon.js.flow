/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */
import type {LexicalEditor} from 'lexical';
import {$getSelection, $isRangeSelection, $isTextNode} from 'lexical';
declare export function registerDragonSupport(
  editor: LexicalEditor,
): () => void;
