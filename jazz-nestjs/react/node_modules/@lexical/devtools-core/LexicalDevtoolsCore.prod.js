/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("@lexical/html"),t=require("@lexical/link"),n=require("@lexical/mark"),r=require("@lexical/table"),o=require("lexical"),i=require("react"),s=require("react/jsx-runtime");const l=Object.freeze({"\t":"\\t","\n":"\\n"}),a=new RegExp(Object.keys(l).join("|"),"g"),c=Object.freeze({ancestorHasNextSibling:"|",ancestorIsLastChild:" ",hasNextSibling:"├",isLastChild:"└",selectedChar:"^",selectedLine:">"}),u=[e=>e.hasFormat("bold")&&"Bold",e=>e.hasFormat("code")&&"Code",e=>e.hasFormat("italic")&&"Italic",e=>e.hasFormat("strikethrough")&&"Strikethrough",e=>e.hasFormat("subscript")&&"Subscript",e=>e.hasFormat("superscript")&&"Superscript",e=>e.hasFormat("underline")&&"Underline",e=>e.hasFormat("highlight")&&"Highlight"],f=[e=>e.hasTextFormat("bold")&&"Bold",e=>e.hasTextFormat("code")&&"Code",e=>e.hasTextFormat("italic")&&"Italic",e=>e.hasTextFormat("strikethrough")&&"Strikethrough",e=>e.hasTextFormat("subscript")&&"Subscript",e=>e.hasTextFormat("superscript")&&"Superscript",e=>e.hasTextFormat("underline")&&"Underline",e=>e.hasTextFormat("highlight")&&"Highlight"],d=[e=>e.isDirectionless()&&"Directionless",e=>e.isUnmergeable()&&"Unmergeable"],h=[e=>e.isToken()&&"Token",e=>e.isSegmented()&&"Segmented"];function g(e,t,n=[]){const r=e.getChildren(),i=r.length;r.forEach(((e,r)=>{t(e,n.concat(r===i-1?c.isLastChild:c.hasNextSibling)),o.$isElementNode(e)&&g(e,t,n.concat(r===i-1?c.ancestorIsLastChild:c.ancestorHasNextSibling))}))}function m(e,t=!1){const n=Object.entries(l).reduce(((e,[t,n])=>e.replace(new RegExp(t,"g"),String(n))),e);return t?n.replace(/[^\s]/g,"*"):n}function p(e){let t=d.map((t=>t(e))).filter(Boolean).join(", ").toLocaleLowerCase();return""!==t&&(t="detail: "+t),t}function y(e){let t=h.map((t=>t(e))).filter(Boolean).join(", ").toLocaleLowerCase();return""!==t&&(t="mode: "+t),t}function x(e){let t=u.map((t=>t(e))).filter(Boolean).join(", ").toLocaleLowerCase();return""!==t&&(t="format: "+t),t}function $(e){let t=e.getTarget();return null!=t&&(t="target: "+t),t}function S(e){let t=e.getRel();return null!=t&&(t="rel: "+t),t}function C(e){let t=e.getTitle();return null!=t&&(t="title: "+t),t}function N(e,t){const n=new Array(1+t++).join("  "),r=new Array(t-1).join("  ");let o;for(let i=0;i<e.children.length;i++)o=document.createTextNode("\n"+n),e.insertBefore(o,e.children[i]),N(e.children[i],t),e.lastElementChild===e.children[i]&&(o=document.createTextNode("\n"+r),e.appendChild(o));return e}const b=i.forwardRef((function({treeTypeButtonClassName:e,timeTravelButtonClassName:t,timeTravelPanelSliderClassName:n,timeTravelPanelButtonClassName:r,viewClassName:o,timeTravelPanelClassName:l,editorState:a,setEditorState:c,setEditorReadOnly:u,generateContent:f},d){const[h,g]=i.useState([]),[m,p]=i.useState(""),[y,x]=i.useState(!1),[$,S]=i.useState(!1),C=i.useRef(0),N=i.useRef(null),[b,T]=i.useState(!1),[j,k]=i.useState(!1),[L,v]=i.useState(!1),E=i.useRef(),F=i.useRef(0),R=i.useCallback((e=>{const t=++F.current;f(e).then((e=>{t===F.current&&p(e)})).catch((e=>{t===F.current&&p(`Error rendering tree: ${e.message}\n\nStack:\n${e.stack}`)}))}),[f]);i.useEffect((()=>{!L&&a._nodeMap.size>1e3&&(k(!0),!L)||E.current!==a&&(E.current=a,R($),y||g((e=>[...e,[Date.now(),a]])))}),[a,R,$,L,y]);const w=h.length;i.useEffect((()=>{if(b){let e;const t=()=>{const n=C.current;if(n===w-1)return void T(!1);const r=h[n][0],o=h[n+1][0];e=setTimeout((()=>{C.current++;const e=C.current,n=N.current;null!==n&&(n.value=String(e)),c(h[e][1]),t()}),o-r)};return t(),()=>{clearTimeout(e)}}}),[h,b,w,c]);return s.jsxs("div",{className:o,children:[!L&&j?s.jsxs("div",{style:{padding:20},children:[s.jsx("span",{style:{marginRight:20},children:"Detected large EditorState, this can impact debugging performance."}),s.jsx("button",{onClick:()=>{v(!0)},style:{background:"transparent",border:"1px solid white",color:"white",cursor:"pointer",padding:5},children:"Show full tree"})]}):null,L?null:s.jsx("button",{onClick:()=>(R(!$),void S(!$)),className:e,type:"button",children:$?"Tree":"Export DOM"}),!y&&(L||!j)&&w>2&&s.jsx("button",{onClick:()=>{u(!0),C.current=w-1,x(!0)},className:t,type:"button",children:"Time Travel"}),(L||!j)&&s.jsx("pre",{ref:d,children:m}),y&&(L||!j)&&s.jsxs("div",{className:l,children:[s.jsx("button",{className:r,onClick:()=>{C.current===w-1&&(C.current=1),T(!b)},type:"button",children:b?"Pause":"Play"}),s.jsx("input",{className:n,ref:N,onChange:e=>{const t=Number(e.target.value),n=h[t];n&&(C.current=t,c(n[1]))},type:"range",min:"1",max:w-1}),s.jsx("button",{className:r,onClick:()=>{u(!1);const e=h.length-1,t=h[e];c(t[1]);const n=N.current;null!==n&&(n.value=String(e)),x(!1),T(!1)},type:"button",children:"Exit"})]})]})}));function T(e,t){const n=new Set;let r=0;for(const[i]of e._commands)n.add(e.registerCommand(i,(e=>(t((t=>{r+=1;const n=[...t];return n.push({index:r,payload:e,type:i.type?i.type:"UNKNOWN"}),n.length>10&&n.shift(),n})),!1)),o.COMMAND_PRIORITY_CRITICAL));return()=>n.forEach((e=>e()))}exports.TreeView=b,exports.generateContent=function(i,s,l,u,d=!1){const h=i.getEditorState(),b=i._config,T=i._compositionKey,j=i._editable;if(l){let t="";return h.read((()=>{t=function(e){const t=document.createElement("div");return t.innerHTML=e.trim(),N(t,0).innerHTML}(e.$generateHtmlFromNodes(i))})),t}let k=" root\n";const L=h.read((()=>{const e=o.$getSelection();return g(o.$getRoot(),((r,i)=>{const s=`(${r.getKey()})`,l=r.getType()||"",h=r.isSelected();k+=`${h?c.selectedLine:" "} ${i.join(" ")} ${s} ${l} ${function(e,r,i=!1){const s=r?r(e,i):void 0;if(void 0!==s&&s.length>0)return s;if(o.$isTextNode(e)){const t=e.getTextContent(),n=0===t.length?"(empty)":`"${m(t,i)}"`,r=function(e){return[x(e),p(e),y(e)].filter(Boolean).join(", ")}(e);return[n,0!==r.length?`{ ${r} }`:null].filter(Boolean).join(" ").trim()}if(t.$isLinkNode(e)){const t=e.getURL(),n=0===t.length?"(empty)":`"${m(t,i)}"`,r=function(e){return[$(e),S(e),C(e)].filter(Boolean).join(", ")}(e);return[n,0!==r.length?`{ ${r} }`:null].filter(Boolean).join(" ").trim()}if(n.$isMarkNode(e))return`ids: [ ${e.getIDs().join(", ")} ]`;if(o.$isParagraphNode(e)){const t=function(e){let t=f.map((t=>t(e))).filter(Boolean).join(", ").toLocaleLowerCase();""!==t&&(t="format: "+t);return t}(e);let n=""!==t?`{ ${t} }`:"";return n+=e.__style?`(${e.__style})`:"",n}return""}(r,u,d)}\n`,k+=function({indent:e,isSelected:t,node:n,nodeKeyDisplay:r,selection:i,typeDisplay:s}){if(!o.$isTextNode(n)||!o.$isRangeSelection(i)||!t||o.$isElementNode(n))return"";const l=i.anchor,u=i.focus;if(""===n.getTextContent()||l.getNode()===i.focus.getNode()&&l.offset===u.offset)return"";const[f,d]=function(e,t){const n=t.getStartEndPoints();if(o.$isNodeSelection(t)||null===n)return[-1,-1];const[r,i]=n,s=e.getTextContent(),l=s.length;let c=-1,u=-1;if("text"===r.type&&"text"===i.type){const t=r.getNode(),n=i.getNode();t===n&&e===t&&r.offset!==i.offset?[c,u]=r.offset<i.offset?[r.offset,i.offset]:[i.offset,r.offset]:[c,u]=e===t?t.isBefore(n)?[r.offset,l]:[0,r.offset]:e===n?n.isBefore(t)?[i.offset,l]:[0,i.offset]:[0,l]}const f=(s.slice(0,c).match(a)||[]).length,d=(s.slice(c,u).match(a)||[]).length;return[c+f,u+f+d]}(n,i);if(f===d)return"";const h=e[e.length-1]===c.hasNextSibling?c.ancestorHasNextSibling:c.ancestorIsLastChild,g=[...e.slice(0,e.length-1),h],m=Array(f+1).fill(" "),p=Array(d-f).fill(c.selectedChar),y=s.length+2,x=Array(r.length+y).fill(" ");return[c.selectedLine,g.join(" "),[...x,...m,...p].join("")].join(" ")+"\n"}({indent:i,isSelected:h,node:r,nodeKeyDisplay:s,selection:e,typeDisplay:l})})),null===e?": null":o.$isRangeSelection(e)?function(e){let t="";const n=x(e);t+=`: range ${""!==n?`{ ${n} }`:""} ${""!==e.style?`{ style: ${e.style} } `:""}`;const r=e.anchor,o=e.focus,i=r.offset,s=o.offset;return t+=`\n  ├ anchor { key: ${r.key}, offset: ${null===i?"null":i}, type: ${r.type} }`,t+=`\n  └ focus { key: ${o.key}, offset: ${null===s?"null":s}, type: ${o.type} }`,t}(e):r.$isTableSelection(e)?function(e){return`: table\n  └ { table: ${e.tableKey}, anchorCell: ${e.anchor.key}, focusCell: ${e.focus.key} }`}(e):function(e){if(!o.$isNodeSelection(e))return"";return`: node\n  └ [${Array.from(e._nodes).join(", ")}]`}(e)}));if(k+="\n selection"+L,k+="\n\n commands:",s.length)for(const{index:e,type:t,payload:n}of s)k+=`\n  └ ${e}. { type: ${t}, payload: ${n instanceof Event?n.constructor.name:n} }`;else k+="\n  └ None dispatched.";const{version:v}=i.constructor;return k+=`\n\n editor${v?` (v${v})`:""}:`,k+=`\n  └ namespace ${b.namespace}`,null!==T&&(k+=`\n  └ compositionKey ${T}`),k+=`\n  └ editable ${String(j)}`,k},exports.registerLexicalCommandLogger=T,exports.useLexicalCommandsLog=function(e){const[t,n]=i.useState([]);return i.useEffect((()=>T(e,n)),[e]),i.useMemo((()=>t),[t])};
