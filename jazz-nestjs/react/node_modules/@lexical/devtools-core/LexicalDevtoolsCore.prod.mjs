/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{$generateHtmlFromNodes as e}from"@lexical/html";import{$isLinkNode as t}from"@lexical/link";import{$isMarkNode as n}from"@lexical/mark";import{$isTableSelection as r}from"@lexical/table";import{$getSelection as o,$getRoot as i,$isRangeSelection as l,$isNodeSelection as s,$isElementNode as a,$isTextNode as c,$isParagraphNode as u,COMMAND_PRIORITY_CRITICAL as f}from"lexical";import{forwardRef as d,useState as h,useRef as m,useCallback as g,useEffect as p,useMemo as y}from"react";import{jsxs as b,jsx as $}from"react/jsx-runtime";const x=Object.freeze({"\t":"\\t","\n":"\\n"}),C=new RegExp(Object.keys(x).join("|"),"g"),N=Object.freeze({ancestorHasNextSibling:"|",ancestorIsLastChild:" ",hasNextSibling:"├",isLastChild:"└",selectedChar:"^",selectedLine:">"}),S=[e=>e.hasFormat("bold")&&"Bold",e=>e.hasFormat("code")&&"Code",e=>e.hasFormat("italic")&&"Italic",e=>e.hasFormat("strikethrough")&&"Strikethrough",e=>e.hasFormat("subscript")&&"Subscript",e=>e.hasFormat("superscript")&&"Superscript",e=>e.hasFormat("underline")&&"Underline",e=>e.hasFormat("highlight")&&"Highlight"],T=[e=>e.hasTextFormat("bold")&&"Bold",e=>e.hasTextFormat("code")&&"Code",e=>e.hasTextFormat("italic")&&"Italic",e=>e.hasTextFormat("strikethrough")&&"Strikethrough",e=>e.hasTextFormat("subscript")&&"Subscript",e=>e.hasTextFormat("superscript")&&"Superscript",e=>e.hasTextFormat("underline")&&"Underline",e=>e.hasTextFormat("highlight")&&"Highlight"],k=[e=>e.isDirectionless()&&"Directionless",e=>e.isUnmergeable()&&"Unmergeable"],j=[e=>e.isToken()&&"Token",e=>e.isSegmented()&&"Segmented"];function v(f,d,h,m,g=!1){const p=f.getEditorState(),y=f._config,b=f._compositionKey,$=f._editable;if(h){let t="";return p.read((()=>{t=function(e){const t=document.createElement("div");return t.innerHTML=e.trim(),K(t,0).innerHTML}(e(f))})),t}let x=" root\n";const S=p.read((()=>{const e=o();return L(i(),((r,o)=>{const i=`(${r.getKey()})`,f=r.getType()||"",d=r.isSelected();x+=`${d?N.selectedLine:" "} ${o.join(" ")} ${i} ${f} ${function(e,r,o=!1){const i=r?r(e,o):void 0;if(void 0!==i&&i.length>0)return i;if(c(e)){const t=e.getTextContent(),n=0===t.length?"(empty)":`"${B(t,o)}"`,r=function(e){return[w(e),F(e),E(e)].filter(Boolean).join(", ")}(e);return[n,0!==r.length?`{ ${r} }`:null].filter(Boolean).join(" ").trim()}if(t(e)){const t=e.getURL(),n=0===t.length?"(empty)":`"${B(t,o)}"`,r=function(e){return[D(e),_(e),H(e)].filter(Boolean).join(", ")}(e);return[n,0!==r.length?`{ ${r} }`:null].filter(Boolean).join(" ").trim()}if(n(e))return`ids: [ ${e.getIDs().join(", ")} ]`;if(u(e)){const t=function(e){let t=T.map((t=>t(e))).filter(Boolean).join(", ").toLocaleLowerCase();""!==t&&(t="format: "+t);return t}(e);let n=""!==t?`{ ${t} }`:"";return n+=e.__style?`(${e.__style})`:"",n}return""}(r,m,g)}\n`,x+=function({indent:e,isSelected:t,node:n,nodeKeyDisplay:r,selection:o,typeDisplay:i}){if(!c(n)||!l(o)||!t||a(n))return"";const u=o.anchor,f=o.focus;if(""===n.getTextContent()||u.getNode()===o.focus.getNode()&&u.offset===f.offset)return"";const[d,h]=function(e,t){const n=t.getStartEndPoints();if(s(t)||null===n)return[-1,-1];const[r,o]=n,i=e.getTextContent(),l=i.length;let a=-1,c=-1;if("text"===r.type&&"text"===o.type){const t=r.getNode(),n=o.getNode();t===n&&e===t&&r.offset!==o.offset?[a,c]=r.offset<o.offset?[r.offset,o.offset]:[o.offset,r.offset]:[a,c]=e===t?t.isBefore(n)?[r.offset,l]:[0,r.offset]:e===n?n.isBefore(t)?[o.offset,l]:[0,o.offset]:[0,l]}const u=(i.slice(0,a).match(C)||[]).length,f=(i.slice(a,c).match(C)||[]).length;return[a+u,c+u+f]}(n,o);if(d===h)return"";const m=e[e.length-1]===N.hasNextSibling?N.ancestorHasNextSibling:N.ancestorIsLastChild,g=[...e.slice(0,e.length-1),m],p=Array(d+1).fill(" "),y=Array(h-d).fill(N.selectedChar),b=i.length+2,$=Array(r.length+b).fill(" ");return[N.selectedLine,g.join(" "),[...$,...p,...y].join("")].join(" ")+"\n"}({indent:o,isSelected:d,node:r,nodeKeyDisplay:i,selection:e,typeDisplay:f})})),null===e?": null":l(e)?function(e){let t="";const n=w(e);t+=`: range ${""!==n?`{ ${n} }`:""} ${""!==e.style?`{ style: ${e.style} } `:""}`;const r=e.anchor,o=e.focus,i=r.offset,l=o.offset;return t+=`\n  ├ anchor { key: ${r.key}, offset: ${null===i?"null":i}, type: ${r.type} }`,t+=`\n  └ focus { key: ${o.key}, offset: ${null===l?"null":l}, type: ${o.type} }`,t}(e):r(e)?function(e){return`: table\n  └ { table: ${e.tableKey}, anchorCell: ${e.anchor.key}, focusCell: ${e.focus.key} }`}(e):function(e){if(!s(e))return"";return`: node\n  └ [${Array.from(e._nodes).join(", ")}]`}(e)}));if(x+="\n selection"+S,x+="\n\n commands:",d.length)for(const{index:e,type:t,payload:n}of d)x+=`\n  └ ${e}. { type: ${t}, payload: ${n instanceof Event?n.constructor.name:n} }`;else x+="\n  └ None dispatched.";const{version:k}=f.constructor;return x+=`\n\n editor${k?` (v${k})`:""}:`,x+=`\n  └ namespace ${y.namespace}`,null!==b&&(x+=`\n  └ compositionKey ${b}`),x+=`\n  └ editable ${String($)}`,x}function L(e,t,n=[]){const r=e.getChildren(),o=r.length;r.forEach(((e,r)=>{t(e,n.concat(r===o-1?N.isLastChild:N.hasNextSibling)),a(e)&&L(e,t,n.concat(r===o-1?N.ancestorIsLastChild:N.ancestorHasNextSibling))}))}function B(e,t=!1){const n=Object.entries(x).reduce(((e,[t,n])=>e.replace(new RegExp(t,"g"),String(n))),e);return t?n.replace(/[^\s]/g,"*"):n}function F(e){let t=k.map((t=>t(e))).filter(Boolean).join(", ").toLocaleLowerCase();return""!==t&&(t="detail: "+t),t}function E(e){let t=j.map((t=>t(e))).filter(Boolean).join(", ").toLocaleLowerCase();return""!==t&&(t="mode: "+t),t}function w(e){let t=S.map((t=>t(e))).filter(Boolean).join(", ").toLocaleLowerCase();return""!==t&&(t="format: "+t),t}function D(e){let t=e.getTarget();return null!=t&&(t="target: "+t),t}function _(e){let t=e.getRel();return null!=t&&(t="rel: "+t),t}function H(e){let t=e.getTitle();return null!=t&&(t="title: "+t),t}function K(e,t){const n=new Array(1+t++).join("  "),r=new Array(t-1).join("  ");let o;for(let i=0;i<e.children.length;i++)o=document.createTextNode("\n"+n),e.insertBefore(o,e.children[i]),K(e.children[i],t),e.lastElementChild===e.children[i]&&(o=document.createTextNode("\n"+r),e.appendChild(o));return e}const O=d((function({treeTypeButtonClassName:e,timeTravelButtonClassName:t,timeTravelPanelSliderClassName:n,timeTravelPanelButtonClassName:r,viewClassName:o,timeTravelPanelClassName:i,editorState:l,setEditorState:s,setEditorReadOnly:a,generateContent:c},u){const[f,d]=h([]),[y,x]=h(""),[C,N]=h(!1),[S,T]=h(!1),k=m(0),j=m(null),[v,L]=h(!1),[B,F]=h(!1),[E,w]=h(!1),D=m(),_=m(0),H=g((e=>{const t=++_.current;c(e).then((e=>{t===_.current&&x(e)})).catch((e=>{t===_.current&&x(`Error rendering tree: ${e.message}\n\nStack:\n${e.stack}`)}))}),[c]);p((()=>{!E&&l._nodeMap.size>1e3&&(F(!0),!E)||D.current!==l&&(D.current=l,H(S),C||d((e=>[...e,[Date.now(),l]])))}),[l,H,S,E,C]);const K=f.length;p((()=>{if(v){let e;const t=()=>{const n=k.current;if(n===K-1)return void L(!1);const r=f[n][0],o=f[n+1][0];e=setTimeout((()=>{k.current++;const e=k.current,n=j.current;null!==n&&(n.value=String(e)),s(f[e][1]),t()}),o-r)};return t(),()=>{clearTimeout(e)}}}),[f,v,K,s]);return b("div",{className:o,children:[!E&&B?b("div",{style:{padding:20},children:[$("span",{style:{marginRight:20},children:"Detected large EditorState, this can impact debugging performance."}),$("button",{onClick:()=>{w(!0)},style:{background:"transparent",border:"1px solid white",color:"white",cursor:"pointer",padding:5},children:"Show full tree"})]}):null,E?null:$("button",{onClick:()=>(H(!S),void T(!S)),className:e,type:"button",children:S?"Tree":"Export DOM"}),!C&&(E||!B)&&K>2&&$("button",{onClick:()=>{a(!0),k.current=K-1,N(!0)},className:t,type:"button",children:"Time Travel"}),(E||!B)&&$("pre",{ref:u,children:y}),C&&(E||!B)&&b("div",{className:i,children:[$("button",{className:r,onClick:()=>{k.current===K-1&&(k.current=1),L(!v)},type:"button",children:v?"Pause":"Play"}),$("input",{className:n,ref:j,onChange:e=>{const t=Number(e.target.value),n=f[t];n&&(k.current=t,s(n[1]))},type:"range",min:"1",max:K-1}),$("button",{className:r,onClick:()=>{a(!1);const e=f.length-1,t=f[e];s(t[1]);const n=j.current;null!==n&&(n.value=String(e)),N(!1),L(!1)},type:"button",children:"Exit"})]})]})}));function A(e,t){const n=new Set;let r=0;for(const[o]of e._commands)n.add(e.registerCommand(o,(e=>(t((t=>{r+=1;const n=[...t];return n.push({index:r,payload:e,type:o.type?o.type:"UNKNOWN"}),n.length>10&&n.shift(),n})),!1)),f));return()=>n.forEach((e=>e()))}function I(e){const[t,n]=h([]);return p((()=>A(e,n)),[e]),y((()=>t),[t])}export{O as TreeView,v as generateContent,A as registerLexicalCommandLogger,I as useLexicalCommandsLog};
