{"name": "@lexical/devtools-core", "description": "This package contains tools necessary to debug and develop Lexical.", "keywords": ["lexical", "editor", "rich-text", "utils"], "license": "MIT", "version": "0.27.2", "main": "LexicalDevtoolsCore.js", "types": "index.d.ts", "dependencies": {"@lexical/html": "0.27.2", "@lexical/link": "0.27.2", "@lexical/mark": "0.27.2", "@lexical/table": "0.27.2", "@lexical/utils": "0.27.2", "lexical": "0.27.2"}, "peerDependencies": {"react": ">=17.x", "react-dom": ">=17.x"}, "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-devtools-core"}, "module": "LexicalDevtoolsCore.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalDevtoolsCore.dev.mjs", "production": "./LexicalDevtoolsCore.prod.mjs", "node": "./LexicalDevtoolsCore.node.mjs", "default": "./LexicalDevtoolsCore.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalDevtoolsCore.dev.js", "production": "./LexicalDevtoolsCore.prod.js", "default": "./LexicalDevtoolsCore.js"}}}}