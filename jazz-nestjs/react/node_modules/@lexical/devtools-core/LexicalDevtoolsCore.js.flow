/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */

/**
 * LexicalDevtoolsCore
 */

import type {
  LexicalCommand,
  LexicalEditor,
  LexicalNode,
} from 'lexical';

export type LexicalCommandLog = $ReadOnlyArray<
  {index: number} | LexicalCommand<mixed> | {payload: mixed}
 >;
export type CustomPrintNodeFn = (node: LexicalNode, obfuscateText?: boolean) => string;

declare export function generateContent(
  editor: LexicalEditor,
  commandsLog: $ReadOnlyArray<LexicalCommandLog>,
  exportDOM: boolean,
  customPrintNode?: CustomPrintNodeFn,
  obfuscateText?: boolean,
): string;

declare export function registerLexicalCommandLogger(
  editor: LexicalEditor,
  setLoggedCommands: (
    v: (oldValue: LexicalCommandLog) => LexicalCommandLog,
  ) => void,
): () => void;

declare export function useLexicalCommandsLog(
  editor: LexicalEditor,
): LexicalCommandLog;
