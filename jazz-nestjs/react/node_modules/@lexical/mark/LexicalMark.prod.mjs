/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{ElementNode as t,$isRangeSelection as e,$applyNodeReplacement as r,$createRangeSelection as n,$isElementNode as s,$isTextNode as i,$isDecoratorNode as o}from"lexical";import{addClassNamesToElement as c,removeClassNamesFromElement as u}from"@lexical/utils";const l=[];class f extends t{static getType(){return"mark"}static clone(t){return new f(t.__ids,t.__key)}static importDOM(){return null}static importJSON(t){return a().updateFromJSON(t)}updateFromJSON(t){return super.updateFromJSON(t).setIDs(t.ids)}exportJSON(){return{...super.exportJSON(),ids:this.getIDs()}}constructor(t=l,e){super(e),this.__ids=t}createDOM(t){const e=document.createElement("mark");return c(e,t.theme.mark),this.__ids.length>1&&c(e,t.theme.markOverlap),e}updateDOM(t,e,r){const n=t.__ids,s=this.__ids,i=n.length,o=s.length,l=r.theme.markOverlap;return i!==o&&(1===i?2===o&&c(e,l):1===o&&u(e,l)),!1}hasID(t){return this.getIDs().includes(t)}getIDs(){return Array.from(this.getLatest().__ids)}setIDs(t){const e=this.getWritable();return e.__ids=t,e}addID(t){const e=this.getWritable();return e.__ids.includes(t)?e:e.setIDs([...e.__ids,t])}deleteID(t){const e=this.getWritable(),r=e.__ids.indexOf(t);if(-1===r)return e;const n=Array.from(e.__ids);return n.splice(r,1),e.setIDs(n)}insertNewAfter(t,e=!0){const r=a(this.__ids);return this.insertAfter(r,e),r}canInsertTextBefore(){return!1}canInsertTextAfter(){return!1}canBeEmpty(){return!1}isInline(){return!0}extractWithChild(t,r,n){if(!e(r)||"html"===n)return!1;const s=r.anchor,i=r.focus,o=s.getNode(),c=i.getNode(),u=r.isBackward()?s.offset-i.offset:i.offset-s.offset;return this.isParentOf(o)&&this.isParentOf(c)&&this.getTextContent().length===u}excludeFromCopy(t){return"clone"!==t}}function a(t=l){return r(new f(t))}function d(t){return t instanceof f}function h(t){const e=t.getChildren();let r=null;for(let n=0;n<e.length;n++){const s=e[n];null===r?t.insertBefore(s):r.insertAfter(s),r=s}t.remove()}function _(t,e,r,c){const u=n(),[l,f]=t.isBackward()?[t.focus,t.anchor]:[t.anchor,t.focus];let h,_;u.anchor.set(l.key,l.offset,l.type),u.focus.set(f.key,f.offset,f.type);const m=u.extract();for(const t of m){if(s(_)&&_.isParentOf(t))continue;let e=null;if(i(t))e=t;else{if(d(t))continue;(s(t)||o(t))&&t.isInline()&&(e=t)}if(null!==e){if(e&&e.is(h))continue;const t=e.getParent();if(null!=t&&t.is(h)||(_=void 0),h=t,void 0===_){_=(c||a)([r]),e.insertBefore(_)}_.append(e)}else h=void 0,_=void 0}s(_)&&(e?_.selectStart():_.selectEnd())}function m(t,e){let r=t;for(;null!==r;){if(d(r))return r.getIDs();if(i(r)&&e===r.getTextContentSize()){const t=r.getNextSibling();if(d(t))return t.getIDs()}r=r.getParent()}return null}export{a as $createMarkNode,m as $getMarkIDs,d as $isMarkNode,h as $unwrapMarkNode,_ as $wrapSelectionInMarkNode,f as MarkNode};
