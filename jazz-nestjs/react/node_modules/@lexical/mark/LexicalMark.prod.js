/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("lexical"),t=require("@lexical/utils");const r=[];class s extends e.ElementNode{static getType(){return"mark"}static clone(e){return new s(e.__ids,e.__key)}static importDOM(){return null}static importJSON(e){return n().updateFromJSON(e)}updateFromJSON(e){return super.updateFromJSON(e).setIDs(e.ids)}exportJSON(){return{...super.exportJSON(),ids:this.getIDs()}}constructor(e=r,t){super(t),this.__ids=e}createDOM(e){const r=document.createElement("mark");return t.addClassNamesToElement(r,e.theme.mark),this.__ids.length>1&&t.addClassNamesToElement(r,e.theme.markOverlap),r}updateDOM(e,r,s){const n=e.__ids,i=this.__ids,o=n.length,a=i.length,l=s.theme.markOverlap;return o!==a&&(1===o?2===a&&t.addClassNamesToElement(r,l):1===a&&t.removeClassNamesFromElement(r,l)),!1}hasID(e){return this.getIDs().includes(e)}getIDs(){return Array.from(this.getLatest().__ids)}setIDs(e){const t=this.getWritable();return t.__ids=e,t}addID(e){const t=this.getWritable();return t.__ids.includes(e)?t:t.setIDs([...t.__ids,e])}deleteID(e){const t=this.getWritable(),r=t.__ids.indexOf(e);if(-1===r)return t;const s=Array.from(t.__ids);return s.splice(r,1),t.setIDs(s)}insertNewAfter(e,t=!0){const r=n(this.__ids);return this.insertAfter(r,t),r}canInsertTextBefore(){return!1}canInsertTextAfter(){return!1}canBeEmpty(){return!1}isInline(){return!0}extractWithChild(t,r,s){if(!e.$isRangeSelection(r)||"html"===s)return!1;const n=r.anchor,i=r.focus,o=n.getNode(),a=i.getNode(),l=r.isBackward()?n.offset-i.offset:i.offset-n.offset;return this.isParentOf(o)&&this.isParentOf(a)&&this.getTextContent().length===l}excludeFromCopy(e){return"clone"!==e}}function n(t=r){return e.$applyNodeReplacement(new s(t))}function i(e){return e instanceof s}exports.$createMarkNode=n,exports.$getMarkIDs=function(t,r){let s=t;for(;null!==s;){if(i(s))return s.getIDs();if(e.$isTextNode(s)&&r===s.getTextContentSize()){const e=s.getNextSibling();if(i(e))return e.getIDs()}s=s.getParent()}return null},exports.$isMarkNode=i,exports.$unwrapMarkNode=function(e){const t=e.getChildren();let r=null;for(let s=0;s<t.length;s++){const n=t[s];null===r?e.insertBefore(n):r.insertAfter(n),r=n}e.remove()},exports.$wrapSelectionInMarkNode=function(t,r,s,o){const a=e.$createRangeSelection(),[l,c]=t.isBackward()?[t.focus,t.anchor]:[t.anchor,t.focus];let u,d;a.anchor.set(l.key,l.offset,l.type),a.focus.set(c.key,c.offset,c.type);const f=a.extract();for(const t of f){if(e.$isElementNode(d)&&d.isParentOf(t))continue;let r=null;if(e.$isTextNode(t))r=t;else{if(i(t))continue;(e.$isElementNode(t)||e.$isDecoratorNode(t))&&t.isInline()&&(r=t)}if(null!==r){if(r&&r.is(u))continue;const e=r.getParent();if(null!=e&&e.is(u)||(d=void 0),u=e,void 0===d){d=(o||n)([s]),r.insertBefore(d)}d.append(r)}else u=void 0,d=void 0}e.$isElementNode(d)&&(r?d.selectStart():d.selectEnd())},exports.MarkNode=s;
