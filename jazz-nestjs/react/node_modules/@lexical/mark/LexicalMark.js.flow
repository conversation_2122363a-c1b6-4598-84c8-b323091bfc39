/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */

import type {
  NodeKey,
  RangeSelection,
  TextNode,
  SerializedElementNode,
} from 'lexical';
import {ElementNode, LexicalNode} from 'lexical';

export type SerializedMarkNode = SerializedElementNode & {
  ids: Array<string>,
};

declare export class MarkNode extends ElementNode {
  __ids: Array<string>;

  static clone(node: this): MarkNode;
  constructor(ids: Array<string>, key?: NodeKey): void;

  hasID(id: string): boolean;
  getIDs(): Array<string>;
  addID(id: string): void;
  deleteID(id: string): void;
  canInsertTextBefore(): false;
  canInsertTextAfter(): false;
  isInline(): true;
}

declare export function $isMarkNode(
  node: ?LexicalNode,
): node is MarkNode;

declare export function $createMarkNode(ids: Array<string>): MarkNode;

declare export function $getMarkIDs(
  node: TextNode,
  offset: number,
): null | Array<string>;

declare export function $wrapSelectionInMarkNode(
  selection: RangeSelection,
  isBackward: boolean,
  id: string,
  createNode?: (ids: Array<string>) => MarkNode,
): void;

declare export function $unwrapMarkNode(node: MarkNode): void;
