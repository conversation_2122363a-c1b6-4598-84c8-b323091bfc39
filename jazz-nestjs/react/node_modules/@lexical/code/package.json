{"name": "@lexical/code", "description": "This package contains the functionality for the code blocks and code highlighting for Lexical.", "keywords": ["lexical", "editor", "rich-text", "code"], "license": "MIT", "version": "0.27.2", "main": "LexicalCode.js", "types": "index.d.ts", "dependencies": {"@lexical/utils": "0.27.2", "lexical": "0.27.2", "prismjs": "^1.30.0"}, "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-code"}, "devDependencies": {"@types/prismjs": "^1.26.0"}, "module": "LexicalCode.mjs", "sideEffects": true, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalCode.dev.mjs", "production": "./LexicalCode.prod.mjs", "node": "./LexicalCode.node.mjs", "default": "./LexicalCode.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalCode.dev.js", "production": "./LexicalCode.prod.js", "default": "./LexicalCode.js"}}}}