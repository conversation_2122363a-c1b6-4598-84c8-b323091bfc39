/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("@lexical/clipboard"),t=require("@lexical/selection"),n=require("@lexical/utils"),r=require("lexical");function o(e,t){if(void 0!==document.caretRangeFromPoint){const n=document.caretRangeFromPoint(e,t);return null===n?null:{node:n.startContainer,offset:n.startOffset}}if("undefined"!==document.caretPositionFromPoint){const n=document.caretPositionFromPoint(e,t);return null===n?null:{node:n.offsetNode,offset:n.offset}}return null}const i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,s=i&&"documentMode"in document?document.documentMode:null,a=!(!i||!("InputEvent"in window)||s)&&"getTargetRanges"in new window.InputEvent("input"),c=i&&/Version\/[\d.]+.*Safari/.test(navigator.userAgent),l=i&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,u=i&&/^(?=.*Chrome).*/i.test(navigator.userAgent),d=i&&/AppleWebKit\/[\d.]+/.test(navigator.userAgent)&&!u,g=r.createCommand("DRAG_DROP_PASTE_FILE");class O extends r.ElementNode{static getType(){return"quote"}static clone(e){return new O(e.__key)}createDOM(e){const t=document.createElement("blockquote");return n.addClassNamesToElement(t,e.theme.quote),t}updateDOM(e,t){return!1}static importDOM(){return{blockquote:e=>({conversion:M,priority:0})}}exportDOM(e){const{element:t}=super.exportDOM(e);if(n.isHTMLElement(t)){this.isEmpty()&&t.append(document.createElement("br"));const e=this.getFormatType();t.style.textAlign=e;const n=this.getDirection();n&&(t.dir=n)}return{element:t}}static importJSON(e){return R().updateFromJSON(e)}insertNewAfter(e,t){const n=r.$createParagraphNode(),o=this.getDirection();return n.setDirection(o),this.insertAfter(n,t),n}collapseAtStart(){const e=r.$createParagraphNode();return this.getChildren().forEach((t=>e.append(t))),this.replace(e),!0}canMergeWhenEmpty(){return!0}}function R(){return r.$applyNodeReplacement(new O)}class N extends r.ElementNode{static getType(){return"heading"}static clone(e){return new N(e.__tag,e.__key)}constructor(e,t){super(t),this.__tag=e}getTag(){return this.__tag}setTag(e){const t=this.getWritable();return this.__tag=e,t}createDOM(e){const t=this.__tag,r=document.createElement(t),o=e.theme.heading;if(void 0!==o){const e=o[t];n.addClassNamesToElement(r,e)}return r}updateDOM(e,t,n){return e.__tag!==this.__tag}static importDOM(){return{h1:e=>({conversion:m,priority:0}),h2:e=>({conversion:m,priority:0}),h3:e=>({conversion:m,priority:0}),h4:e=>({conversion:m,priority:0}),h5:e=>({conversion:m,priority:0}),h6:e=>({conversion:m,priority:0}),p:e=>{const t=e.firstChild;return null!==t&&D(t)?{conversion:()=>({node:null}),priority:3}:null},span:e=>D(e)?{conversion:e=>({node:_("h1")}),priority:3}:null}}exportDOM(e){const{element:t}=super.exportDOM(e);if(n.isHTMLElement(t)){this.isEmpty()&&t.append(document.createElement("br"));const e=this.getFormatType();t.style.textAlign=e;const n=this.getDirection();n&&(t.dir=n)}return{element:t}}static importJSON(e){return _(e.tag).updateFromJSON(e)}updateFromJSON(e){return super.updateFromJSON(e).setTag(e.tag)}exportJSON(){return{...super.exportJSON(),tag:this.getTag()}}insertNewAfter(e,t=!0){const n=e?e.anchor.offset:0,o=this.getLastDescendant(),i=!o||e&&e.anchor.key===o.getKey()&&n===o.getTextContentSize()||!e?r.$createParagraphNode():_(this.getTag()),s=this.getDirection();if(i.setDirection(s),this.insertAfter(i,t),0===n&&!this.isEmpty()&&e){const e=r.$createParagraphNode();e.select(),this.replace(e,!0)}return i}collapseAtStart(){const e=this.isEmpty()?r.$createParagraphNode():_(this.getTag());return this.getChildren().forEach((t=>e.append(t))),this.replace(e),!0}extractWithChild(){return!0}}function D(e){return"span"===e.nodeName.toLowerCase()&&"26pt"===e.style.fontSize}function m(e){const t=e.nodeName.toLowerCase();let n=null;return"h1"!==t&&"h2"!==t&&"h3"!==t&&"h4"!==t&&"h5"!==t&&"h6"!==t||(n=_(t),null!==e.style&&(r.setNodeIndentFromDOM(e,n),n.setFormat(e.style.textAlign))),{node:n}}function M(e){const t=R();return null!==e.style&&(t.setFormat(e.style.textAlign),r.setNodeIndentFromDOM(e,t)),{node:t}}function _(e="h1"){return r.$applyNodeReplacement(new N(e))}function C(e){let t=null;if(n.objectKlassEquals(e,DragEvent)?t=e.dataTransfer:n.objectKlassEquals(e,ClipboardEvent)&&(t=e.clipboardData),null===t)return[!1,[],!1];const r=t.types,o=r.includes("Files"),i=r.includes("text/html")||r.includes("text/plain");return[o,Array.from(t.files),i]}function T(e){const t=r.$getSelection();if(!r.$isRangeSelection(t))return!1;const o=new Set,i=t.getNodes();for(let t=0;t<i.length;t++){const s=i[t],a=s.getKey();if(o.has(a))continue;const c=n.$findMatchingParent(s,(e=>r.$isElementNode(e)&&!e.isInline()));if(null===c)continue;const l=c.getKey();c.canIndent()&&!o.has(l)&&(o.add(l),e(c))}return o.size>0}function E(e){const t=r.$getNearestNodeFromDOMNode(e);return r.$isDecoratorNode(t)}function f(e){for(const t of["lowercase","uppercase","capitalize"])e.hasFormat(t)&&e.toggleFormat(t)}exports.$createHeadingNode=_,exports.$createQuoteNode=R,exports.$isHeadingNode=function(e){return e instanceof N},exports.$isQuoteNode=function(e){return e instanceof O},exports.DRAG_DROP_PASTE=g,exports.HeadingNode=N,exports.QuoteNode=O,exports.eventFiles=C,exports.registerRichText=function(i){const s=n.mergeRegister(i.registerCommand(r.CLICK_COMMAND,(e=>{const t=r.$getSelection();return!!r.$isNodeSelection(t)&&(t.clear(),!0)}),0),i.registerCommand(r.DELETE_CHARACTER_COMMAND,(e=>{const t=r.$getSelection();return!!r.$isRangeSelection(t)&&(t.deleteCharacter(e),!0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.DELETE_WORD_COMMAND,(e=>{const t=r.$getSelection();return!!r.$isRangeSelection(t)&&(t.deleteWord(e),!0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.DELETE_LINE_COMMAND,(e=>{const t=r.$getSelection();return!!r.$isRangeSelection(t)&&(t.deleteLine(e),!0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.CONTROLLED_TEXT_INSERTION_COMMAND,(t=>{const n=r.$getSelection();if("string"==typeof t)null!==n&&n.insertText(t);else{if(null===n)return!1;const o=t.dataTransfer;if(null!=o)e.$insertDataTransferForRichText(o,n,i);else if(r.$isRangeSelection(n)){const e=t.data;return e&&n.insertText(e),!0}}return!0}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.REMOVE_TEXT_COMMAND,(()=>{const e=r.$getSelection();return!!r.$isRangeSelection(e)&&(e.removeText(),!0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.FORMAT_TEXT_COMMAND,(e=>{const t=r.$getSelection();return!!r.$isRangeSelection(t)&&(t.formatText(e),!0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.FORMAT_ELEMENT_COMMAND,(e=>{const t=r.$getSelection();if(!r.$isRangeSelection(t)&&!r.$isNodeSelection(t))return!1;const o=t.getNodes();for(const t of o){const o=n.$findMatchingParent(t,(e=>r.$isElementNode(e)&&!e.isInline()));null!==o&&o.setFormat(e)}return!0}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.INSERT_LINE_BREAK_COMMAND,(e=>{const t=r.$getSelection();return!!r.$isRangeSelection(t)&&(t.insertLineBreak(e),!0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.INSERT_PARAGRAPH_COMMAND,(()=>{const e=r.$getSelection();return!!r.$isRangeSelection(e)&&(e.insertParagraph(),!0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.INSERT_TAB_COMMAND,(()=>(r.$insertNodes([r.$createTabNode()]),!0)),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.INDENT_CONTENT_COMMAND,(()=>T((e=>{const t=e.getIndent();e.setIndent(t+1)}))),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.OUTDENT_CONTENT_COMMAND,(()=>T((e=>{const t=e.getIndent();t>0&&e.setIndent(t-1)}))),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.KEY_ARROW_UP_COMMAND,(e=>{const t=r.$getSelection();if(r.$isNodeSelection(t)&&!E(e.target)){const e=t.getNodes();if(e.length>0)return e[0].selectPrevious(),!0}else if(r.$isRangeSelection(t)){const n=r.$getAdjacentNode(t.focus,!0);if(!e.shiftKey&&r.$isDecoratorNode(n)&&!n.isIsolated()&&!n.isInline())return n.selectPrevious(),e.preventDefault(),!0}return!1}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.KEY_ARROW_DOWN_COMMAND,(e=>{const t=r.$getSelection();if(r.$isNodeSelection(t)){const e=t.getNodes();if(e.length>0)return e[0].selectNext(0,0),!0}else if(r.$isRangeSelection(t)){if(function(e){const t=e.focus;return"root"===t.key&&t.offset===r.$getRoot().getChildrenSize()}(t))return e.preventDefault(),!0;const n=r.$getAdjacentNode(t.focus,!1);if(!e.shiftKey&&r.$isDecoratorNode(n)&&!n.isIsolated()&&!n.isInline())return n.selectNext(),e.preventDefault(),!0}return!1}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.KEY_ARROW_LEFT_COMMAND,(e=>{const n=r.$getSelection();if(r.$isNodeSelection(n)){const t=n.getNodes();if(t.length>0)return e.preventDefault(),t[0].selectPrevious(),!0}if(!r.$isRangeSelection(n))return!1;if(t.$shouldOverrideDefaultCharacterSelection(n,!0)){const r=e.shiftKey;return e.preventDefault(),t.$moveCharacter(n,r,!0),!0}return!1}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.KEY_ARROW_RIGHT_COMMAND,(e=>{const n=r.$getSelection();if(r.$isNodeSelection(n)&&!E(e.target)){const t=n.getNodes();if(t.length>0)return e.preventDefault(),t[0].selectNext(0,0),!0}if(!r.$isRangeSelection(n))return!1;const o=e.shiftKey;return!!t.$shouldOverrideDefaultCharacterSelection(n,!1)&&(e.preventDefault(),t.$moveCharacter(n,o,!1),!0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.KEY_BACKSPACE_COMMAND,(e=>{if(E(e.target))return!1;const t=r.$getSelection();if(!r.$isRangeSelection(t))return!1;const{anchor:o}=t,s=o.getNode();if(t.isCollapsed()&&0===o.offset&&!r.$isRootNode(s)){if(n.$getNearestBlockElementAncestorOrThrow(s).getIndent()>0)return e.preventDefault(),i.dispatchCommand(r.OUTDENT_CONTENT_COMMAND,void 0)}return(!l||"ko-KR"!==navigator.language)&&(e.preventDefault(),i.dispatchCommand(r.DELETE_CHARACTER_COMMAND,!0))}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.KEY_DELETE_COMMAND,(e=>{if(E(e.target))return!1;const t=r.$getSelection();return!!r.$isRangeSelection(t)&&(e.preventDefault(),i.dispatchCommand(r.DELETE_CHARACTER_COMMAND,!1))}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.KEY_ENTER_COMMAND,(e=>{const t=r.$getSelection();if(!r.$isRangeSelection(t))return!1;if(f(t),null!==e){if((l||c||d)&&a)return!1;if(e.preventDefault(),e.shiftKey)return i.dispatchCommand(r.INSERT_LINE_BREAK_COMMAND,!1)}return i.dispatchCommand(r.INSERT_PARAGRAPH_COMMAND,void 0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.KEY_ESCAPE_COMMAND,(()=>{const e=r.$getSelection();return!!r.$isRangeSelection(e)&&(i.blur(),!0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.DROP_COMMAND,(e=>{const[,t]=C(e);if(t.length>0){const n=o(e.clientX,e.clientY);if(null!==n){const{offset:e,node:o}=n,s=r.$getNearestNodeFromDOMNode(o);if(null!==s){const t=r.$createRangeSelection();if(r.$isTextNode(s))t.anchor.set(s.getKey(),e,"text"),t.focus.set(s.getKey(),e,"text");else{const e=s.getParentOrThrow().getKey(),n=s.getIndexWithinParent()+1;t.anchor.set(e,n,"element"),t.focus.set(e,n,"element")}const n=r.$normalizeSelection__EXPERIMENTAL(t);r.$setSelection(n)}i.dispatchCommand(g,t)}return e.preventDefault(),!0}const n=r.$getSelection();return!!r.$isRangeSelection(n)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.DRAGSTART_COMMAND,(e=>{const[t]=C(e),n=r.$getSelection();return!(t&&!r.$isRangeSelection(n))}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.DRAGOVER_COMMAND,(e=>{const[t]=C(e),n=r.$getSelection();if(t&&!r.$isRangeSelection(n))return!1;const i=o(e.clientX,e.clientY);if(null!==i){const t=r.$getNearestNodeFromDOMNode(i.node);r.$isDecoratorNode(t)&&e.preventDefault()}return!0}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.SELECT_ALL_COMMAND,(()=>(r.$selectAll(),!0)),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.COPY_COMMAND,(t=>(e.copyToClipboard(i,n.objectKlassEquals(t,ClipboardEvent)?t:null),!0)),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.CUT_COMMAND,(t=>(async function(t,o){await e.copyToClipboard(o,n.objectKlassEquals(t,ClipboardEvent)?t:null),o.update((()=>{const e=r.$getSelection();r.$isRangeSelection(e)?e.removeText():r.$isNodeSelection(e)&&e.getNodes().forEach((e=>e.remove()))}))}(t,i),!0)),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.PASTE_COMMAND,(t=>{const[,o,s]=C(t);if(o.length>0&&!s)return i.dispatchCommand(g,o),!0;if(r.isDOMNode(t.target)&&r.isSelectionCapturedInDecoratorInput(t.target))return!1;return null!==r.$getSelection()&&(function(t,o){t.preventDefault(),o.update((()=>{const i=r.$getSelection(),s=n.objectKlassEquals(t,InputEvent)||n.objectKlassEquals(t,KeyboardEvent)?null:t.clipboardData;null!=s&&null!==i&&e.$insertDataTransferForRichText(s,i,o)}),{tag:"paste"})}(t,i),!0)}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.KEY_SPACE_COMMAND,(e=>{const t=r.$getSelection();return r.$isRangeSelection(t)&&f(t),!1}),r.COMMAND_PRIORITY_EDITOR),i.registerCommand(r.KEY_TAB_COMMAND,(e=>{const t=r.$getSelection();return r.$isRangeSelection(t)&&f(t),!1}),r.COMMAND_PRIORITY_EDITOR));return s};
