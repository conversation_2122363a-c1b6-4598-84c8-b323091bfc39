/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{$insertDataTransferForRichText as t,copyToClipboard as e}from"@lexical/clipboard";import{$shouldOverrideDefaultCharacterSelection as n,$move<PERSON><PERSON>cter as r}from"@lexical/selection";import{addClassNamesToElement as o,isHTMLElement as i,objectKlassEquals as s,mergeRegister as a,$findMatchingParent as c,$getNearestBlockElementAncestorOrThrow as u}from"@lexical/utils";import{createCommand as l,ElementNode as d,$createParagraphNode as m,$applyNodeReplacement as f,setNodeIndentFromDOM as g,CLICK_COMMAND as p,$getSelection as h,$isNodeSelection as C,DELETE_CHARACTER_COMMAND as v,$isRangeSelection as y,COMMAND_PRIORITY_EDITOR as D,DELETE_WORD_COMMAND as x,DELETE_LINE_COMMAND as w,CONTROLLED_TEXT_INSERTION_COMMAND as N,REMOVE_TEXT_COMMAND as E,FORMAT_TEXT_COMMAND as O,FORMAT_ELEMENT_COMMAND as T,$isElementNode as _,INSERT_LINE_BREAK_COMMAND as A,INSERT_PARAGRAPH_COMMAND as S,INSERT_TAB_COMMAND as F,$insertNodes as I,$createTabNode as P,INDENT_CONTENT_COMMAND as b,OUTDENT_CONTENT_COMMAND as K,KEY_ARROW_UP_COMMAND as M,$getAdjacentNode as k,$isDecoratorNode as J,KEY_ARROW_DOWN_COMMAND as L,KEY_ARROW_LEFT_COMMAND as R,KEY_ARROW_RIGHT_COMMAND as W,KEY_BACKSPACE_COMMAND as z,$isRootNode as q,KEY_DELETE_COMMAND as X,KEY_ENTER_COMMAND as Y,KEY_ESCAPE_COMMAND as B,DROP_COMMAND as G,$getNearestNodeFromDOMNode as V,$createRangeSelection as j,$isTextNode as H,$normalizeSelection__EXPERIMENTAL as Q,$setSelection as U,DRAGSTART_COMMAND as Z,DRAGOVER_COMMAND as $,SELECT_ALL_COMMAND as tt,$selectAll as et,COPY_COMMAND as nt,CUT_COMMAND as rt,PASTE_COMMAND as ot,isDOMNode as it,isSelectionCapturedInDecoratorInput as st,KEY_SPACE_COMMAND as at,KEY_TAB_COMMAND as ct,$getRoot as ut}from"lexical";function lt(t,e){if(void 0!==document.caretRangeFromPoint){const n=document.caretRangeFromPoint(t,e);return null===n?null:{node:n.startContainer,offset:n.startOffset}}if("undefined"!==document.caretPositionFromPoint){const n=document.caretPositionFromPoint(t,e);return null===n?null:{node:n.offsetNode,offset:n.offset}}return null}const dt="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,mt=dt&&"documentMode"in document?document.documentMode:null,ft=!(!dt||!("InputEvent"in window)||mt)&&"getTargetRanges"in new window.InputEvent("input"),gt=dt&&/Version\/[\d.]+.*Safari/.test(navigator.userAgent),pt=dt&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,ht=dt&&/^(?=.*Chrome).*/i.test(navigator.userAgent),Ct=dt&&/AppleWebKit\/[\d.]+/.test(navigator.userAgent)&&!ht,vt=l("DRAG_DROP_PASTE_FILE");class yt extends d{static getType(){return"quote"}static clone(t){return new yt(t.__key)}createDOM(t){const e=document.createElement("blockquote");return o(e,t.theme.quote),e}updateDOM(t,e){return!1}static importDOM(){return{blockquote:t=>({conversion:Ot,priority:0})}}exportDOM(t){const{element:e}=super.exportDOM(t);if(i(e)){this.isEmpty()&&e.append(document.createElement("br"));const t=this.getFormatType();e.style.textAlign=t;const n=this.getDirection();n&&(e.dir=n)}return{element:e}}static importJSON(t){return Dt().updateFromJSON(t)}insertNewAfter(t,e){const n=m(),r=this.getDirection();return n.setDirection(r),this.insertAfter(n,e),n}collapseAtStart(){const t=m();return this.getChildren().forEach((e=>t.append(e))),this.replace(t),!0}canMergeWhenEmpty(){return!0}}function Dt(){return f(new yt)}function xt(t){return t instanceof yt}class wt extends d{static getType(){return"heading"}static clone(t){return new wt(t.__tag,t.__key)}constructor(t,e){super(e),this.__tag=t}getTag(){return this.__tag}setTag(t){const e=this.getWritable();return this.__tag=t,e}createDOM(t){const e=this.__tag,n=document.createElement(e),r=t.theme.heading;if(void 0!==r){const t=r[e];o(n,t)}return n}updateDOM(t,e,n){return t.__tag!==this.__tag}static importDOM(){return{h1:t=>({conversion:Et,priority:0}),h2:t=>({conversion:Et,priority:0}),h3:t=>({conversion:Et,priority:0}),h4:t=>({conversion:Et,priority:0}),h5:t=>({conversion:Et,priority:0}),h6:t=>({conversion:Et,priority:0}),p:t=>{const e=t.firstChild;return null!==e&&Nt(e)?{conversion:()=>({node:null}),priority:3}:null},span:t=>Nt(t)?{conversion:t=>({node:Tt("h1")}),priority:3}:null}}exportDOM(t){const{element:e}=super.exportDOM(t);if(i(e)){this.isEmpty()&&e.append(document.createElement("br"));const t=this.getFormatType();e.style.textAlign=t;const n=this.getDirection();n&&(e.dir=n)}return{element:e}}static importJSON(t){return Tt(t.tag).updateFromJSON(t)}updateFromJSON(t){return super.updateFromJSON(t).setTag(t.tag)}exportJSON(){return{...super.exportJSON(),tag:this.getTag()}}insertNewAfter(t,e=!0){const n=t?t.anchor.offset:0,r=this.getLastDescendant(),o=!r||t&&t.anchor.key===r.getKey()&&n===r.getTextContentSize()||!t?m():Tt(this.getTag()),i=this.getDirection();if(o.setDirection(i),this.insertAfter(o,e),0===n&&!this.isEmpty()&&t){const t=m();t.select(),this.replace(t,!0)}return o}collapseAtStart(){const t=this.isEmpty()?m():Tt(this.getTag());return this.getChildren().forEach((e=>t.append(e))),this.replace(t),!0}extractWithChild(){return!0}}function Nt(t){return"span"===t.nodeName.toLowerCase()&&"26pt"===t.style.fontSize}function Et(t){const e=t.nodeName.toLowerCase();let n=null;return"h1"!==e&&"h2"!==e&&"h3"!==e&&"h4"!==e&&"h5"!==e&&"h6"!==e||(n=Tt(e),null!==t.style&&(g(t,n),n.setFormat(t.style.textAlign))),{node:n}}function Ot(t){const e=Dt();return null!==t.style&&(e.setFormat(t.style.textAlign),g(t,e)),{node:e}}function Tt(t="h1"){return f(new wt(t))}function _t(t){return t instanceof wt}function At(t){let e=null;if(s(t,DragEvent)?e=t.dataTransfer:s(t,ClipboardEvent)&&(e=t.clipboardData),null===e)return[!1,[],!1];const n=e.types,r=n.includes("Files"),o=n.includes("text/html")||n.includes("text/plain");return[r,Array.from(e.files),o]}function St(t){const e=h();if(!y(e))return!1;const n=new Set,r=e.getNodes();for(let e=0;e<r.length;e++){const o=r[e],i=o.getKey();if(n.has(i))continue;const s=c(o,(t=>_(t)&&!t.isInline()));if(null===s)continue;const a=s.getKey();s.canIndent()&&!n.has(a)&&(n.add(a),t(s))}return n.size>0}function Ft(t){const e=V(t);return J(e)}function It(t){for(const e of["lowercase","uppercase","capitalize"])t.hasFormat(e)&&t.toggleFormat(e)}function Pt(o){return a(o.registerCommand(p,(t=>{const e=h();return!!C(e)&&(e.clear(),!0)}),0),o.registerCommand(v,(t=>{const e=h();return!!y(e)&&(e.deleteCharacter(t),!0)}),D),o.registerCommand(x,(t=>{const e=h();return!!y(e)&&(e.deleteWord(t),!0)}),D),o.registerCommand(w,(t=>{const e=h();return!!y(e)&&(e.deleteLine(t),!0)}),D),o.registerCommand(N,(e=>{const n=h();if("string"==typeof e)null!==n&&n.insertText(e);else{if(null===n)return!1;const r=e.dataTransfer;if(null!=r)t(r,n,o);else if(y(n)){const t=e.data;return t&&n.insertText(t),!0}}return!0}),D),o.registerCommand(E,(()=>{const t=h();return!!y(t)&&(t.removeText(),!0)}),D),o.registerCommand(O,(t=>{const e=h();return!!y(e)&&(e.formatText(t),!0)}),D),o.registerCommand(T,(t=>{const e=h();if(!y(e)&&!C(e))return!1;const n=e.getNodes();for(const e of n){const n=c(e,(t=>_(t)&&!t.isInline()));null!==n&&n.setFormat(t)}return!0}),D),o.registerCommand(A,(t=>{const e=h();return!!y(e)&&(e.insertLineBreak(t),!0)}),D),o.registerCommand(S,(()=>{const t=h();return!!y(t)&&(t.insertParagraph(),!0)}),D),o.registerCommand(F,(()=>(I([P()]),!0)),D),o.registerCommand(b,(()=>St((t=>{const e=t.getIndent();t.setIndent(e+1)}))),D),o.registerCommand(K,(()=>St((t=>{const e=t.getIndent();e>0&&t.setIndent(e-1)}))),D),o.registerCommand(M,(t=>{const e=h();if(C(e)&&!Ft(t.target)){const t=e.getNodes();if(t.length>0)return t[0].selectPrevious(),!0}else if(y(e)){const n=k(e.focus,!0);if(!t.shiftKey&&J(n)&&!n.isIsolated()&&!n.isInline())return n.selectPrevious(),t.preventDefault(),!0}return!1}),D),o.registerCommand(L,(t=>{const e=h();if(C(e)){const t=e.getNodes();if(t.length>0)return t[0].selectNext(0,0),!0}else if(y(e)){if(function(t){const e=t.focus;return"root"===e.key&&e.offset===ut().getChildrenSize()}(e))return t.preventDefault(),!0;const n=k(e.focus,!1);if(!t.shiftKey&&J(n)&&!n.isIsolated()&&!n.isInline())return n.selectNext(),t.preventDefault(),!0}return!1}),D),o.registerCommand(R,(t=>{const e=h();if(C(e)){const n=e.getNodes();if(n.length>0)return t.preventDefault(),n[0].selectPrevious(),!0}if(!y(e))return!1;if(n(e,!0)){const n=t.shiftKey;return t.preventDefault(),r(e,n,!0),!0}return!1}),D),o.registerCommand(W,(t=>{const e=h();if(C(e)&&!Ft(t.target)){const n=e.getNodes();if(n.length>0)return t.preventDefault(),n[0].selectNext(0,0),!0}if(!y(e))return!1;const o=t.shiftKey;return!!n(e,!1)&&(t.preventDefault(),r(e,o,!1),!0)}),D),o.registerCommand(z,(t=>{if(Ft(t.target))return!1;const e=h();if(!y(e))return!1;const{anchor:n}=e,r=n.getNode();if(e.isCollapsed()&&0===n.offset&&!q(r)){if(u(r).getIndent()>0)return t.preventDefault(),o.dispatchCommand(K,void 0)}return(!pt||"ko-KR"!==navigator.language)&&(t.preventDefault(),o.dispatchCommand(v,!0))}),D),o.registerCommand(X,(t=>{if(Ft(t.target))return!1;const e=h();return!!y(e)&&(t.preventDefault(),o.dispatchCommand(v,!1))}),D),o.registerCommand(Y,(t=>{const e=h();if(!y(e))return!1;if(It(e),null!==t){if((pt||gt||Ct)&&ft)return!1;if(t.preventDefault(),t.shiftKey)return o.dispatchCommand(A,!1)}return o.dispatchCommand(S,void 0)}),D),o.registerCommand(B,(()=>{const t=h();return!!y(t)&&(o.blur(),!0)}),D),o.registerCommand(G,(t=>{const[,e]=At(t);if(e.length>0){const n=lt(t.clientX,t.clientY);if(null!==n){const{offset:t,node:r}=n,i=V(r);if(null!==i){const e=j();if(H(i))e.anchor.set(i.getKey(),t,"text"),e.focus.set(i.getKey(),t,"text");else{const t=i.getParentOrThrow().getKey(),n=i.getIndexWithinParent()+1;e.anchor.set(t,n,"element"),e.focus.set(t,n,"element")}const n=Q(e);U(n)}o.dispatchCommand(vt,e)}return t.preventDefault(),!0}const n=h();return!!y(n)}),D),o.registerCommand(Z,(t=>{const[e]=At(t),n=h();return!(e&&!y(n))}),D),o.registerCommand($,(t=>{const[e]=At(t),n=h();if(e&&!y(n))return!1;const r=lt(t.clientX,t.clientY);if(null!==r){const e=V(r.node);J(e)&&t.preventDefault()}return!0}),D),o.registerCommand(tt,(()=>(et(),!0)),D),o.registerCommand(nt,(t=>(e(o,s(t,ClipboardEvent)?t:null),!0)),D),o.registerCommand(rt,(t=>(async function(t,n){await e(n,s(t,ClipboardEvent)?t:null),n.update((()=>{const t=h();y(t)?t.removeText():C(t)&&t.getNodes().forEach((t=>t.remove()))}))}(t,o),!0)),D),o.registerCommand(ot,(e=>{const[,n,r]=At(e);if(n.length>0&&!r)return o.dispatchCommand(vt,n),!0;if(it(e.target)&&st(e.target))return!1;return null!==h()&&(function(e,n){e.preventDefault(),n.update((()=>{const r=h(),o=s(e,InputEvent)||s(e,KeyboardEvent)?null:e.clipboardData;null!=o&&null!==r&&t(o,r,n)}),{tag:"paste"})}(e,o),!0)}),D),o.registerCommand(at,(t=>{const e=h();return y(e)&&It(e),!1}),D),o.registerCommand(ct,(t=>{const e=h();return y(e)&&It(e),!1}),D))}export{Tt as $createHeadingNode,Dt as $createQuoteNode,_t as $isHeadingNode,xt as $isQuoteNode,vt as DRAG_DROP_PASTE,wt as HeadingNode,yt as QuoteNode,At as eventFiles,Pt as registerRichText};
