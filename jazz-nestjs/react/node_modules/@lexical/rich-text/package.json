{"name": "@lexical/rich-text", "description": "This package contains rich text helpers for Lexical.", "keywords": ["lexical", "editor", "rich-text"], "license": "MIT", "version": "0.27.2", "main": "LexicalRichText.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-rich-text"}, "module": "LexicalRichText.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalRichText.dev.mjs", "production": "./LexicalRichText.prod.mjs", "node": "./LexicalRichText.node.mjs", "default": "./LexicalRichText.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalRichText.dev.js", "production": "./LexicalRichText.prod.js", "default": "./LexicalRichText.js"}}}, "dependencies": {"@lexical/clipboard": "0.27.2", "@lexical/selection": "0.27.2", "@lexical/utils": "0.27.2", "lexical": "0.27.2"}}