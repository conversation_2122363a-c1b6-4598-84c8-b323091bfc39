/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{addClassNamesToElement as e,$descendantsMatching as t,$findMatchingParent as n,removeClassNamesFromElement as o,objectKlassEquals as r,isHTMLElement as l,$insertFirst as s,mergeRegister as i,$insertNodeToNearestRoot as c,$unwrapAndFilterDescendants as a}from"@lexical/utils";import{ElementNode as u,isHTMLElement as h,$createParagraphNode as d,$isElementNode as f,$isLineBreakNode as g,$isTextNode as m,$applyNodeReplacement as p,createCommand as C,$createTextNode as _,$getSelection as S,$isRangeSelection as w,$createPoint as b,$isParagraphNode as y,$normalizeSelection__EXPERIMENTAL as N,isCurrentlyReadOnlyMode as x,TEXT_TYPE_TO_FORMAT as v,$getNodeByKey as T,$getEditor as R,$setSelection as F,SELECTION_CHANGE_COMMAND as A,getDOMSelection as O,$createRangeSelection as K,$isRootNode as E,INSERT_PARAGRAPH_COMMAND as k,COMMAND_PRIORITY_HIGH as M,KEY_ESCAPE_COMMAND as $,COMMAND_PRIORITY_CRITICAL as L,CUT_COMMAND as z,FORMAT_TEXT_COMMAND as W,FORMAT_ELEMENT_COMMAND as H,CONTROLLED_TEXT_INSERTION_COMMAND as B,KEY_TAB_COMMAND as P,FOCUS_COMMAND as D,SELECTION_INSERT_CLIPBOARD_NODES_COMMAND as I,$getPreviousSelection as U,$getNearestNodeFromDOMNode as J,$createRangeSelectionFromDom as Y,$isRootOrShadowRoot as q,KEY_ARROW_DOWN_COMMAND as X,KEY_ARROW_UP_COMMAND as j,KEY_ARROW_LEFT_COMMAND as V,KEY_ARROW_RIGHT_COMMAND as G,DELETE_WORD_COMMAND as Q,DELETE_LINE_COMMAND as Z,DELETE_CHARACTER_COMMAND as ee,KEY_BACKSPACE_COMMAND as te,KEY_DELETE_COMMAND as ne,isDOMNode as oe,$caretFromPoint as re,$isExtendableTextPointCaret as le,$extendCaretToRange as se,$isSiblingCaret as ie,$getSiblingCaret as ce,$setPointFromCaret as ae,$normalizeCaret as ue,$getAdjacentChildCaret as he,$isChildCaret as de,$getChildCaret as fe,setDOMUnmanaged as ge,COMMAND_PRIORITY_EDITOR as me,CLICK_COMMAND as pe}from"lexical";import{copyToClipboard as Ce,$getClipboardDataFromSelection as _e}from"@lexical/clipboard";const Se=/^(\d+(?:\.\d+)?)px$/,we={BOTH:3,COLUMN:2,NO_STATUS:0,ROW:1};class be extends u{static getType(){return"tablecell"}static clone(e){return new be(e.__headerState,e.__colSpan,e.__width,e.__key)}afterCloneFrom(e){super.afterCloneFrom(e),this.__rowSpan=e.__rowSpan,this.__backgroundColor=e.__backgroundColor,this.__verticalAlign=e.__verticalAlign}static importDOM(){return{td:e=>({conversion:Ne,priority:0}),th:e=>({conversion:Ne,priority:0})}}static importJSON(e){return xe().updateFromJSON(e)}updateFromJSON(e){return super.updateFromJSON(e).setHeaderStyles(e.headerState).setColSpan(e.colSpan||1).setRowSpan(e.rowSpan||1).setWidth(e.width||void 0).setBackgroundColor(e.backgroundColor||null).setVerticalAlign(e.verticalAlign||void 0)}constructor(e=we.NO_STATUS,t=1,n,o){super(o),this.__colSpan=t,this.__rowSpan=1,this.__headerState=e,this.__width=n,this.__backgroundColor=null,this.__verticalAlign=void 0}createDOM(t){const n=document.createElement(this.getTag());return this.__width&&(n.style.width=`${this.__width}px`),this.__colSpan>1&&(n.colSpan=this.__colSpan),this.__rowSpan>1&&(n.rowSpan=this.__rowSpan),null!==this.__backgroundColor&&(n.style.backgroundColor=this.__backgroundColor),ye(this.__verticalAlign)&&(n.style.verticalAlign=this.__verticalAlign),e(n,t.theme.tableCell,this.hasHeader()&&t.theme.tableCellHeader),n}exportDOM(e){const t=super.exportDOM(e);if(h(t.element)){const e=t.element;e.setAttribute("data-temporary-table-cell-lexical-key",this.getKey()),e.style.border="1px solid black",this.__colSpan>1&&(e.colSpan=this.__colSpan),this.__rowSpan>1&&(e.rowSpan=this.__rowSpan),e.style.width=`${this.getWidth()||75}px`,e.style.verticalAlign=this.getVerticalAlign()||"top",e.style.textAlign="start",null===this.__backgroundColor&&this.hasHeader()&&(e.style.backgroundColor="#f2f3f5")}return t}exportJSON(){return{...super.exportJSON(),...ye(this.__verticalAlign)&&{verticalAlign:this.__verticalAlign},backgroundColor:this.getBackgroundColor(),colSpan:this.__colSpan,headerState:this.__headerState,rowSpan:this.__rowSpan,width:this.getWidth()}}getColSpan(){return this.getLatest().__colSpan}setColSpan(e){const t=this.getWritable();return t.__colSpan=e,t}getRowSpan(){return this.getLatest().__rowSpan}setRowSpan(e){const t=this.getWritable();return t.__rowSpan=e,t}getTag(){return this.hasHeader()?"th":"td"}setHeaderStyles(e,t=we.BOTH){const n=this.getWritable();return n.__headerState=e&t|n.__headerState&~t,n}getHeaderStyles(){return this.getLatest().__headerState}setWidth(e){const t=this.getWritable();return t.__width=e,t}getWidth(){return this.getLatest().__width}getBackgroundColor(){return this.getLatest().__backgroundColor}setBackgroundColor(e){const t=this.getWritable();return t.__backgroundColor=e,t}getVerticalAlign(){return this.getLatest().__verticalAlign}setVerticalAlign(e){const t=this.getWritable();return t.__verticalAlign=e||void 0,t}toggleHeaderStyle(e){const t=this.getWritable();return(t.__headerState&e)===e?t.__headerState-=e:t.__headerState+=e,t}hasHeaderState(e){return(this.getHeaderStyles()&e)===e}hasHeader(){return this.getLatest().__headerState!==we.NO_STATUS}updateDOM(e){return e.__headerState!==this.__headerState||e.__width!==this.__width||e.__colSpan!==this.__colSpan||e.__rowSpan!==this.__rowSpan||e.__backgroundColor!==this.__backgroundColor||e.__verticalAlign!==this.__verticalAlign}isShadowRoot(){return!0}collapseAtStart(){return!0}canBeEmpty(){return!1}canIndent(){return!1}}function ye(e){return"middle"===e||"bottom"===e}function Ne(e){const t=e,n=e.nodeName.toLowerCase();let o;Se.test(t.style.width)&&(o=parseFloat(t.style.width));const r=xe("th"===n?we.ROW:we.NO_STATUS,t.colSpan,o);r.__rowSpan=t.rowSpan;const l=t.style.backgroundColor;""!==l&&(r.__backgroundColor=l);const s=t.style.verticalAlign;ye(s)&&(r.__verticalAlign=s);const i=t.style,c=(i&&i.textDecoration||"").split(" "),a="700"===i.fontWeight||"bold"===i.fontWeight,u=c.includes("line-through"),h="italic"===i.fontStyle,p=c.includes("underline");return{after:e=>(0===e.length&&e.push(d()),e),forChild:(e,t)=>{if(ve(t)&&!f(e)){const t=d();return g(e)&&"\n"===e.getTextContent()?null:(m(e)&&(a&&e.toggleFormat("bold"),u&&e.toggleFormat("strikethrough"),h&&e.toggleFormat("italic"),p&&e.toggleFormat("underline")),t.append(e),t)}return e},node:r}}function xe(e=we.NO_STATUS,t=1,n){return p(new be(e,t,n))}function ve(e){return e instanceof be}const Te=C("INSERT_TABLE_COMMAND");function Re(e,...t){const n=new URL("https://lexical.dev/docs/error"),o=new URLSearchParams;o.append("code",e);for(const e of t)o.append("v",e);throw n.search=o.toString(),Error(`Minified Lexical error #${e}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}class Fe extends u{static getType(){return"tablerow"}static clone(e){return new Fe(e.__height,e.__key)}static importDOM(){return{tr:e=>({conversion:Ae,priority:0})}}static importJSON(e){return Oe().updateFromJSON(e)}updateFromJSON(e){return super.updateFromJSON(e).setHeight(e.height)}constructor(e,t){super(t),this.__height=e}exportJSON(){const e=this.getHeight();return{...super.exportJSON(),...void 0===e?void 0:{height:e}}}createDOM(t){const n=document.createElement("tr");return this.__height&&(n.style.height=`${this.__height}px`),e(n,t.theme.tableRow),n}extractWithChild(e,t,n){return"html"===n}isShadowRoot(){return!0}setHeight(e){const t=this.getWritable();return t.__height=e,t}getHeight(){return this.getLatest().__height}updateDOM(e){return e.__height!==this.__height}canBeEmpty(){return!1}canIndent(){return!1}}function Ae(e){const n=e;let o;return Se.test(n.style.height)&&(o=parseFloat(n.style.height)),{after:e=>t(e,ve),node:Oe(o)}}function Oe(e){return p(new Fe(e))}function Ke(e){return e instanceof Fe}const Ee="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,ke=Ee&&"documentMode"in document?document.documentMode:null,Me=Ee&&/^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);function $e(e,t,n=!0){const o=rn();for(let r=0;r<e;r++){const e=Oe();for(let o=0;o<t;o++){let t=we.NO_STATUS;"object"==typeof n?(0===r&&n.rows&&(t|=we.ROW),0===o&&n.columns&&(t|=we.COLUMN)):n&&(0===r&&(t|=we.ROW),0===o&&(t|=we.COLUMN));const l=xe(t),s=d();s.append(_()),l.append(s),e.append(l)}o.append(e)}return o}function Le(e){const t=n(e,(e=>ve(e)));return ve(t)?t:null}function ze(e){const t=n(e,(e=>Ke(e)));if(Ke(t))return t;throw new Error("Expected table cell to be inside of table row.")}function We(e){const t=n(e,(e=>ln(e)));if(ln(t))return t;throw new Error("Expected table cell to be inside of table.")}function He(e){const t=ze(e);return We(t).getChildren().findIndex((e=>e.is(t)))}function Be(e){return ze(e).getChildren().findIndex((t=>t.is(e)))}function Pe(e,t){const n=We(e),{x:o,y:r}=n.getCordsFromCellNode(e,t);return{above:n.getCellNodeFromCords(o,r-1,t),below:n.getCellNodeFromCords(o,r+1,t),left:n.getCellNodeFromCords(o-1,r,t),right:n.getCellNodeFromCords(o+1,r,t)}}function De(e,t){const n=e.getChildren();if(t>=n.length||t<0)throw new Error("Expected table cell to be inside of table row.");return n[t].remove(),e}function Ie(e,t,n=!0,o,r){const l=e.getChildren();if(t>=l.length||t<0)throw new Error("Table row target index out of range");const s=l[t];if(!Ke(s))throw new Error("Row before insertion index does not exist.");for(let e=0;e<o;e++){const e=s.getChildren(),t=e.length,o=Oe();for(let n=0;n<t;n++){const t=e[n];ve(t)||Re(12);const{above:l,below:s}=Pe(t,r);let i=we.NO_STATUS;const c=l&&l.getWidth()||s&&s.getWidth()||void 0;(l&&l.hasHeaderState(we.COLUMN)||s&&s.hasHeaderState(we.COLUMN))&&(i|=we.COLUMN);const a=xe(i,1,c);a.append(d()),o.append(a)}n?s.insertAfter(o):s.insertBefore(o)}return e}Ee&&"InputEvent"in window&&!ke&&new window.InputEvent("input");const Ue=(e,t)=>e===we.BOTH||e===t?t:we.NO_STATUS;function Je(e=!0){const t=S();w(t)||it(t)||Re(188);const n=t.anchor.getNode(),o=t.focus.getNode(),[r]=nt(n),[l,,s]=nt(o),[i,c,a]=et(s,l,r),u=i[0].length,{startRow:h}=a,{startRow:f}=c;let g=null;if(e){const e=Math.max(f+l.__rowSpan,h+r.__rowSpan)-1,t=i[e],n=Oe();for(let o=0;o<u;o++){const{cell:r,startRow:l}=t[o];if(l+r.__rowSpan-1<=e){const e=t[o].cell.__headerState,r=Ue(e,we.COLUMN);n.append(xe(r).append(d()))}else r.setRowSpan(r.__rowSpan+1)}const o=s.getChildAtIndex(e);Ke(o)||Re(256),o.insertAfter(n),g=n}else{const e=Math.min(f,h),t=i[e],n=Oe();for(let o=0;o<u;o++){const{cell:r,startRow:l}=t[o];if(l===e){const e=t[o].cell.__headerState,r=Ue(e,we.COLUMN);n.append(xe(r).append(d()))}else r.setRowSpan(r.__rowSpan+1)}const o=s.getChildAtIndex(e);Ke(o)||Re(257),o.insertBefore(n),g=n}return g}function Ye(e,t,n=!0,o,r){const l=e.getChildren(),s=[];for(let e=0;e<l.length;e++){const n=l[e];if(Ke(n))for(let e=0;e<o;e++){const e=n.getChildren();if(t>=e.length||t<0)throw new Error("Table column target index out of range");const o=e[t];ve(o)||Re(12);const{left:l,right:i}=Pe(o,r);let c=we.NO_STATUS;(l&&l.hasHeaderState(we.ROW)||i&&i.hasHeaderState(we.ROW))&&(c|=we.ROW);const a=xe(c);a.append(d()),s.push({newTableCell:a,targetCell:o})}}return s.forEach((({newTableCell:e,targetCell:t})=>{n?t.insertAfter(e):t.insertBefore(e)})),e}function qe(e=!0){const t=S();w(t)||it(t)||Re(188);const n=t.anchor.getNode(),o=t.focus.getNode(),[r]=nt(n),[l,,s]=nt(o),[i,c,a]=et(s,l,r),u=i.length,h=e?Math.max(c.startColumn,a.startColumn):Math.min(c.startColumn,a.startColumn),f=e?h+l.__colSpan-1:h-1,g=s.getFirstChild();Ke(g)||Re(120);let m=null;function p(e=we.NO_STATUS){const t=xe(e).append(d());return null===m&&(m=t),t}let C=g;e:for(let e=0;e<u;e++){if(0!==e){const e=C.getNextSibling();Ke(e)||Re(121),C=e}const t=i[e],n=t[f<0?0:f].cell.__headerState,o=Ue(n,we.ROW);if(f<0){Qe(C,p(o));continue}const{cell:r,startColumn:l,startRow:s}=t[f];if(l+r.__colSpan-1<=f){let n=r,l=s,i=f;for(;l!==e&&n.__rowSpan>1;){if(i-=r.__colSpan,!(i>=0)){C.append(p(o));continue e}{const{cell:e,startRow:o}=t[i];n=e,l=o}}n.insertAfter(p(o))}else r.setColSpan(r.__colSpan+1)}null!==m&&Ge(m);const _=s.getColWidths();if(_){const e=[..._],t=f<0?0:f,n=e[t];e.splice(t,0,n),s.setColWidths(e)}return m}function Xe(e,t){const n=e.getChildren();for(let e=0;e<n.length;e++){const o=n[e];if(Ke(o)){const e=o.getChildren();if(t>=e.length||t<0)throw new Error("Table column target index out of range");e[t].remove()}}return e}function je(){const e=S();w(e)||it(e)||Re(188);const[t,n]=e.isBackward()?[e.focus.getNode(),e.anchor.getNode()]:[e.anchor.getNode(),e.focus.getNode()],[o,,r]=nt(t),[l]=nt(n),[s,i,c]=et(r,o,l),{startRow:a}=i,{startRow:u}=c,h=u+l.__rowSpan-1;if(s.length===h-a+1)return void r.remove();const d=s[0].length,f=o.__rowSpan,g=s[h+1],m=r.getChildAtIndex(h+1);for(let e=h;e>=a;e--){for(let t=d-1;t>=0;t--){const{cell:n,startRow:o,startColumn:r}=s[e][t];if(r===t){if(e===a&&o<a){const e=a-o;n.setRowSpan(n.__rowSpan-Math.min(f,n.__rowSpan-e))}if(o>=a&&o+n.__rowSpan-1>h){n.setRowSpan(n.__rowSpan-(h-o+1)),null===m&&Re(122);let r=null;for(let n=0;n<t;n++){const t=g[n],o=t.cell;t.startRow===e+1&&(r=o),o.__colSpan>1&&(n+=o.__colSpan-1)}null===r?Qe(m,n):r.insertAfter(n)}}}const t=r.getChildAtIndex(e);Ke(t)||Re(206,String(e)),t.remove()}if(void 0!==g){const{cell:e}=g[0];Ge(e)}else{const e=s[a-1],{cell:t}=e[0];Ge(t)}}function Ve(){const e=S();w(e)||it(e)||Re(188);const t=e.anchor.getNode(),n=e.focus.getNode(),[o,,r]=nt(t),[l]=nt(n),[s,i,c]=et(r,o,l),{startColumn:a}=i,{startRow:u,startColumn:h}=c,d=Math.min(a,h),f=Math.max(a+o.__colSpan-1,h+l.__colSpan-1),g=f-d+1;if(s[0].length===f-d+1)return r.selectPrevious(),void r.remove();const m=s.length;for(let e=0;e<m;e++)for(let t=d;t<=f;t++){const{cell:n,startColumn:o}=s[e][t];if(o<d){if(t===d){const e=d-o;n.setColSpan(n.__colSpan-Math.min(g,n.__colSpan-e))}}else if(o+n.__colSpan-1>f){if(t===f){const e=f-o+1;n.setColSpan(n.__colSpan-e)}}else n.remove()}const p=s[u],C=a>h?p[a+o.__colSpan]:p[h+l.__colSpan];if(void 0!==C){const{cell:e}=C;Ge(e)}else{const e=h<a?p[h-1]:p[a-1],{cell:t}=e;Ge(t)}const _=r.getColWidths();if(_){const e=[..._];e.splice(d,g),r.setColWidths(e)}}function Ge(e){const t=e.getFirstDescendant();null==t?e.selectStart():t.getParentOrThrow().selectStart()}function Qe(e,t){const n=e.getFirstChild();null!==n?n.insertBefore(t):e.append(t)}function Ze(){const e=S();w(e)||it(e)||Re(188);const t=e.anchor.getNode(),[n,o,r]=nt(t),l=n.__colSpan,s=n.__rowSpan;if(1===l&&1===s)return;const[i,c]=et(r,n,n),{startColumn:a,startRow:u}=c,h=n.__headerState&we.COLUMN,f=Array.from({length:l},((e,t)=>{let n=h;for(let e=0;0!==n&&e<i.length;e++)n&=i[e][t+a].cell.__headerState;return n})),g=n.__headerState&we.ROW,m=Array.from({length:s},((e,t)=>{let n=g;for(let e=0;0!==n&&e<i[0].length;e++)n&=i[t+u][e].cell.__headerState;return n}));if(l>1){for(let e=1;e<l;e++)n.insertAfter(xe(f[e]|m[0]).append(d()));n.setColSpan(1)}if(s>1){let e;for(let t=1;t<s;t++){const n=u+t,r=i[n];e=(e||o).getNextSibling(),Ke(e)||Re(125);let s=null;for(let e=0;e<a;e++){const t=r[e],o=t.cell;t.startRow===n&&(s=o),o.__colSpan>1&&(e+=o.__colSpan-1)}if(null===s)for(let n=l-1;n>=0;n--)Qe(e,xe(f[n]|m[t]).append(d()));else for(let e=l-1;e>=0;e--)s.insertAfter(xe(f[e]|m[t]).append(d()))}n.setRowSpan(1)}}function et(e,t,n){const[o,r,l]=tt(e,t,n);return null===r&&Re(207),null===l&&Re(208),[o,r,l]}function tt(e,t,n){const o=[];let r=null,l=null;function s(e){let t=o[e];return void 0===t&&(o[e]=t=[]),t}const i=e.getChildren();for(let e=0;e<i.length;e++){const o=i[e];Ke(o)||Re(209);const c=s(e);for(let a=o.getFirstChild(),u=0;null!=a;a=a.getNextSibling()){for(ve(a)||Re(147);void 0!==c[u];)u++;const o={cell:a,startColumn:u,startRow:e},{__rowSpan:h,__colSpan:d}=a;for(let t=0;t<h&&!(e+t>=i.length);t++){const n=s(e+t);for(let e=0;e<d;e++)n[u+e]=o}null!==t&&null===r&&t.is(a)&&(r=o),null!==n&&null===l&&n.is(a)&&(l=o)}}return[o,r,l]}function nt(e){let t;if(e instanceof be)t=e;else if("__type"in e){const o=n(e,ve);ve(o)||Re(148),t=o}else{const o=n(e.getNode(),ve);ve(o)||Re(148),t=o}const o=t.getParent();Ke(o)||Re(149);const r=o.getParent();return ln(r)||Re(210),[t,o,r]}function ot(e,t,n){let o,r=Math.min(t.startColumn,n.startColumn),l=Math.min(t.startRow,n.startRow),s=Math.max(t.startColumn+t.cell.__colSpan-1,n.startColumn+n.cell.__colSpan-1),i=Math.max(t.startRow+t.cell.__rowSpan-1,n.startRow+n.cell.__rowSpan-1);do{o=!1;for(let t=0;t<e.length;t++)for(let n=0;n<e[0].length;n++){const c=e[t][n];if(!c)continue;const a=c.startColumn+c.cell.__colSpan-1,u=c.startRow+c.cell.__rowSpan-1,h=c.startColumn<=s&&a>=r,d=c.startRow<=i&&u>=l;if(h&&d){const e=Math.min(r,c.startColumn),t=Math.max(s,a),n=Math.min(l,c.startRow),h=Math.max(i,u);e===r&&t===s&&n===l&&h===i||(r=e,s=t,l=n,i=h,o=!0)}}}while(o);return{maxColumn:s,maxRow:i,minColumn:r,minRow:l}}function rt(e){const[t,,n]=nt(e),o=n.getChildren(),r=o.length,l=o[0].getChildren().length,s=new Array(r);for(let e=0;e<r;e++)s[e]=new Array(l);for(let e=0;e<r;e++){const n=o[e].getChildren();let r=0;for(let o=0;o<n.length;o++){for(;s[e][r];)r++;const l=n[o],i=l.__rowSpan||1,c=l.__colSpan||1;for(let t=0;t<i;t++)for(let n=0;n<c;n++)s[e+t][r+n]=l;if(t===l)return{colSpan:c,columnIndex:r,rowIndex:e,rowSpan:i};r+=c}}return null}function lt(e){const[[t,o,r,l],[s,i,c,a]]=["anchor","focus"].map((t=>{const o=e[t].getNode(),r=n(o,ve);ve(r)||Re(238,t,o.getKey(),o.getType());const l=r.getParent();Ke(l)||Re(239,t);const s=l.getParent();return ln(s)||Re(240,t),[o,r,l,s]}));return l.is(a)||Re(241),{anchorCell:o,anchorNode:t,anchorRow:r,anchorTable:l,focusCell:i,focusNode:s,focusRow:c,focusTable:a}}class st{constructor(e,t,n){this.anchor=t,this.focus=n,t._selection=this,n._selection=this,this._cachedNodes=null,this.dirty=!1,this.tableKey=e}getStartEndPoints(){return[this.anchor,this.focus]}isValid(){return"root"!==this.tableKey&&"root"!==this.anchor.key&&"element"===this.anchor.type&&"root"!==this.focus.key&&"element"===this.focus.type}isBackward(){return this.focus.isBefore(this.anchor)}getCachedNodes(){return this._cachedNodes}setCachedNodes(e){this._cachedNodes=e}is(e){return it(e)&&this.tableKey===e.tableKey&&this.anchor.is(e.anchor)&&this.focus.is(e.focus)}set(e,t,n){this.dirty=this.dirty||e!==this.tableKey||t!==this.anchor.key||n!==this.focus.key,this.tableKey=e,this.anchor.key=t,this.focus.key=n,this._cachedNodes=null}clone(){return new st(this.tableKey,b(this.anchor.key,this.anchor.offset,this.anchor.type),b(this.focus.key,this.focus.offset,this.focus.type))}isCollapsed(){return!1}extract(){return this.getNodes()}insertRawText(e){}insertText(){}hasFormat(e){let t=0;this.getNodes().filter(ve).forEach((e=>{const n=e.getFirstChild();y(n)&&(t|=n.getTextFormat())}));const n=v[e];return!!(t&n)}insertNodes(e){const t=this.focus.getNode();f(t)||Re(151);N(t.select(0,t.getChildrenSize())).insertNodes(e)}getShape(){const{anchorCell:e,focusCell:t}=lt(this),n=rt(e);null===n&&Re(153);const o=rt(t);null===o&&Re(155);const r=Math.min(n.columnIndex,o.columnIndex),l=Math.max(n.columnIndex+n.colSpan-1,o.columnIndex+o.colSpan-1),s=Math.min(n.rowIndex,o.rowIndex),i=Math.max(n.rowIndex+n.rowSpan-1,o.rowIndex+o.rowSpan-1);return{fromX:Math.min(r,l),fromY:Math.min(s,i),toX:Math.max(r,l),toY:Math.max(s,i)}}getNodes(){if(!this.isValid())return[];const e=this._cachedNodes;if(null!==e)return e;const{anchorTable:t,anchorCell:n,focusCell:o}=lt(this),r=o.getParents()[1];if(r!==t){if(t.isParentOf(o)){const e=r.getParent();null==e&&Re(159),this.set(this.tableKey,o.getKey(),e.getKey())}else{const e=t.getParent();null==e&&Re(158),this.set(this.tableKey,e.getKey(),o.getKey())}return this.getNodes()}const[l,s,i]=et(t,n,o),{minColumn:c,maxColumn:a,minRow:u,maxRow:h}=ot(l,s,i),d=new Map([[t.getKey(),t]]);let f=null;for(let e=u;e<=h;e++)for(let t=c;t<=a;t++){const{cell:n}=l[e][t],o=n.getParent();Ke(o)||Re(160),o!==f&&(d.set(o.getKey(),o),f=o),d.has(n.getKey())||ut(n,(e=>{d.set(e.getKey(),e)}))}const g=Array.from(d.values());return x()||(this._cachedNodes=g),g}getTextContent(){const e=this.getNodes().filter((e=>ve(e)));let t="";for(let n=0;n<e.length;n++){const o=e[n],r=o.__parent,l=(e[n+1]||{}).__parent;t+=o.getTextContent()+(l!==r?"\n":"\t")}return t}}function it(e){return e instanceof st}function ct(){const e=b("root",0,"element"),t=b("root",0,"element");return new st("root",e,t)}function at(e,t,n){e.getKey(),t.getKey(),n.getKey();const o=S(),r=it(o)?o.clone():ct();return r.set(e.getKey(),t.getKey(),n.getKey()),r}function ut(e,t){const n=[[e]];for(let e=n.at(-1);void 0!==e&&n.length>0;e=n.at(-1)){const o=e.pop();void 0===o?n.pop():!1!==t(o)&&f(o)&&n.push(o.getChildren())}}function ht(e,t=R()){const n=T(e);ln(n)||Re(231,e);const o=mt(n,t.getElementByKey(e));return null===o&&Re(232,e),{tableElement:o,tableNode:n}}class dt{constructor(e,t){this.isHighlightingCells=!1,this.anchorX=-1,this.anchorY=-1,this.focusX=-1,this.focusY=-1,this.listenersToRemove=new Set,this.tableNodeKey=t,this.editor=e,this.table={columns:0,domRows:[],rows:0},this.tableSelection=null,this.anchorCellNodeKey=null,this.focusCellNodeKey=null,this.anchorCell=null,this.focusCell=null,this.hasHijackedSelectionStyles=!1,this.isSelecting=!1,this.pointerType=null,this.shouldCheckSelection=!1,this.abortController=new AbortController,this.listenerOptions={signal:this.abortController.signal},this.nextFocus=null,this.trackTable()}getTable(){return this.table}removeListeners(){this.abortController.abort("removeListeners"),Array.from(this.listenersToRemove).forEach((e=>e())),this.listenersToRemove.clear()}$lookup(){return ht(this.tableNodeKey,this.editor)}trackTable(){const e=new MutationObserver((e=>{this.editor.getEditorState().read((()=>{let t=!1;for(let n=0;n<e.length;n++){const o=e[n].target.nodeName;if("TABLE"===o||"TBODY"===o||"THEAD"===o||"TR"===o){t=!0;break}}if(!t)return;const{tableNode:n,tableElement:o}=this.$lookup();this.table=vt(n,o)}),{editor:this.editor})}));this.editor.getEditorState().read((()=>{const{tableNode:t,tableElement:n}=this.$lookup();this.table=vt(t,n),e.observe(n,{attributes:!0,childList:!0,subtree:!0})}),{editor:this.editor})}$clearHighlight(){const e=this.editor;this.isHighlightingCells=!1,this.anchorX=-1,this.anchorY=-1,this.focusX=-1,this.focusY=-1,this.tableSelection=null,this.anchorCellNodeKey=null,this.focusCellNodeKey=null,this.anchorCell=null,this.focusCell=null,this.hasHijackedSelectionStyles=!1,this.$enableHighlightStyle();const{tableNode:t,tableElement:n}=this.$lookup();Tt(e,vt(t,n),null),null!==S()&&(F(null),e.dispatchCommand(A,void 0))}$enableHighlightStyle(){const e=this.editor,{tableElement:t}=this.$lookup();o(t,e._config.theme.tableSelection),t.classList.remove("disable-selection"),this.hasHijackedSelectionStyles=!1}$disableHighlightStyle(){const{tableElement:t}=this.$lookup();e(t,this.editor._config.theme.tableSelection),this.hasHijackedSelectionStyles=!0}$updateTableTableSelection(e){if(null!==e){e.tableKey!==this.tableNodeKey&&Re(233,e.tableKey,this.tableNodeKey);const t=this.editor;this.tableSelection=e,this.isHighlightingCells=!0,this.$disableHighlightStyle(),this.updateDOMSelection(),Tt(t,this.table,this.tableSelection)}else this.$clearHighlight()}setShouldCheckSelection(){this.shouldCheckSelection=!0}getAndClearShouldCheckSelection(){return!!this.shouldCheckSelection&&(this.shouldCheckSelection=!1,!0)}setNextFocus(e){this.nextFocus=e}getAndClearNextFocus(){const{nextFocus:e}=this;return null!==e&&(this.nextFocus=null),e}updateDOMSelection(){if(null!==this.anchorCell&&null!==this.focusCell){const e=O(this.editor._window);e&&e.rangeCount>0&&e.removeAllRanges()}}$setFocusCellForSelection(e,t=!1){const n=this.editor,{tableNode:o}=this.$lookup(),r=e.x,l=e.y;if(this.focusCell=e,this.isHighlightingCells||this.anchorX===r&&this.anchorY===l&&!t){if(r===this.focusX&&l===this.focusY)return!1}else this.isHighlightingCells=!0,this.$disableHighlightStyle();if(this.focusX=r,this.focusY=l,this.isHighlightingCells){const t=Yt(o,e.elem);if(null!=this.tableSelection&&null!=this.anchorCellNodeKey&&null!==t)return this.focusCellNodeKey=t.getKey(),this.tableSelection=at(o,this.$getAnchorTableCellOrThrow(),t),F(this.tableSelection),n.dispatchCommand(A,void 0),Tt(n,this.table,this.tableSelection),!0}return!1}$getAnchorTableCell(){return this.anchorCellNodeKey?T(this.anchorCellNodeKey):null}$getAnchorTableCellOrThrow(){const e=this.$getAnchorTableCell();return null===e&&Re(234),e}$getFocusTableCell(){return this.focusCellNodeKey?T(this.focusCellNodeKey):null}$getFocusTableCellOrThrow(){const e=this.$getFocusTableCell();return null===e&&Re(235),e}$setAnchorCellForSelection(e){this.isHighlightingCells=!1,this.anchorCell=e,this.anchorX=e.x,this.anchorY=e.y;const{tableNode:t}=this.$lookup(),n=Yt(t,e.elem);if(null!==n){const e=n.getKey();this.tableSelection=null!=this.tableSelection?this.tableSelection.clone():ct(),this.anchorCellNodeKey=e}}$formatCells(e){const t=S();it(t)||Re(236);const n=K(),o=n.anchor,r=n.focus,l=t.getNodes().filter(ve);l.length>0||Re(237);const s=l[0].getFirstChild(),i=y(s)?s.getFormatFlags(e,null):null;l.forEach((t=>{o.set(t.getKey(),0,"element"),r.set(t.getKey(),t.getChildrenSize(),"element"),n.formatText(e,i)})),F(t),this.editor.dispatchCommand(A,void 0)}$clearText(){const{editor:e}=this,t=T(this.tableNodeKey);if(!ln(t))throw new Error("Expected TableNode.");const n=S();it(n)||Re(253);const o=n.getNodes().filter(ve);if(o.length===this.table.columns*this.table.rows){t.selectPrevious();const n=t.getParent();return t.remove(),void(E(n)&&n.isEmpty()&&e.dispatchCommand(k,void 0))}o.forEach((e=>{if(f(e)){const t=d(),n=_();t.append(n),e.append(t),e.getChildren().forEach((e=>{e!==t&&e.remove()}))}})),Tt(e,this.table,null),F(null),e.dispatchCommand(A,void 0)}}const ft="__lexicalTableSelection",gt=e=>!(1&~e.buttons);function mt(e,t){if(!t)return t;const n="TABLE"===t.nodeName?t:e.getDOMSlot(t).element;return"TABLE"!==n.nodeName&&Re(245,t.nodeName),n}function pt(e){return e._window}function Ct(e,t){for(let n=t,o=null;null!==n;n=n.getParent()){if(e.is(n))return o;ve(n)&&(o=n)}return null}const _t=[[X,"down"],[j,"up"],[V,"backward"],[G,"forward"]],St=[Q,Z,ee],wt=[te,ne];function bt(e,t,o,l){const s=o.getRootElement(),i=pt(o);null!==s&&null!==i||Re(246);const c=new dt(o,e.getKey()),a=mt(e,t);!function(e,t){null!==yt(e)&&Re(205);e[ft]=t}(a,c),c.listenersToRemove.add((()=>function(e,t){yt(e)===t&&delete e[ft]}(a,c)));const u=t=>{if(c.pointerType=t.pointerType,0!==t.button||!oe(t.target)||!i)return;const n=Nt(t.target);null!==n&&o.update((()=>{const o=U();if(Me&&t.shiftKey&&Mt(o,e)&&(w(o)||it(o))){const r=o.anchor.getNode(),l=Ct(e,o.anchor.getNode());if(l)c.$setAnchorCellForSelection(Jt(c,l)),c.$setFocusCellForSelection(n),Dt(t);else{(e.isBefore(r)?e.selectStart():e.selectEnd()).anchor.set(o.anchor.key,o.anchor.offset,o.anchor.type)}}else c.$setAnchorCellForSelection(n)})),(()=>{if(c.isSelecting)return;const e=()=>{c.isSelecting=!1,i.removeEventListener("pointerup",e),i.removeEventListener("pointermove",t)},t=n=>{if(!gt(n)&&c.isSelecting)return c.isSelecting=!1,i.removeEventListener("pointerup",e),void i.removeEventListener("pointermove",t);if(!oe(n.target))return;let r=null;const l=!(Me||a.contains(n.target));if(l)r=xt(a,n.target);else for(const e of document.elementsFromPoint(n.clientX,n.clientY))if(r=xt(a,e),r)break;!r||null!==c.focusCell&&r.elem===c.focusCell.elem||(c.setNextFocus({focusCell:r,override:l}),o.dispatchCommand(A,void 0))};c.isSelecting=!0,i.addEventListener("pointerup",e,c.listenerOptions),i.addEventListener("pointermove",t,c.listenerOptions)})()};a.addEventListener("pointerdown",u,c.listenerOptions),c.listenersToRemove.add((()=>{a.removeEventListener("pointerdown",u)}));const h=e=>{if(e.detail>=3&&oe(e.target)){null!==Nt(e.target)&&e.preventDefault()}};a.addEventListener("mousedown",h,c.listenerOptions),c.listenersToRemove.add((()=>{a.removeEventListener("mousedown",h)}));const g=e=>{const t=e.target;0===e.button&&oe(t)&&o.update((()=>{const e=S();it(e)&&e.tableKey===c.tableNodeKey&&s.contains(t)&&c.$clearHighlight()}))};i.addEventListener("pointerdown",g,c.listenerOptions),c.listenersToRemove.add((()=>{i.removeEventListener("pointerdown",g)}));for(const[t,n]of _t)c.listenersToRemove.add(o.registerCommand(t,(t=>Pt(o,t,n,e,c)),M));c.listenersToRemove.add(o.registerCommand($,(t=>{const n=S();if(it(n)){const o=Ct(e,n.focus.getNode());if(null!==o)return Dt(t),o.selectEnd(),!0}return!1}),M));const p=t=>()=>{const o=S();if(!Mt(o,e))return!1;if(it(o))return c.$clearText(),!0;if(w(o)){if(!ve(Ct(e,o.anchor.getNode())))return!1;const r=o.anchor.getNode(),l=o.focus.getNode(),s=e.isParentOf(r),i=e.isParentOf(l);if(s&&!i||i&&!s)return c.$clearText(),!0;const a=n(o.anchor.getNode(),(e=>f(e))),u=a&&n(a,(e=>f(e)&&ve(e.getParent())));if(!f(u)||!f(a))return!1;if(t===Z&&null===u.getPreviousSibling())return!0}return!1};for(const e of St)c.listenersToRemove.add(o.registerCommand(e,p(e),L));const C=t=>{const n=S();if(!it(n)&&!w(n))return!1;const o=e.isParentOf(n.anchor.getNode());if(o!==e.isParentOf(n.focus.getNode())){const t=o?"anchor":"focus",r=o?"focus":"anchor",{key:l,offset:s,type:i}=n[r];return e[n[t].isBefore(n[r])?"selectPrevious":"selectNext"]()[r].set(l,s,i),!1}return!!Mt(n,e)&&(!!it(n)&&(t&&(t.preventDefault(),t.stopPropagation()),c.$clearText(),!0))};for(const e of wt)c.listenersToRemove.add(o.registerCommand(e,C,L));return c.listenersToRemove.add(o.registerCommand(z,(e=>{const t=S();if(t){if(!it(t)&&!w(t))return!1;Ce(o,r(e,ClipboardEvent)?e:null,_e(t));const n=C(e);return w(t)?(t.removeText(),!0):n}return!1}),L)),c.listenersToRemove.add(o.registerCommand(W,(t=>{const o=S();if(!Mt(o,e))return!1;if(it(o))return c.$formatCells(t),!0;if(w(o)){const e=n(o.anchor.getNode(),(e=>ve(e)));if(!ve(e))return!1}return!1}),L)),c.listenersToRemove.add(o.registerCommand(H,(t=>{const n=S();if(!it(n)||!Mt(n,e))return!1;const o=n.anchor.getNode(),r=n.focus.getNode();if(!ve(o)||!ve(r))return!1;if(function(e,t){if(it(e)){const n=e.anchor.getNode(),o=e.focus.getNode();if(t&&n&&o){const[e]=et(t,n,o);return n.getKey()===e[0][0].cell.getKey()&&o.getKey()===e[e.length-1].at(-1).cell.getKey()}}return!1}(n,e))return e.setFormat(t),!0;const[l,s,i]=et(e,o,r),c=Math.max(s.startRow+s.cell.__rowSpan-1,i.startRow+i.cell.__rowSpan-1),a=Math.max(s.startColumn+s.cell.__colSpan-1,i.startColumn+i.cell.__colSpan-1),u=Math.min(s.startRow,i.startRow),h=Math.min(s.startColumn,i.startColumn),d=new Set;for(let e=u;e<=c;e++)for(let n=h;n<=a;n++){const o=l[e][n].cell;if(d.has(o))continue;d.add(o),o.setFormat(t);const r=o.getChildren();for(let e=0;e<r.length;e++){const n=r[e];f(n)&&!n.isInline()&&n.setFormat(t)}}return!0}),L)),c.listenersToRemove.add(o.registerCommand(B,(t=>{const r=S();if(!Mt(r,e))return!1;if(it(r))return c.$clearHighlight(),!1;if(w(r)){const l=n(r.anchor.getNode(),(e=>ve(e)));if(!ve(l))return!1;if("string"==typeof t){const n=Ut(o,r,e);if(n)return It(n,e,[_(t)]),!0}}return!1}),L)),l&&c.listenersToRemove.add(o.registerCommand(P,(t=>{const o=S();if(!w(o)||!o.isCollapsed()||!Mt(o,e))return!1;const r=Wt(o.anchor.getNode());return!(null===r||!e.is(Ht(r)))&&(Dt(t),function(e,t){const o="next"===t?"getNextSibling":"getPreviousSibling",r="next"===t?"getFirstChild":"getLastChild",l=e[o]();if(f(l))return l.selectEnd();const s=n(e,Ke);null===s&&Re(247);for(let e=s[o]();Ke(e);e=e[o]()){const t=e[r]();if(f(t))return t.selectEnd()}const i=n(s,ln);null===i&&Re(248);"next"===t?i.selectNext():i.selectPrevious()}(r,t.shiftKey?"previous":"next"),!0)}),L)),c.listenersToRemove.add(o.registerCommand(D,(t=>e.isSelected()),M)),c.listenersToRemove.add(o.registerCommand(I,(e=>{const{nodes:t,selection:o}=e,r=o.getStartEndPoints(),l=it(o),s=w(o)&&null!==n(o.anchor.getNode(),(e=>ve(e)))&&null!==n(o.focus.getNode(),(e=>ve(e)))||l;if(1!==t.length||!ln(t[0])||!s||null===r)return!1;const[i]=r,c=t[0],a=c.getChildren(),u=c.getFirstChildOrThrow().getChildrenSize(),h=c.getChildrenSize(),f=n(i.getNode(),(e=>ve(e))),g=f&&n(f,(e=>Ke(e))),p=g&&n(g,(e=>ln(e)));if(!ve(f)||!Ke(g)||!ln(p))return!1;const C=g.getIndexWithinParent(),_=Math.min(p.getChildrenSize()-1,C+h-1),S=f.getIndexWithinParent(),b=Math.min(g.getChildrenSize()-1,S+u-1),y=Math.min(S,b),N=Math.min(C,_),x=Math.max(S,b),v=Math.max(C,_),T=p.getChildren();let R=0;for(let e=N;e<=v;e++){const t=T[e];if(!Ke(t))return!1;const n=a[R];if(!Ke(n))return!1;const o=t.getChildren(),r=n.getChildren();let l=0;for(let e=y;e<=x;e++){const t=o[e];if(!ve(t))return!1;const n=r[l];if(!ve(n))return!1;const s=t.getChildren();n.getChildren().forEach((e=>{if(m(e)){d().append(e),t.append(e)}else t.append(e)})),s.forEach((e=>e.remove())),l++}R++}return!0}),L)),c.listenersToRemove.add(o.registerCommand(A,(()=>{const t=S(),r=U(),l=c.getAndClearNextFocus();if(null!==l){const{focusCell:n}=l;if(it(t)&&t.tableKey===c.tableNodeKey)return(n.x!==c.focusX||n.y!==c.focusY)&&(c.$setFocusCellForSelection(n),!0);if(n!==c.anchorCell&&Mt(t,e))return c.$setFocusCellForSelection(n),!0}if(c.getAndClearShouldCheckSelection()&&w(r)&&w(t)&&t.isCollapsed()){const o=t.anchor.getNode(),r=e.getFirstChild(),l=Wt(o);if(null!==l&&Ke(r)){const t=r.getFirstChild();if(ve(t)&&e.is(n(l,(n=>n.is(e)||n.is(t)))))return t.selectStart(),!0}}if(w(t)){const{anchor:n,focus:l}=t,s=n.getNode(),i=l.getNode(),a=Wt(s),u=Wt(i),h=!(!a||!e.is(Ht(a))),d=!(!u||!e.is(Ht(u))),f=h!==d,g=h&&d,m=t.isBackward();if(f){const n=t.clone();if(d){const[t]=et(e,u,u),o=t[0][0].cell,r=t[t.length-1].at(-1).cell;n.focus.set(m?o.getKey():r.getKey(),m?o.getChildrenSize():r.getChildrenSize(),"element")}else if(h){const[t]=et(e,a,a),o=t[0][0].cell,r=t[t.length-1].at(-1).cell;n.anchor.set(m?r.getKey():o.getKey(),m?r.getChildrenSize():0,"element")}F(n),Ft(o,c)}else if(g&&(a.is(u)||(c.$setAnchorCellForSelection(Jt(c,a)),c.$setFocusCellForSelection(Jt(c,u),!0)),"touch"===c.pointerType&&t.isCollapsed()&&w(r)&&r.isCollapsed())){const e=Wt(r.anchor.getNode());e&&!e.is(u)&&(c.$setAnchorCellForSelection(Jt(c,e)),c.$setFocusCellForSelection(Jt(c,u),!0),c.pointerType=null)}}else if(t&&it(t)&&t.is(r)&&t.tableKey===e.getKey()){const n=O(i);if(n&&n.anchorNode&&n.focusNode){const r=J(n.focusNode),l=r&&!e.isParentOf(r),s=J(n.anchorNode),i=s&&e.isParentOf(s);if(l&&i&&n.rangeCount>0){const r=Y(n,o);r&&(r.anchor.set(e.getKey(),t.isBackward()?e.getChildrenSize():0,"element"),n.removeAllRanges(),F(r))}}}return t&&!t.is(r)&&(it(t)||it(r))&&c.tableSelection&&!c.tableSelection.is(r)?(it(t)&&t.tableKey===c.tableNodeKey?c.$updateTableTableSelection(t):!it(t)&&it(r)&&r.tableKey===c.tableNodeKey&&c.$updateTableTableSelection(null),!1):(c.hasHijackedSelectionStyles&&!e.isSelected()?function(e,t){t.$enableHighlightStyle(),Rt(t.table,(t=>{const n=t.elem;t.highlighted=!1,zt(e,t),n.getAttribute("style")||n.removeAttribute("style")}))}(o,c):!c.hasHijackedSelectionStyles&&e.isSelected()&&Ft(o,c),!1)}),L)),c.listenersToRemove.add(o.registerCommand(k,(()=>{const t=S();if(!w(t)||!t.isCollapsed()||!Mt(t,e))return!1;const n=Ut(o,t,e);return!!n&&(It(n,e),!0)}),L)),c}function yt(e){return e[ft]||null}function Nt(e){let t=e;for(;null!=t;){const e=t.nodeName;if("TD"===e||"TH"===e){const e=t._cell;return void 0===e?null:e}t=t.parentNode}return null}function xt(e,t){if(!e.contains(t))return null;let n=null;for(let o=t;null!=o;o=o.parentNode){if(o===e)return n;const t=o.nodeName;"TD"!==t&&"TH"!==t||(n=o._cell||null)}return null}function vt(e,t){const n=[],o={columns:0,domRows:n,rows:0};let r=mt(e,t).querySelector("tr"),l=0,s=0;for(n.length=0;null!=r;){const e=r.nodeName;if("TD"===e||"TH"===e){const e={elem:r,hasBackgroundColor:""!==r.style.backgroundColor,highlighted:!1,x:l,y:s};r._cell=e;let t=n[s];void 0===t&&(t=n[s]=[]),t[l]=e}else{const e=r.firstChild;if(null!=e){r=e;continue}}const t=r.nextSibling;if(null!=t){l++,r=t;continue}const o=r.parentNode;if(null!=o){const e=o.nextSibling;if(null==e)break;s++,l=0,r=e}}return o.columns=l+1,o.rows=s+1,o}function Tt(e,t,n){const o=new Set(n?n.getNodes():[]);Rt(t,((t,n)=>{const r=t.elem;o.has(n)?(t.highlighted=!0,Lt(e,t)):(t.highlighted=!1,zt(e,t),r.getAttribute("style")||r.removeAttribute("style"))}))}function Rt(e,t){const{domRows:n}=e;for(let e=0;e<n.length;e++){const o=n[e];if(o)for(let n=0;n<o.length;n++){const r=o[n];if(!r)continue;const l=J(r.elem);null!==l&&t(r,l,{x:n,y:e})}}}function Ft(e,t){t.$disableHighlightStyle(),Rt(t.table,(t=>{t.highlighted=!0,Lt(e,t)}))}const At=(e,t,n,o,r)=>{const l="forward"===r;switch(r){case"backward":case"forward":return n!==(l?e.table.columns-1:0)?$t(t.getCellNodeFromCordsOrThrow(n+(l?1:-1),o,e.table),l):o!==(l?e.table.rows-1:0)?$t(t.getCellNodeFromCordsOrThrow(l?0:e.table.columns-1,o+(l?1:-1),e.table),l):l?t.selectNext():t.selectPrevious(),!0;case"up":return 0!==o?$t(t.getCellNodeFromCordsOrThrow(n,o-1,e.table),!1):t.selectPrevious(),!0;case"down":return o!==e.table.rows-1?$t(t.getCellNodeFromCordsOrThrow(n,o+1,e.table),!0):t.selectNext(),!0;default:return!1}};function Ot(e,t){let n,o;if(t.startColumn===e.minColumn)n="minColumn";else{if(t.startColumn+t.cell.__colSpan-1!==e.maxColumn)return null;n="maxColumn"}if(t.startRow===e.minRow)o="minRow";else{if(t.startRow+t.cell.__rowSpan-1!==e.maxRow)return null;o="maxRow"}return[n,o]}function Kt([e,t]){return["minColumn"===e?"maxColumn":"minColumn","minRow"===t?"maxRow":"minRow"]}function Et(e,t,[n,o]){const r=t[o],l=e[r];void 0===l&&Re(250,o,String(r));const s=t[n],i=l[s];return void 0===i&&Re(250,n,String(s)),i}function kt(e,t,n,o,r){const l=ot(t,n,o),s=function(e,t){const{minColumn:n,maxColumn:o,minRow:r,maxRow:l}=t;let s=1,i=1,c=1,a=1;const u=e[r],h=e[l];for(let e=n;e<=o;e++)s=Math.max(s,u[e].cell.__rowSpan),a=Math.max(a,h[e].cell.__rowSpan);for(let t=r;t<=l;t++)i=Math.max(i,e[t][n].cell.__colSpan),c=Math.max(c,e[t][o].cell.__colSpan);return{bottomSpan:a,leftSpan:i,rightSpan:c,topSpan:s}}(t,l),{topSpan:i,leftSpan:c,bottomSpan:a,rightSpan:u}=s,h=function(e,t){const n=Ot(e,t);return null===n&&Re(249,t.cell.getKey()),n}(l,n),[d,f]=Kt(h);let g=l[d],m=l[f];"forward"===r?g+="maxColumn"===d?1:c:"backward"===r?g-="minColumn"===d?1:u:"down"===r?m+="maxRow"===f?1:i:"up"===r&&(m-="minRow"===f?1:a);const p=t[m];if(void 0===p)return!1;const C=p[g];if(void 0===C)return!1;const[_,S]=function(e,t,n){const o=ot(e,t,n),r=Ot(o,t);if(r)return[Et(e,o,r),Et(e,o,Kt(r))];const l=Ot(o,n);if(l)return[Et(e,o,Kt(l)),Et(e,o,l)];const s=["minColumn","minRow"];return[Et(e,o,s),Et(e,o,Kt(s))]}(t,n,C),w=Jt(e,_.cell),b=Jt(e,S.cell);return e.$setAnchorCellForSelection(w),e.$setFocusCellForSelection(b,!0),!0}function Mt(e,t){if(w(e)||it(e)){const n=t.isParentOf(e.anchor.getNode()),o=t.isParentOf(e.focus.getNode());return n&&o}return!1}function $t(e,t){t?e.selectStart():e.selectEnd()}function Lt(t,n){const o=n.elem,r=t._config.theme;ve(J(o))||Re(131),e(o,r.tableCellSelected)}function zt(e,t){const n=t.elem;ve(J(n))||Re(131);const r=e._config.theme;o(n,r.tableCellSelected)}function Wt(e){const t=n(e,ve);return ve(t)?t:null}function Ht(e){const t=n(e,ln);return ln(t)?t:null}function Bt(e,t,o,r,l,s,i){const c=re(o.focus,l?"previous":"next");if(le(c))return!1;let a=c;for(const e of se(c).iterNodeCarets("shadowRoot")){if(!ie(e)||!f(e.origin))return!1;a=e}const u=a.getParentAtCaret();if(!ve(u))return!1;const h=u,d=function(e){for(const t of se(e).iterNodeCarets("root")){const{origin:n}=t;if(ve(n)){if(de(t))return fe(n,e.direction)}else if(!Ke(n))break}return null}(ce(h,a.direction)),g=n(h,ln);if(!g||!g.is(s))return!1;const m=e.getElementByKey(h.getKey()),p=Nt(m);if(!m||!p)return!1;const C=nn(e,g);if(i.table=C,d)if("extend"===r){const t=Nt(e.getElementByKey(d.origin.getKey()));if(!t)return!1;i.$setAnchorCellForSelection(p),i.$setFocusCellForSelection(t,!0)}else{const e=ue(d);ae(o.anchor,e),ae(o.focus,e)}else if("extend"===r)i.$setAnchorCellForSelection(p),i.$setFocusCellForSelection(p,!0);else{const e=function(e){const t=he(e);return de(t)?ue(t):e}(ce(g,c.direction));ae(o.anchor,e),ae(o.focus,e)}return Dt(t),!0}function Pt(e,t,o,r,l){if(("up"===o||"down"===o)&&function(e){const t=e.getRootElement();if(!t)return!1;return t.hasAttribute("aria-controls")&&"typeahead-menu"===t.getAttribute("aria-controls")}(e))return!1;const s=S();if(!Mt(s,r)){if(w(s)){if("backward"===o){if(s.focus.offset>0)return!1;const e=function(e){for(let t=e,n=e;null!==n;t=n,n=n.getParent())if(f(n)){if(n!==t&&n.getFirstChild()!==t)return null;if(!n.isInline())return n}return null}(s.focus.getNode());if(!e)return!1;const n=e.getPreviousSibling();return!!ln(n)&&(Dt(t),t.shiftKey?s.focus.set(n.getParentOrThrow().getKey(),n.getIndexWithinParent(),"element"):n.selectEnd(),!0)}if(t.shiftKey&&("up"===o||"down"===o)){const e=s.focus.getNode();if(!s.isCollapsed()&&("up"===o&&!s.isBackward()||"down"===o&&s.isBackward())){let l=n(e,(e=>ln(e)));if(ve(l)&&(l=n(l,ln)),l!==r)return!1;if(!l)return!1;const i="down"===o?l.getNextSibling():l.getPreviousSibling();if(!i)return!1;let c=0;"up"===o&&f(i)&&(c=i.getChildrenSize());let a=i;if("up"===o&&f(i)){const e=i.getLastChild();a=e||i,c=m(a)?a.getTextContentSize():0}const u=s.clone();return u.focus.set(a.getKey(),c,m(a)?"text":"element"),F(u),Dt(t),!0}if(q(e)){const e="up"===o?s.getNodes()[s.getNodes().length-1]:s.getNodes()[0];if(e){if(null!==Ct(r,e)){const e=r.getFirstDescendant(),t=r.getLastDescendant();if(!e||!t)return!1;const[n]=nt(e),[o]=nt(t),s=r.getCordsFromCellNode(n,l.table),i=r.getCordsFromCellNode(o,l.table),c=r.getDOMCellFromCordsOrThrow(s.x,s.y,l.table),a=r.getDOMCellFromCordsOrThrow(i.x,i.y,l.table);return l.$setAnchorCellForSelection(c),l.$setFocusCellForSelection(a,!0),!0}}return!1}{let r=n(e,(e=>f(e)&&!e.isInline()));if(ve(r)&&(r=n(r,ln)),!r)return!1;const i="down"===o?r.getNextSibling():r.getPreviousSibling();if(ln(i)&&l.tableNodeKey===i.getKey()){const e=i.getFirstDescendant(),n=i.getLastDescendant();if(!e||!n)return!1;const[r]=nt(e),[l]=nt(n),c=s.clone();return c.focus.set(("up"===o?r:l).getKey(),"up"===o?0:l.getChildrenSize(),"element"),Dt(t),F(c),!0}}}}return"down"===o&&Zt(e)&&l.setShouldCheckSelection(),!1}if(w(s)){if("backward"===o||"forward"===o){return Bt(e,t,s,t.shiftKey?"extend":"move","backward"===o,r,l)}if(s.isCollapsed()){const{anchor:i,focus:c}=s,a=n(i.getNode(),ve),u=n(c.getNode(),ve);if(!ve(a)||!a.is(u))return!1;const h=Ht(a);if(h!==r&&null!=h){const n=mt(h,e.getElementByKey(h.getKey()));if(null!=n)return l.table=vt(h,n),Pt(e,t,o,h,l)}const d=e.getElementByKey(a.__key),f=e.getElementByKey(i.key);if(null==f||null==d)return!1;let g;if("element"===i.type)g=f.getBoundingClientRect();else{const t=O(pt(e));if(null===t||0===t.rangeCount)return!1;g=t.getRangeAt(0).getBoundingClientRect()}const m="up"===o?a.getFirstChild():a.getLastChild();if(null==m)return!1;const p=e.getElementByKey(m.__key);if(null==p)return!1;const C=p.getBoundingClientRect();if("up"===o?C.top>g.top-g.height:g.bottom+g.height>C.bottom){Dt(t);const e=r.getCordsFromCellNode(a,l.table);if(!t.shiftKey)return At(l,r,e.x,e.y,o);{const t=r.getDOMCellFromCordsOrThrow(e.x,e.y,l.table);l.$setAnchorCellForSelection(t),l.$setFocusCellForSelection(t,!0)}return!0}}}else if(it(s)){const{anchor:i,focus:c}=s,a=n(i.getNode(),ve),u=n(c.getNode(),ve),[h]=s.getNodes();ln(h)||Re(251);const d=mt(h,e.getElementByKey(h.getKey()));if(!ve(a)||!ve(u)||!ln(h)||null==d)return!1;l.$updateTableTableSelection(s);const f=vt(h,d),g=r.getCordsFromCellNode(a,f),m=r.getDOMCellFromCordsOrThrow(g.x,g.y,f);if(l.$setAnchorCellForSelection(m),Dt(t),t.shiftKey){const[e,t,n]=et(r,a,u);return kt(l,e,t,n,o)}return u.selectEnd(),!0}return!1}function Dt(e){e.preventDefault(),e.stopImmediatePropagation(),e.stopPropagation()}function It(e,t,n){const o=d();"first"===e?t.insertBefore(o):t.insertAfter(o),o.append(...n||[]),o.selectEnd()}function Ut(e,t,o){const r=o.getParent();if(!r)return;const l=O(pt(e));if(!l)return;const s=l.anchorNode,i=e.getElementByKey(r.getKey()),c=mt(o,e.getElementByKey(o.getKey()));if(!s||!i||!c||!i.contains(s)||c.contains(s))return;const a=n(t.anchor.getNode(),(e=>ve(e)));if(!a)return;const u=n(a,(e=>ln(e)));if(!ln(u)||!u.is(o))return;const[h,d]=et(o,a,a),f=h[0][0],g=h[h.length-1][h[0].length-1],{startRow:m,startColumn:p}=d,C=m===f.startRow&&p===f.startColumn,_=m===g.startRow&&p===g.startColumn;return C?"first":_?"last":void 0}function Jt(e,t){const{tableNode:n}=e.$lookup(),o=n.getCordsFromCellNode(t,e.table);return n.getDOMCellFromCordsOrThrow(o.x,o.y,e.table)}function Yt(e,t,n){return Ct(e,J(t,n))}function qt(e,t,n,o){const r=e.querySelector("colgroup");if(!r)return;const l=[];for(let e=0;e<n;e++){const t=document.createElement("col"),n=o&&o[e];n&&(t.style.width=`${n}px`),l.push(t)}r.replaceChildren(...l)}function Xt(t,n,r){r?(e(t,n.theme.tableRowStriping),t.setAttribute("data-lexical-row-striping","true")):(o(t,n.theme.tableRowStriping),t.removeAttribute("data-lexical-row-striping"))}function jt(t,n,r){r>0?(e(t,n.theme.tableFrozenColumn),t.setAttribute("data-lexical-frozen-column","true")):(o(t,n.theme.tableFrozenColumn),t.removeAttribute("data-lexical-frozen-column"))}function Vt(t,n,r){r>0?(e(t,n.theme.tableFrozenRow),t.setAttribute("data-lexical-frozen-row","true")):(o(t,n.theme.tableFrozenRow),t.removeAttribute("data-lexical-frozen-row"))}function Gt(t,n,r){if(!n.theme.tableAlignment)return;const l=[],s=[];for(const e of["center","right"]){const t=n.theme.tableAlignment[e];t&&(e===r?s:l).push(t)}o(t,...l),e(t,...s)}const Qt=new WeakSet;function Zt(e=R()){return Qt.has(e)}function en(e,t){t?Qt.add(e):Qt.delete(e)}class tn extends u{static getType(){return"table"}getColWidths(){return this.getLatest().__colWidths}setColWidths(e){const t=this.getWritable();return t.__colWidths=e,t}static clone(e){return new tn(e.__key)}afterCloneFrom(e){super.afterCloneFrom(e),this.__colWidths=e.__colWidths,this.__rowStriping=e.__rowStriping,this.__frozenColumnCount=e.__frozenColumnCount,this.__frozenRowCount=e.__frozenRowCount}static importDOM(){return{table:e=>({conversion:on,priority:1})}}static importJSON(e){return rn().updateFromJSON(e)}updateFromJSON(e){return super.updateFromJSON(e).setRowStriping(e.rowStriping||!1).setFrozenColumns(e.frozenColumnCount||0).setFrozenRows(e.frozenRowCount||0).setColWidths(e.colWidths)}constructor(e){super(e),this.__rowStriping=!1,this.__frozenColumnCount=0,this.__frozenRowCount=0}exportJSON(){return{...super.exportJSON(),colWidths:this.getColWidths(),frozenColumnCount:this.__frozenColumnCount?this.__frozenColumnCount:void 0,frozenRowCount:this.__frozenRowCount?this.__frozenRowCount:void 0,rowStriping:this.__rowStriping?this.__rowStriping:void 0}}extractWithChild(e,t,n){return"html"===n}getDOMSlot(e){const t="TABLE"!==e.nodeName&&e.querySelector("table")||e;return"TABLE"!==t.nodeName&&Re(229),super.getDOMSlot(t).withAfter(t.querySelector("colgroup"))}createDOM(t,n){const o=document.createElement("table");this.__style&&(o.style.cssText=this.__style);const r=document.createElement("colgroup");if(o.appendChild(r),qt(o,0,this.getColumnCount(),this.getColWidths()),ge(r),e(o,t.theme.table),Gt(o,t,this.getFormatType()),this.__frozenColumnCount&&jt(o,t,this.__frozenColumnCount),this.__frozenRowCount&&Vt(o,t,this.__frozenRowCount),this.__rowStriping&&Xt(o,t,!0),Zt(n)){const n=document.createElement("div"),r=t.theme.tableScrollableWrapper;return r?e(n,r):n.style.cssText="overflow-x: auto;",n.appendChild(o),n}return o}updateDOM(e,t,n){e.__rowStriping!==this.__rowStriping&&Xt(t,n,this.__rowStriping),e.__frozenColumnCount!==this.__frozenColumnCount&&jt(t,n,this.__frozenColumnCount),e.__frozenRowCount!==this.__frozenRowCount&&Vt(t,n,this.__frozenRowCount),qt(t,0,this.getColumnCount(),this.getColWidths());const o=this.getDOMSlot(t).element;return e.__style!==this.__style&&(o.style.cssText=this.__style),Gt(o,n,this.getFormatType()),!1}exportDOM(e){const t=super.exportDOM(e),{element:n}=t;return{after:n=>{if(t.after&&(n=t.after(n),this.__format&&Gt(n,e._config,this.getFormatType())),l(n)&&"TABLE"!==n.nodeName&&(n=n.querySelector("table")),!l(n))return null;const[o]=tt(this,null,null),r=new Map;for(const e of o)for(const t of e){const e=t.cell.getKey();r.has(e)||r.set(e,{colSpan:t.cell.getColSpan(),startColumn:t.startColumn})}const s=new Set;for(const e of n.querySelectorAll(":scope > tr > [data-temporary-table-cell-lexical-key]")){const t=e.getAttribute("data-temporary-table-cell-lexical-key");if(t){const n=r.get(t);if(e.removeAttribute("data-temporary-table-cell-lexical-key"),n){r.delete(t);for(let e=0;e<n.colSpan;e++)s.add(e+n.startColumn)}}}const i=n.querySelector(":scope > colgroup");if(i){const e=Array.from(n.querySelectorAll(":scope > colgroup > col")).filter(((e,t)=>s.has(t)));i.replaceChildren(...e)}const c=n.querySelectorAll(":scope > tr");if(c.length>0){const e=document.createElement("tbody");for(const t of c)e.appendChild(t);n.append(e)}return n},element:l(n)&&"TABLE"!==n.nodeName?n.querySelector("table"):n}}canBeEmpty(){return!1}isShadowRoot(){return!0}getCordsFromCellNode(e,t){const{rows:n,domRows:o}=t;for(let t=0;t<n;t++){const n=o[t];if(null!=n)for(let o=0;o<n.length;o++){const r=n[o];if(null==r)continue;const{elem:l}=r,s=Yt(this,l);if(null!==s&&e.is(s))return{x:o,y:t}}}throw new Error("Cell not found in table.")}getDOMCellFromCords(e,t,n){const{domRows:o}=n,r=o[t];if(null==r)return null;const l=r[e<r.length?e:r.length-1];return null==l?null:l}getDOMCellFromCordsOrThrow(e,t,n){const o=this.getDOMCellFromCords(e,t,n);if(!o)throw new Error("Cell not found at cords.");return o}getCellNodeFromCords(e,t,n){const o=this.getDOMCellFromCords(e,t,n);if(null==o)return null;const r=J(o.elem);return ve(r)?r:null}getCellNodeFromCordsOrThrow(e,t,n){const o=this.getCellNodeFromCords(e,t,n);if(!o)throw new Error("Node at cords not TableCellNode.");return o}getRowStriping(){return Boolean(this.getLatest().__rowStriping)}setRowStriping(e){const t=this.getWritable();return t.__rowStriping=e,t}setFrozenColumns(e){const t=this.getWritable();return t.__frozenColumnCount=e,t}getFrozenColumns(){return this.getLatest().__frozenColumnCount}setFrozenRows(e){const t=this.getWritable();return t.__frozenRowCount=e,t}getFrozenRows(){return this.getLatest().__frozenRowCount}canSelectBefore(){return!0}canIndent(){return!1}getColumnCount(){const e=this.getFirstChild();if(!e)return 0;let t=0;return e.getChildren().forEach((e=>{ve(e)&&(t+=e.getColSpan())})),t}}function nn(e,t){const n=e.getElementByKey(t.getKey());return null===n&&Re(230),vt(t,n)}function on(e){const n=rn();e.hasAttribute("data-lexical-row-striping")&&n.setRowStriping(!0);const o=e.querySelector(":scope > colgroup");if(o){let e=[];for(const t of o.querySelectorAll(":scope > col")){let n=t.style.width||"";if(!Se.test(n)&&(n=t.getAttribute("width")||"",!/^\d+$/.test(n))){e=void 0;break}e.push(parseFloat(n))}e&&n.setColWidths(e)}return{after:e=>t(e,Ke),node:n}}function rn(){return p(new tn)}function ln(e){return e instanceof tn}function sn({rows:e,columns:t,includeHeaders:n}){const o=S();if(!o||!w(o))return!1;if(Ht(o.anchor.getNode()))return!1;const r=$e(Number(e),Number(t),n);c(r);const l=r.getFirstDescendant();return m(l)&&l.select(),!0}function cn(e){Ke(e.getParent())?e.isEmpty()&&e.append(d()):e.remove()}function an(e){ln(e.getParent())?a(e,ve):e.remove()}function un(e){a(e,Ke);const[t]=tt(e,null,null),n=t.reduce(((e,t)=>Math.max(e,t.length)),0),o=e.getChildren();for(let e=0;e<t.length;++e){const r=o[e];if(!r)continue;Ke(r)||Re(254,r.constructor.name,r.getType());const l=t[e].reduce(((e,t)=>t?1+e:e),0);if(l!==n)for(let e=l;e<n;++e){const e=xe();e.append(d()),r.append(e)}}}function hn(e){if(e.detail<3||!oe(e.target))return!1;const t=J(e.target);if(null===t)return!1;const o=n(t,(e=>f(e)&&!e.isInline()));if(null===o)return!1;return!!ve(o.getParent())&&(o.select(0),!0)}function dn(e){return e.registerNodeTransform(be,(e=>{if(e.getColSpan()>1||e.getRowSpan()>1){const[,,t]=nt(e),[n]=et(t,e,e),o=n.length,r=n[0].length;let l=t.getFirstChild();Ke(l)||Re(175);const i=[];for(let e=0;e<o;e++){0!==e&&(l=l.getNextSibling(),Ke(l)||Re(175));let t=null;for(let o=0;o<r;o++){const r=n[e][o],c=r.cell;if(r.startRow===e&&r.startColumn===o)t=c,i.push(c);else if(c.getColSpan()>1||c.getRowSpan()>1){ve(c)||Re(176);const e=xe(c.__headerState);null!==t?t.insertAfter(e):s(l,e)}}}for(const e of i)e.setColSpan(1),e.setRowSpan(1)}}))}function fn(e,t=!0){const n=new Map,o=(o,r,l)=>{const s=mt(o,l),i=bt(o,s,e,t);n.set(r,[i,s])},r=e.registerMutationListener(tn,(t=>{e.getEditorState().read((()=>{for(const[e,r]of t){const t=n.get(e);if("created"===r||"updated"===r){const{tableNode:r,tableElement:l}=ht(e);void 0===t?o(r,e,l):l!==t[1]&&(t[0].removeListeners(),n.delete(e),o(r,e,l))}else"destroyed"===r&&void 0!==t&&(t[0].removeListeners(),n.delete(e))}}),{editor:e})}),{skipInitialization:!1});return()=>{r();for(const[,[e]]of n)e.removeListeners()}}function gn(e){return e.hasNodes([tn])||Re(255),i(e.registerCommand(Te,sn,me),e.registerCommand(I,(({nodes:e,selection:t})=>{if(!w(t))return!1;return null!==Ht(t.anchor.getNode())&&e.some(ln)}),me),e.registerCommand(pe,hn,me),e.registerNodeTransform(tn,un),e.registerNodeTransform(Fe,an),e.registerNodeTransform(be,cn))}export{et as $computeTableMap,tt as $computeTableMapSkipCellCheck,xe as $createTableCellNode,rn as $createTableNode,$e as $createTableNodeWithDimensions,Oe as $createTableRowNode,ct as $createTableSelection,at as $createTableSelectionFrom,Xe as $deleteTableColumn,Ve as $deleteTableColumn__EXPERIMENTAL,je as $deleteTableRow__EXPERIMENTAL,Wt as $findCellNode,Ht as $findTableNode,nn as $getElementForTableNode,nt as $getNodeTriplet,ht as $getTableAndElementByKey,Le as $getTableCellNodeFromLexicalNode,rt as $getTableCellNodeRect,Be as $getTableColumnIndexFromTableCellNode,We as $getTableNodeFromLexicalNodeOrThrow,He as $getTableRowIndexFromTableCellNode,ze as $getTableRowNodeFromTableCellNodeOrThrow,Ye as $insertTableColumn,qe as $insertTableColumn__EXPERIMENTAL,Ie as $insertTableRow,Je as $insertTableRow__EXPERIMENTAL,Zt as $isScrollableTablesActive,ve as $isTableCellNode,ln as $isTableNode,Ke as $isTableRowNode,it as $isTableSelection,De as $removeTableRowAtIndex,Ze as $unmergeCell,Te as INSERT_TABLE_COMMAND,we as TableCellHeaderStates,be as TableCellNode,tn as TableNode,dt as TableObserver,Fe as TableRowNode,bt as applyTableHandlers,Nt as getDOMCellFromTarget,mt as getTableElement,yt as getTableObserverFromTableElement,dn as registerTableCellUnmergeTransform,gn as registerTablePlugin,fn as registerTableSelectionObserver,en as setScrollableTablesActive};
