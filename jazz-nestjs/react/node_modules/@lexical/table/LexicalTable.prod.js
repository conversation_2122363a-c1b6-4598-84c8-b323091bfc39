/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("@lexical/utils"),t=require("lexical"),n=require("@lexical/clipboard");const o=/^(\d+(?:\.\d+)?)px$/,r={BOTH:3,COLUMN:2,NO_STATUS:0,ROW:1};class l extends t.ElementNode{static getType(){return"tablecell"}static clone(e){return new l(e.__headerState,e.__colSpan,e.__width,e.__key)}afterCloneFrom(e){super.afterCloneFrom(e),this.__rowSpan=e.__rowSpan,this.__backgroundColor=e.__backgroundColor,this.__verticalAlign=e.__verticalAlign}static importDOM(){return{td:e=>({conversion:s,priority:0}),th:e=>({conversion:s,priority:0})}}static importJSON(e){return a().updateFromJSON(e)}updateFromJSON(e){return super.updateFromJSON(e).setHeaderStyles(e.headerState).setColSpan(e.colSpan||1).setRowSpan(e.rowSpan||1).setWidth(e.width||void 0).setBackgroundColor(e.backgroundColor||null).setVerticalAlign(e.verticalAlign||void 0)}constructor(e=r.NO_STATUS,t=1,n,o){super(o),this.__colSpan=t,this.__rowSpan=1,this.__headerState=e,this.__width=n,this.__backgroundColor=null,this.__verticalAlign=void 0}createDOM(t){const n=document.createElement(this.getTag());return this.__width&&(n.style.width=`${this.__width}px`),this.__colSpan>1&&(n.colSpan=this.__colSpan),this.__rowSpan>1&&(n.rowSpan=this.__rowSpan),null!==this.__backgroundColor&&(n.style.backgroundColor=this.__backgroundColor),i(this.__verticalAlign)&&(n.style.verticalAlign=this.__verticalAlign),e.addClassNamesToElement(n,t.theme.tableCell,this.hasHeader()&&t.theme.tableCellHeader),n}exportDOM(e){const n=super.exportDOM(e);if(t.isHTMLElement(n.element)){const e=n.element;e.setAttribute("data-temporary-table-cell-lexical-key",this.getKey()),e.style.border="1px solid black",this.__colSpan>1&&(e.colSpan=this.__colSpan),this.__rowSpan>1&&(e.rowSpan=this.__rowSpan),e.style.width=`${this.getWidth()||75}px`,e.style.verticalAlign=this.getVerticalAlign()||"top",e.style.textAlign="start",null===this.__backgroundColor&&this.hasHeader()&&(e.style.backgroundColor="#f2f3f5")}return n}exportJSON(){return{...super.exportJSON(),...i(this.__verticalAlign)&&{verticalAlign:this.__verticalAlign},backgroundColor:this.getBackgroundColor(),colSpan:this.__colSpan,headerState:this.__headerState,rowSpan:this.__rowSpan,width:this.getWidth()}}getColSpan(){return this.getLatest().__colSpan}setColSpan(e){const t=this.getWritable();return t.__colSpan=e,t}getRowSpan(){return this.getLatest().__rowSpan}setRowSpan(e){const t=this.getWritable();return t.__rowSpan=e,t}getTag(){return this.hasHeader()?"th":"td"}setHeaderStyles(e,t=r.BOTH){const n=this.getWritable();return n.__headerState=e&t|n.__headerState&~t,n}getHeaderStyles(){return this.getLatest().__headerState}setWidth(e){const t=this.getWritable();return t.__width=e,t}getWidth(){return this.getLatest().__width}getBackgroundColor(){return this.getLatest().__backgroundColor}setBackgroundColor(e){const t=this.getWritable();return t.__backgroundColor=e,t}getVerticalAlign(){return this.getLatest().__verticalAlign}setVerticalAlign(e){const t=this.getWritable();return t.__verticalAlign=e||void 0,t}toggleHeaderStyle(e){const t=this.getWritable();return(t.__headerState&e)===e?t.__headerState-=e:t.__headerState+=e,t}hasHeaderState(e){return(this.getHeaderStyles()&e)===e}hasHeader(){return this.getLatest().__headerState!==r.NO_STATUS}updateDOM(e){return e.__headerState!==this.__headerState||e.__width!==this.__width||e.__colSpan!==this.__colSpan||e.__rowSpan!==this.__rowSpan||e.__backgroundColor!==this.__backgroundColor||e.__verticalAlign!==this.__verticalAlign}isShadowRoot(){return!0}collapseAtStart(){return!0}canBeEmpty(){return!1}canIndent(){return!1}}function i(e){return"middle"===e||"bottom"===e}function s(e){const n=e,l=e.nodeName.toLowerCase();let s;o.test(n.style.width)&&(s=parseFloat(n.style.width));const u=a("th"===l?r.ROW:r.NO_STATUS,n.colSpan,s);u.__rowSpan=n.rowSpan;const d=n.style.backgroundColor;""!==d&&(u.__backgroundColor=d);const h=n.style.verticalAlign;i(h)&&(u.__verticalAlign=h);const g=n.style,f=(g&&g.textDecoration||"").split(" "),m="700"===g.fontWeight||"bold"===g.fontWeight,C=f.includes("line-through"),p="italic"===g.fontStyle,_=f.includes("underline");return{after:e=>(0===e.length&&e.push(t.$createParagraphNode()),e),forChild:(e,n)=>{if(c(n)&&!t.$isElementNode(e)){const n=t.$createParagraphNode();return t.$isLineBreakNode(e)&&"\n"===e.getTextContent()?null:(t.$isTextNode(e)&&(m&&e.toggleFormat("bold"),C&&e.toggleFormat("strikethrough"),p&&e.toggleFormat("italic"),_&&e.toggleFormat("underline")),n.append(e),n)}return e},node:u}}function a(e=r.NO_STATUS,n=1,o){return t.$applyNodeReplacement(new l(e,n,o))}function c(e){return e instanceof l}const u=t.createCommand("INSERT_TABLE_COMMAND");function d(e,...t){const n=new URL("https://lexical.dev/docs/error"),o=new URLSearchParams;o.append("code",e);for(const e of t)o.append("v",e);throw n.search=o.toString(),Error(`Minified Lexical error #${e}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}class h extends t.ElementNode{static getType(){return"tablerow"}static clone(e){return new h(e.__height,e.__key)}static importDOM(){return{tr:e=>({conversion:g,priority:0})}}static importJSON(e){return f().updateFromJSON(e)}updateFromJSON(e){return super.updateFromJSON(e).setHeight(e.height)}constructor(e,t){super(t),this.__height=e}exportJSON(){const e=this.getHeight();return{...super.exportJSON(),...void 0===e?void 0:{height:e}}}createDOM(t){const n=document.createElement("tr");return this.__height&&(n.style.height=`${this.__height}px`),e.addClassNamesToElement(n,t.theme.tableRow),n}extractWithChild(e,t,n){return"html"===n}isShadowRoot(){return!0}setHeight(e){const t=this.getWritable();return t.__height=e,t}getHeight(){return this.getLatest().__height}updateDOM(e){return e.__height!==this.__height}canBeEmpty(){return!1}canIndent(){return!1}}function g(t){const n=t;let r;return o.test(n.style.height)&&(r=parseFloat(n.style.height)),{after:t=>e.$descendantsMatching(t,c),node:f(r)}}function f(e){return t.$applyNodeReplacement(new h(e))}function m(e){return e instanceof h}const C="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,p=C&&"documentMode"in document?document.documentMode:null,_=C&&/^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);function S(e,n,o=!0){const l=xe();for(let i=0;i<e;i++){const e=f();for(let l=0;l<n;l++){let n=r.NO_STATUS;"object"==typeof o?(0===i&&o.rows&&(n|=r.ROW),0===l&&o.columns&&(n|=r.COLUMN)):o&&(0===i&&(n|=r.ROW),0===l&&(n|=r.COLUMN));const s=a(n),c=t.$createParagraphNode();c.append(t.$createTextNode()),s.append(c),e.append(s)}l.append(e)}return l}function N(t){const n=e.$findMatchingParent(t,(e=>m(e)));if(m(n))return n;throw new Error("Expected table cell to be inside of table row.")}function w(t){const n=e.$findMatchingParent(t,(e=>Oe(e)));if(Oe(n))return n;throw new Error("Expected table cell to be inside of table.")}function b(e,t){const n=w(e),{x:o,y:r}=n.getCordsFromCellNode(e,t);return{above:n.getCellNodeFromCords(o,r-1,t),below:n.getCellNodeFromCords(o,r+1,t),left:n.getCellNodeFromCords(o-1,r,t),right:n.getCellNodeFromCords(o+1,r,t)}}C&&"InputEvent"in window&&!p&&new window.InputEvent("input");const T=(e,t)=>e===r.BOTH||e===t?t:r.NO_STATUS;function $(e){const t=e.getFirstDescendant();null==t?e.selectStart():t.getParentOrThrow().selectStart()}function y(e,t){const n=e.getFirstChild();null!==n?n.insertBefore(t):e.append(t)}function R(e,t,n){const[o,r,l]=M(e,t,n);return null===r&&d(207),null===l&&d(208),[o,r,l]}function M(e,t,n){const o=[];let r=null,l=null;function i(e){let t=o[e];return void 0===t&&(o[e]=t=[]),t}const s=e.getChildren();for(let e=0;e<s.length;e++){const o=s[e];m(o)||d(209);const a=i(e);for(let u=o.getFirstChild(),h=0;null!=u;u=u.getNextSibling()){for(c(u)||d(147);void 0!==a[h];)h++;const o={cell:u,startColumn:h,startRow:e},{__rowSpan:g,__colSpan:f}=u;for(let t=0;t<g&&!(e+t>=s.length);t++){const n=i(e+t);for(let e=0;e<f;e++)n[h+e]=o}null!==t&&null===r&&t.is(u)&&(r=o),null!==n&&null===l&&n.is(u)&&(l=o)}}return[o,r,l]}function E(t){let n;if(t instanceof l)n=t;else if("__type"in t){const o=e.$findMatchingParent(t,c);c(o)||d(148),n=o}else{const o=e.$findMatchingParent(t.getNode(),c);c(o)||d(148),n=o}const o=n.getParent();m(o)||d(149);const r=o.getParent();return Oe(r)||d(210),[n,o,r]}function x(e,t,n){let o,r=Math.min(t.startColumn,n.startColumn),l=Math.min(t.startRow,n.startRow),i=Math.max(t.startColumn+t.cell.__colSpan-1,n.startColumn+n.cell.__colSpan-1),s=Math.max(t.startRow+t.cell.__rowSpan-1,n.startRow+n.cell.__rowSpan-1);do{o=!1;for(let t=0;t<e.length;t++)for(let n=0;n<e[0].length;n++){const a=e[t][n];if(!a)continue;const c=a.startColumn+a.cell.__colSpan-1,u=a.startRow+a.cell.__rowSpan-1,d=a.startColumn<=i&&c>=r,h=a.startRow<=s&&u>=l;if(d&&h){const e=Math.min(r,a.startColumn),t=Math.max(i,c),n=Math.min(l,a.startRow),d=Math.max(s,u);e===r&&t===i&&n===l&&d===s||(r=e,i=t,l=n,s=d,o=!0)}}}while(o);return{maxColumn:i,maxRow:s,minColumn:r,minRow:l}}function O(e){const[t,,n]=E(e),o=n.getChildren(),r=o.length,l=o[0].getChildren().length,i=new Array(r);for(let e=0;e<r;e++)i[e]=new Array(l);for(let e=0;e<r;e++){const n=o[e].getChildren();let r=0;for(let o=0;o<n.length;o++){for(;i[e][r];)r++;const l=n[o],s=l.__rowSpan||1,a=l.__colSpan||1;for(let t=0;t<s;t++)for(let n=0;n<a;n++)i[e+t][r+n]=l;if(t===l)return{colSpan:a,columnIndex:r,rowIndex:e,rowSpan:s};r+=a}}return null}function A(t){const[[n,o,r,l],[i,s,a,u]]=["anchor","focus"].map((n=>{const o=t[n].getNode(),r=e.$findMatchingParent(o,c);c(r)||d(238,n,o.getKey(),o.getType());const l=r.getParent();m(l)||d(239,n);const i=l.getParent();return Oe(i)||d(240,n),[o,r,l,i]}));return l.is(u)||d(241),{anchorCell:o,anchorNode:n,anchorRow:r,anchorTable:l,focusCell:s,focusNode:i,focusRow:a,focusTable:u}}class v{constructor(e,t,n){this.anchor=t,this.focus=n,t._selection=this,n._selection=this,this._cachedNodes=null,this.dirty=!1,this.tableKey=e}getStartEndPoints(){return[this.anchor,this.focus]}isValid(){return"root"!==this.tableKey&&"root"!==this.anchor.key&&"element"===this.anchor.type&&"root"!==this.focus.key&&"element"===this.focus.type}isBackward(){return this.focus.isBefore(this.anchor)}getCachedNodes(){return this._cachedNodes}setCachedNodes(e){this._cachedNodes=e}is(e){return F(e)&&this.tableKey===e.tableKey&&this.anchor.is(e.anchor)&&this.focus.is(e.focus)}set(e,t,n){this.dirty=this.dirty||e!==this.tableKey||t!==this.anchor.key||n!==this.focus.key,this.tableKey=e,this.anchor.key=t,this.focus.key=n,this._cachedNodes=null}clone(){return new v(this.tableKey,t.$createPoint(this.anchor.key,this.anchor.offset,this.anchor.type),t.$createPoint(this.focus.key,this.focus.offset,this.focus.type))}isCollapsed(){return!1}extract(){return this.getNodes()}insertRawText(e){}insertText(){}hasFormat(e){let n=0;this.getNodes().filter(c).forEach((e=>{const o=e.getFirstChild();t.$isParagraphNode(o)&&(n|=o.getTextFormat())}));const o=t.TEXT_TYPE_TO_FORMAT[e];return!!(n&o)}insertNodes(e){const n=this.focus.getNode();t.$isElementNode(n)||d(151);t.$normalizeSelection__EXPERIMENTAL(n.select(0,n.getChildrenSize())).insertNodes(e)}getShape(){const{anchorCell:e,focusCell:t}=A(this),n=O(e);null===n&&d(153);const o=O(t);null===o&&d(155);const r=Math.min(n.columnIndex,o.columnIndex),l=Math.max(n.columnIndex+n.colSpan-1,o.columnIndex+o.colSpan-1),i=Math.min(n.rowIndex,o.rowIndex),s=Math.max(n.rowIndex+n.rowSpan-1,o.rowIndex+o.rowSpan-1);return{fromX:Math.min(r,l),fromY:Math.min(i,s),toX:Math.max(r,l),toY:Math.max(i,s)}}getNodes(){if(!this.isValid())return[];const e=this._cachedNodes;if(null!==e)return e;const{anchorTable:n,anchorCell:o,focusCell:r}=A(this),l=r.getParents()[1];if(l!==n){if(n.isParentOf(r)){const e=l.getParent();null==e&&d(159),this.set(this.tableKey,r.getKey(),e.getKey())}else{const e=n.getParent();null==e&&d(158),this.set(this.tableKey,e.getKey(),r.getKey())}return this.getNodes()}const[i,s,a]=R(n,o,r),{minColumn:c,maxColumn:u,minRow:h,maxRow:g}=x(i,s,a),f=new Map([[n.getKey(),n]]);let C=null;for(let e=h;e<=g;e++)for(let t=c;t<=u;t++){const{cell:n}=i[e][t],o=n.getParent();m(o)||d(160),o!==C&&(f.set(o.getKey(),o),C=o),f.has(n.getKey())||I(n,(e=>{f.set(e.getKey(),e)}))}const p=Array.from(f.values());return t.isCurrentlyReadOnlyMode()||(this._cachedNodes=p),p}getTextContent(){const e=this.getNodes().filter((e=>c(e)));let t="";for(let n=0;n<e.length;n++){const o=e[n],r=o.__parent,l=(e[n+1]||{}).__parent;t+=o.getTextContent()+(l!==r?"\n":"\t")}return t}}function F(e){return e instanceof v}function P(){const e=t.$createPoint("root",0,"element"),n=t.$createPoint("root",0,"element");return new v("root",e,n)}function D(e,n,o){e.getKey(),n.getKey(),o.getKey();const r=t.$getSelection(),l=F(r)?r.clone():P();return l.set(e.getKey(),n.getKey(),o.getKey()),l}function I(e,n){const o=[[e]];for(let e=o.at(-1);void 0!==e&&o.length>0;e=o.at(-1)){const r=e.pop();void 0===r?o.pop():!1!==n(r)&&t.$isElementNode(r)&&o.push(r.getChildren())}}function K(e,n=t.$getEditor()){const o=t.$getNodeByKey(e);Oe(o)||d(231,e);const r=B(o,n.getElementByKey(e));return null===r&&d(232,e),{tableElement:r,tableNode:o}}class L{constructor(e,t){this.isHighlightingCells=!1,this.anchorX=-1,this.anchorY=-1,this.focusX=-1,this.focusY=-1,this.listenersToRemove=new Set,this.tableNodeKey=t,this.editor=e,this.table={columns:0,domRows:[],rows:0},this.tableSelection=null,this.anchorCellNodeKey=null,this.focusCellNodeKey=null,this.anchorCell=null,this.focusCell=null,this.hasHijackedSelectionStyles=!1,this.isSelecting=!1,this.pointerType=null,this.shouldCheckSelection=!1,this.abortController=new AbortController,this.listenerOptions={signal:this.abortController.signal},this.nextFocus=null,this.trackTable()}getTable(){return this.table}removeListeners(){this.abortController.abort("removeListeners"),Array.from(this.listenersToRemove).forEach((e=>e())),this.listenersToRemove.clear()}$lookup(){return K(this.tableNodeKey,this.editor)}trackTable(){const e=new MutationObserver((e=>{this.editor.getEditorState().read((()=>{let t=!1;for(let n=0;n<e.length;n++){const o=e[n].target.nodeName;if("TABLE"===o||"TBODY"===o||"THEAD"===o||"TR"===o){t=!0;break}}if(!t)return;const{tableNode:n,tableElement:o}=this.$lookup();this.table=V(n,o)}),{editor:this.editor})}));this.editor.getEditorState().read((()=>{const{tableNode:t,tableElement:n}=this.$lookup();this.table=V(t,n),e.observe(n,{attributes:!0,childList:!0,subtree:!0})}),{editor:this.editor})}$clearHighlight(){const e=this.editor;this.isHighlightingCells=!1,this.anchorX=-1,this.anchorY=-1,this.focusX=-1,this.focusY=-1,this.tableSelection=null,this.anchorCellNodeKey=null,this.focusCellNodeKey=null,this.anchorCell=null,this.focusCell=null,this.hasHijackedSelectionStyles=!1,this.$enableHighlightStyle();const{tableNode:n,tableElement:o}=this.$lookup();Q(e,V(n,o),null),null!==t.$getSelection()&&(t.$setSelection(null),e.dispatchCommand(t.SELECTION_CHANGE_COMMAND,void 0))}$enableHighlightStyle(){const t=this.editor,{tableElement:n}=this.$lookup();e.removeClassNamesFromElement(n,t._config.theme.tableSelection),n.classList.remove("disable-selection"),this.hasHijackedSelectionStyles=!1}$disableHighlightStyle(){const{tableElement:t}=this.$lookup();e.addClassNamesToElement(t,this.editor._config.theme.tableSelection),this.hasHijackedSelectionStyles=!0}$updateTableTableSelection(e){if(null!==e){e.tableKey!==this.tableNodeKey&&d(233,e.tableKey,this.tableNodeKey);const t=this.editor;this.tableSelection=e,this.isHighlightingCells=!0,this.$disableHighlightStyle(),this.updateDOMSelection(),Q(t,this.table,this.tableSelection)}else this.$clearHighlight()}setShouldCheckSelection(){this.shouldCheckSelection=!0}getAndClearShouldCheckSelection(){return!!this.shouldCheckSelection&&(this.shouldCheckSelection=!1,!0)}setNextFocus(e){this.nextFocus=e}getAndClearNextFocus(){const{nextFocus:e}=this;return null!==e&&(this.nextFocus=null),e}updateDOMSelection(){if(null!==this.anchorCell&&null!==this.focusCell){const e=t.getDOMSelection(this.editor._window);e&&e.rangeCount>0&&e.removeAllRanges()}}$setFocusCellForSelection(e,n=!1){const o=this.editor,{tableNode:r}=this.$lookup(),l=e.x,i=e.y;if(this.focusCell=e,this.isHighlightingCells||this.anchorX===l&&this.anchorY===i&&!n){if(l===this.focusX&&i===this.focusY)return!1}else this.isHighlightingCells=!0,this.$disableHighlightStyle();if(this.focusX=l,this.focusY=i,this.isHighlightingCells){const n=_e(r,e.elem);if(null!=this.tableSelection&&null!=this.anchorCellNodeKey&&null!==n)return this.focusCellNodeKey=n.getKey(),this.tableSelection=D(r,this.$getAnchorTableCellOrThrow(),n),t.$setSelection(this.tableSelection),o.dispatchCommand(t.SELECTION_CHANGE_COMMAND,void 0),Q(o,this.table,this.tableSelection),!0}return!1}$getAnchorTableCell(){return this.anchorCellNodeKey?t.$getNodeByKey(this.anchorCellNodeKey):null}$getAnchorTableCellOrThrow(){const e=this.$getAnchorTableCell();return null===e&&d(234),e}$getFocusTableCell(){return this.focusCellNodeKey?t.$getNodeByKey(this.focusCellNodeKey):null}$getFocusTableCellOrThrow(){const e=this.$getFocusTableCell();return null===e&&d(235),e}$setAnchorCellForSelection(e){this.isHighlightingCells=!1,this.anchorCell=e,this.anchorX=e.x,this.anchorY=e.y;const{tableNode:t}=this.$lookup(),n=_e(t,e.elem);if(null!==n){const e=n.getKey();this.tableSelection=null!=this.tableSelection?this.tableSelection.clone():P(),this.anchorCellNodeKey=e}}$formatCells(e){const n=t.$getSelection();F(n)||d(236);const o=t.$createRangeSelection(),r=o.anchor,l=o.focus,i=n.getNodes().filter(c);i.length>0||d(237);const s=i[0].getFirstChild(),a=t.$isParagraphNode(s)?s.getFormatFlags(e,null):null;i.forEach((t=>{r.set(t.getKey(),0,"element"),l.set(t.getKey(),t.getChildrenSize(),"element"),o.formatText(e,a)})),t.$setSelection(n),this.editor.dispatchCommand(t.SELECTION_CHANGE_COMMAND,void 0)}$clearText(){const{editor:e}=this,n=t.$getNodeByKey(this.tableNodeKey);if(!Oe(n))throw new Error("Expected TableNode.");const o=t.$getSelection();F(o)||d(253);const r=o.getNodes().filter(c);if(r.length===this.table.columns*this.table.rows){n.selectPrevious();const o=n.getParent();return n.remove(),void(t.$isRootNode(o)&&o.isEmpty()&&e.dispatchCommand(t.INSERT_PARAGRAPH_COMMAND,void 0))}r.forEach((e=>{if(t.$isElementNode(e)){const n=t.$createParagraphNode(),o=t.$createTextNode();n.append(o),e.append(n),e.getChildren().forEach((e=>{e!==n&&e.remove()}))}})),Q(e,this.table,null),t.$setSelection(null),e.dispatchCommand(t.SELECTION_CHANGE_COMMAND,void 0)}}const k="__lexicalTableSelection",H=e=>!(1&~e.buttons);function B(e,t){if(!t)return t;const n="TABLE"===t.nodeName?t:e.getDOMSlot(t).element;return"TABLE"!==n.nodeName&&d(245,t.nodeName),n}function W(e){return e._window}function z(e,t){for(let n=t,o=null;null!==n;n=n.getParent()){if(e.is(n))return o;c(n)&&(o=n)}return null}const Y=[[t.KEY_ARROW_DOWN_COMMAND,"down"],[t.KEY_ARROW_UP_COMMAND,"up"],[t.KEY_ARROW_LEFT_COMMAND,"backward"],[t.KEY_ARROW_RIGHT_COMMAND,"forward"]],U=[t.DELETE_WORD_COMMAND,t.DELETE_LINE_COMMAND,t.DELETE_CHARACTER_COMMAND],X=[t.KEY_BACKSPACE_COMMAND,t.KEY_DELETE_COMMAND];function J(o,r,l,i){const s=l.getRootElement(),a=W(l);null!==s&&null!==a||d(246);const u=new L(l,o.getKey()),h=B(o,r);!function(e,t){null!==q(e)&&d(205);e[k]=t}(h,u),u.listenersToRemove.add((()=>function(e,t){q(e)===t&&delete e[k]}(h,u)));const g=e=>{if(u.pointerType=e.pointerType,0!==e.button||!t.isDOMNode(e.target)||!a)return;const n=G(e.target);null!==n&&l.update((()=>{const r=t.$getPreviousSelection();if(_&&e.shiftKey&&ie(r,o)&&(t.$isRangeSelection(r)||F(r))){const t=r.anchor.getNode(),l=z(o,r.anchor.getNode());if(l)u.$setAnchorCellForSelection(pe(u,l)),u.$setFocusCellForSelection(n),fe(e);else{(o.isBefore(t)?o.selectStart():o.selectEnd()).anchor.set(r.anchor.key,r.anchor.offset,r.anchor.type)}}else u.$setAnchorCellForSelection(n)})),(()=>{if(u.isSelecting)return;const e=()=>{u.isSelecting=!1,a.removeEventListener("pointerup",e),a.removeEventListener("pointermove",n)},n=o=>{if(!H(o)&&u.isSelecting)return u.isSelecting=!1,a.removeEventListener("pointerup",e),void a.removeEventListener("pointermove",n);if(!t.isDOMNode(o.target))return;let r=null;const i=!(_||h.contains(o.target));if(i)r=j(h,o.target);else for(const e of document.elementsFromPoint(o.clientX,o.clientY))if(r=j(h,e),r)break;!r||null!==u.focusCell&&r.elem===u.focusCell.elem||(u.setNextFocus({focusCell:r,override:i}),l.dispatchCommand(t.SELECTION_CHANGE_COMMAND,void 0))};u.isSelecting=!0,a.addEventListener("pointerup",e,u.listenerOptions),a.addEventListener("pointermove",n,u.listenerOptions)})()};h.addEventListener("pointerdown",g,u.listenerOptions),u.listenersToRemove.add((()=>{h.removeEventListener("pointerdown",g)}));const f=e=>{if(e.detail>=3&&t.isDOMNode(e.target)){null!==G(e.target)&&e.preventDefault()}};h.addEventListener("mousedown",f,u.listenerOptions),u.listenersToRemove.add((()=>{h.removeEventListener("mousedown",f)}));const C=e=>{const n=e.target;0===e.button&&t.isDOMNode(n)&&l.update((()=>{const e=t.$getSelection();F(e)&&e.tableKey===u.tableNodeKey&&s.contains(n)&&u.$clearHighlight()}))};a.addEventListener("pointerdown",C,u.listenerOptions),u.listenersToRemove.add((()=>{a.removeEventListener("pointerdown",C)}));for(const[e,n]of Y)u.listenersToRemove.add(l.registerCommand(e,(e=>ge(l,e,n,o,u)),t.COMMAND_PRIORITY_HIGH));u.listenersToRemove.add(l.registerCommand(t.KEY_ESCAPE_COMMAND,(e=>{const n=t.$getSelection();if(F(n)){const t=z(o,n.focus.getNode());if(null!==t)return fe(e),t.selectEnd(),!0}return!1}),t.COMMAND_PRIORITY_HIGH));const p=n=>()=>{const r=t.$getSelection();if(!ie(r,o))return!1;if(F(r))return u.$clearText(),!0;if(t.$isRangeSelection(r)){if(!c(z(o,r.anchor.getNode())))return!1;const l=r.anchor.getNode(),i=r.focus.getNode(),s=o.isParentOf(l),a=o.isParentOf(i);if(s&&!a||a&&!s)return u.$clearText(),!0;const d=e.$findMatchingParent(r.anchor.getNode(),(e=>t.$isElementNode(e))),h=d&&e.$findMatchingParent(d,(e=>t.$isElementNode(e)&&c(e.getParent())));if(!t.$isElementNode(h)||!t.$isElementNode(d))return!1;if(n===t.DELETE_LINE_COMMAND&&null===h.getPreviousSibling())return!0}return!1};for(const e of U)u.listenersToRemove.add(l.registerCommand(e,p(e),t.COMMAND_PRIORITY_CRITICAL));const S=e=>{const n=t.$getSelection();if(!F(n)&&!t.$isRangeSelection(n))return!1;const r=o.isParentOf(n.anchor.getNode());if(r!==o.isParentOf(n.focus.getNode())){const e=r?"anchor":"focus",t=r?"focus":"anchor",{key:l,offset:i,type:s}=n[t];return o[n[e].isBefore(n[t])?"selectPrevious":"selectNext"]()[t].set(l,i,s),!1}return!!ie(n,o)&&(!!F(n)&&(e&&(e.preventDefault(),e.stopPropagation()),u.$clearText(),!0))};for(const e of X)u.listenersToRemove.add(l.registerCommand(e,S,t.COMMAND_PRIORITY_CRITICAL));return u.listenersToRemove.add(l.registerCommand(t.CUT_COMMAND,(o=>{const r=t.$getSelection();if(r){if(!F(r)&&!t.$isRangeSelection(r))return!1;n.copyToClipboard(l,e.objectKlassEquals(o,ClipboardEvent)?o:null,n.$getClipboardDataFromSelection(r));const i=S(o);return t.$isRangeSelection(r)?(r.removeText(),!0):i}return!1}),t.COMMAND_PRIORITY_CRITICAL)),u.listenersToRemove.add(l.registerCommand(t.FORMAT_TEXT_COMMAND,(n=>{const r=t.$getSelection();if(!ie(r,o))return!1;if(F(r))return u.$formatCells(n),!0;if(t.$isRangeSelection(r)){const t=e.$findMatchingParent(r.anchor.getNode(),(e=>c(e)));if(!c(t))return!1}return!1}),t.COMMAND_PRIORITY_CRITICAL)),u.listenersToRemove.add(l.registerCommand(t.FORMAT_ELEMENT_COMMAND,(e=>{const n=t.$getSelection();if(!F(n)||!ie(n,o))return!1;const r=n.anchor.getNode(),l=n.focus.getNode();if(!c(r)||!c(l))return!1;if(function(e,t){if(F(e)){const n=e.anchor.getNode(),o=e.focus.getNode();if(t&&n&&o){const[e]=R(t,n,o);return n.getKey()===e[0][0].cell.getKey()&&o.getKey()===e[e.length-1].at(-1).cell.getKey()}}return!1}(n,o))return o.setFormat(e),!0;const[i,s,a]=R(o,r,l),u=Math.max(s.startRow+s.cell.__rowSpan-1,a.startRow+a.cell.__rowSpan-1),d=Math.max(s.startColumn+s.cell.__colSpan-1,a.startColumn+a.cell.__colSpan-1),h=Math.min(s.startRow,a.startRow),g=Math.min(s.startColumn,a.startColumn),f=new Set;for(let n=h;n<=u;n++)for(let o=g;o<=d;o++){const r=i[n][o].cell;if(f.has(r))continue;f.add(r),r.setFormat(e);const l=r.getChildren();for(let n=0;n<l.length;n++){const o=l[n];t.$isElementNode(o)&&!o.isInline()&&o.setFormat(e)}}return!0}),t.COMMAND_PRIORITY_CRITICAL)),u.listenersToRemove.add(l.registerCommand(t.CONTROLLED_TEXT_INSERTION_COMMAND,(n=>{const r=t.$getSelection();if(!ie(r,o))return!1;if(F(r))return u.$clearHighlight(),!1;if(t.$isRangeSelection(r)){const i=e.$findMatchingParent(r.anchor.getNode(),(e=>c(e)));if(!c(i))return!1;if("string"==typeof n){const e=Ce(l,r,o);if(e)return me(e,o,[t.$createTextNode(n)]),!0}}return!1}),t.COMMAND_PRIORITY_CRITICAL)),i&&u.listenersToRemove.add(l.registerCommand(t.KEY_TAB_COMMAND,(n=>{const r=t.$getSelection();if(!t.$isRangeSelection(r)||!r.isCollapsed()||!ie(r,o))return!1;const l=ue(r.anchor.getNode());return!(null===l||!o.is(de(l)))&&(fe(n),function(n,o){const r="next"===o?"getNextSibling":"getPreviousSibling",l="next"===o?"getFirstChild":"getLastChild",i=n[r]();if(t.$isElementNode(i))return i.selectEnd();const s=e.$findMatchingParent(n,m);null===s&&d(247);for(let e=s[r]();m(e);e=e[r]()){const n=e[l]();if(t.$isElementNode(n))return n.selectEnd()}const a=e.$findMatchingParent(s,Oe);null===a&&d(248);"next"===o?a.selectNext():a.selectPrevious()}(l,n.shiftKey?"previous":"next"),!0)}),t.COMMAND_PRIORITY_CRITICAL)),u.listenersToRemove.add(l.registerCommand(t.FOCUS_COMMAND,(e=>o.isSelected()),t.COMMAND_PRIORITY_HIGH)),u.listenersToRemove.add(l.registerCommand(t.SELECTION_INSERT_CLIPBOARD_NODES_COMMAND,(n=>{const{nodes:o,selection:r}=n,l=r.getStartEndPoints(),i=F(r),s=t.$isRangeSelection(r)&&null!==e.$findMatchingParent(r.anchor.getNode(),(e=>c(e)))&&null!==e.$findMatchingParent(r.focus.getNode(),(e=>c(e)))||i;if(1!==o.length||!Oe(o[0])||!s||null===l)return!1;const[a]=l,u=o[0],d=u.getChildren(),h=u.getFirstChildOrThrow().getChildrenSize(),g=u.getChildrenSize(),f=e.$findMatchingParent(a.getNode(),(e=>c(e))),C=f&&e.$findMatchingParent(f,(e=>m(e))),p=C&&e.$findMatchingParent(C,(e=>Oe(e)));if(!c(f)||!m(C)||!Oe(p))return!1;const _=C.getIndexWithinParent(),S=Math.min(p.getChildrenSize()-1,_+g-1),N=f.getIndexWithinParent(),w=Math.min(C.getChildrenSize()-1,N+h-1),b=Math.min(N,w),T=Math.min(_,S),$=Math.max(N,w),y=Math.max(_,S),R=p.getChildren();let M=0;for(let e=T;e<=y;e++){const n=R[e];if(!m(n))return!1;const o=d[M];if(!m(o))return!1;const r=n.getChildren(),l=o.getChildren();let i=0;for(let e=b;e<=$;e++){const n=r[e];if(!c(n))return!1;const o=l[i];if(!c(o))return!1;const s=n.getChildren();o.getChildren().forEach((e=>{if(t.$isTextNode(e)){t.$createParagraphNode().append(e),n.append(e)}else n.append(e)})),s.forEach((e=>e.remove())),i++}M++}return!0}),t.COMMAND_PRIORITY_CRITICAL)),u.listenersToRemove.add(l.registerCommand(t.SELECTION_CHANGE_COMMAND,(()=>{const n=t.$getSelection(),r=t.$getPreviousSelection(),i=u.getAndClearNextFocus();if(null!==i){const{focusCell:e}=i;if(F(n)&&n.tableKey===u.tableNodeKey)return(e.x!==u.focusX||e.y!==u.focusY)&&(u.$setFocusCellForSelection(e),!0);if(e!==u.anchorCell&&ie(n,o))return u.$setFocusCellForSelection(e),!0}if(u.getAndClearShouldCheckSelection()&&t.$isRangeSelection(r)&&t.$isRangeSelection(n)&&n.isCollapsed()){const t=n.anchor.getNode(),r=o.getFirstChild(),l=ue(t);if(null!==l&&m(r)){const t=r.getFirstChild();if(c(t)&&o.is(e.$findMatchingParent(l,(e=>e.is(o)||e.is(t)))))return t.selectStart(),!0}}if(t.$isRangeSelection(n)){const{anchor:e,focus:i}=n,s=e.getNode(),a=i.getNode(),c=ue(s),d=ue(a),h=!(!c||!o.is(de(c))),g=!(!d||!o.is(de(d))),f=h!==g,m=h&&g,C=n.isBackward();if(f){const e=n.clone();if(g){const[t]=R(o,d,d),n=t[0][0].cell,r=t[t.length-1].at(-1).cell;e.focus.set(C?n.getKey():r.getKey(),C?n.getChildrenSize():r.getChildrenSize(),"element")}else if(h){const[t]=R(o,c,c),n=t[0][0].cell,r=t[t.length-1].at(-1).cell;e.anchor.set(C?r.getKey():n.getKey(),C?r.getChildrenSize():0,"element")}t.$setSelection(e),ee(l,u)}else if(m&&(c.is(d)||(u.$setAnchorCellForSelection(pe(u,c)),u.$setFocusCellForSelection(pe(u,d),!0)),"touch"===u.pointerType&&n.isCollapsed()&&t.$isRangeSelection(r)&&r.isCollapsed())){const e=ue(r.anchor.getNode());e&&!e.is(d)&&(u.$setAnchorCellForSelection(pe(u,e)),u.$setFocusCellForSelection(pe(u,d),!0),u.pointerType=null)}}else if(n&&F(n)&&n.is(r)&&n.tableKey===o.getKey()){const e=t.getDOMSelection(a);if(e&&e.anchorNode&&e.focusNode){const r=t.$getNearestNodeFromDOMNode(e.focusNode),i=r&&!o.isParentOf(r),s=t.$getNearestNodeFromDOMNode(e.anchorNode),a=s&&o.isParentOf(s);if(i&&a&&e.rangeCount>0){const r=t.$createRangeSelectionFromDom(e,l);r&&(r.anchor.set(o.getKey(),n.isBackward()?o.getChildrenSize():0,"element"),e.removeAllRanges(),t.$setSelection(r))}}}return n&&!n.is(r)&&(F(n)||F(r))&&u.tableSelection&&!u.tableSelection.is(r)?(F(n)&&n.tableKey===u.tableNodeKey?u.$updateTableTableSelection(n):!F(n)&&F(r)&&r.tableKey===u.tableNodeKey&&u.$updateTableTableSelection(null),!1):(u.hasHijackedSelectionStyles&&!o.isSelected()?function(e,t){t.$enableHighlightStyle(),Z(t.table,(t=>{const n=t.elem;t.highlighted=!1,ce(e,t),n.getAttribute("style")||n.removeAttribute("style")}))}(l,u):!u.hasHijackedSelectionStyles&&o.isSelected()&&ee(l,u),!1)}),t.COMMAND_PRIORITY_CRITICAL)),u.listenersToRemove.add(l.registerCommand(t.INSERT_PARAGRAPH_COMMAND,(()=>{const e=t.$getSelection();if(!t.$isRangeSelection(e)||!e.isCollapsed()||!ie(e,o))return!1;const n=Ce(l,e,o);return!!n&&(me(n,o),!0)}),t.COMMAND_PRIORITY_CRITICAL)),u}function q(e){return e[k]||null}function G(e){let t=e;for(;null!=t;){const e=t.nodeName;if("TD"===e||"TH"===e){const e=t._cell;return void 0===e?null:e}t=t.parentNode}return null}function j(e,t){if(!e.contains(t))return null;let n=null;for(let o=t;null!=o;o=o.parentNode){if(o===e)return n;const t=o.nodeName;"TD"!==t&&"TH"!==t||(n=o._cell||null)}return null}function V(e,t){const n=[],o={columns:0,domRows:n,rows:0};let r=B(e,t).querySelector("tr"),l=0,i=0;for(n.length=0;null!=r;){const e=r.nodeName;if("TD"===e||"TH"===e){const e={elem:r,hasBackgroundColor:""!==r.style.backgroundColor,highlighted:!1,x:l,y:i};r._cell=e;let t=n[i];void 0===t&&(t=n[i]=[]),t[l]=e}else{const e=r.firstChild;if(null!=e){r=e;continue}}const t=r.nextSibling;if(null!=t){l++,r=t;continue}const o=r.parentNode;if(null!=o){const e=o.nextSibling;if(null==e)break;i++,l=0,r=e}}return o.columns=l+1,o.rows=i+1,o}function Q(e,t,n){const o=new Set(n?n.getNodes():[]);Z(t,((t,n)=>{const r=t.elem;o.has(n)?(t.highlighted=!0,ae(e,t)):(t.highlighted=!1,ce(e,t),r.getAttribute("style")||r.removeAttribute("style"))}))}function Z(e,n){const{domRows:o}=e;for(let e=0;e<o.length;e++){const r=o[e];if(r)for(let o=0;o<r.length;o++){const l=r[o];if(!l)continue;const i=t.$getNearestNodeFromDOMNode(l.elem);null!==i&&n(l,i,{x:o,y:e})}}}function ee(e,t){t.$disableHighlightStyle(),Z(t.table,(t=>{t.highlighted=!0,ae(e,t)}))}const te=(e,t,n,o,r)=>{const l="forward"===r;switch(r){case"backward":case"forward":return n!==(l?e.table.columns-1:0)?se(t.getCellNodeFromCordsOrThrow(n+(l?1:-1),o,e.table),l):o!==(l?e.table.rows-1:0)?se(t.getCellNodeFromCordsOrThrow(l?0:e.table.columns-1,o+(l?1:-1),e.table),l):l?t.selectNext():t.selectPrevious(),!0;case"up":return 0!==o?se(t.getCellNodeFromCordsOrThrow(n,o-1,e.table),!1):t.selectPrevious(),!0;case"down":return o!==e.table.rows-1?se(t.getCellNodeFromCordsOrThrow(n,o+1,e.table),!0):t.selectNext(),!0;default:return!1}};function ne(e,t){let n,o;if(t.startColumn===e.minColumn)n="minColumn";else{if(t.startColumn+t.cell.__colSpan-1!==e.maxColumn)return null;n="maxColumn"}if(t.startRow===e.minRow)o="minRow";else{if(t.startRow+t.cell.__rowSpan-1!==e.maxRow)return null;o="maxRow"}return[n,o]}function oe([e,t]){return["minColumn"===e?"maxColumn":"minColumn","minRow"===t?"maxRow":"minRow"]}function re(e,t,[n,o]){const r=t[o],l=e[r];void 0===l&&d(250,o,String(r));const i=t[n],s=l[i];return void 0===s&&d(250,n,String(i)),s}function le(e,t,n,o,r){const l=x(t,n,o),i=function(e,t){const{minColumn:n,maxColumn:o,minRow:r,maxRow:l}=t;let i=1,s=1,a=1,c=1;const u=e[r],d=e[l];for(let e=n;e<=o;e++)i=Math.max(i,u[e].cell.__rowSpan),c=Math.max(c,d[e].cell.__rowSpan);for(let t=r;t<=l;t++)s=Math.max(s,e[t][n].cell.__colSpan),a=Math.max(a,e[t][o].cell.__colSpan);return{bottomSpan:c,leftSpan:s,rightSpan:a,topSpan:i}}(t,l),{topSpan:s,leftSpan:a,bottomSpan:c,rightSpan:u}=i,h=function(e,t){const n=ne(e,t);return null===n&&d(249,t.cell.getKey()),n}(l,n),[g,f]=oe(h);let m=l[g],C=l[f];"forward"===r?m+="maxColumn"===g?1:a:"backward"===r?m-="minColumn"===g?1:u:"down"===r?C+="maxRow"===f?1:s:"up"===r&&(C-="minRow"===f?1:c);const p=t[C];if(void 0===p)return!1;const _=p[m];if(void 0===_)return!1;const[S,N]=function(e,t,n){const o=x(e,t,n),r=ne(o,t);if(r)return[re(e,o,r),re(e,o,oe(r))];const l=ne(o,n);if(l)return[re(e,o,oe(l)),re(e,o,l)];const i=["minColumn","minRow"];return[re(e,o,i),re(e,o,oe(i))]}(t,n,_),w=pe(e,S.cell),b=pe(e,N.cell);return e.$setAnchorCellForSelection(w),e.$setFocusCellForSelection(b,!0),!0}function ie(e,n){if(t.$isRangeSelection(e)||F(e)){const t=n.isParentOf(e.anchor.getNode()),o=n.isParentOf(e.focus.getNode());return t&&o}return!1}function se(e,t){t?e.selectStart():e.selectEnd()}function ae(n,o){const r=o.elem,l=n._config.theme;c(t.$getNearestNodeFromDOMNode(r))||d(131),e.addClassNamesToElement(r,l.tableCellSelected)}function ce(n,o){const r=o.elem;c(t.$getNearestNodeFromDOMNode(r))||d(131);const l=n._config.theme;e.removeClassNamesFromElement(r,l.tableCellSelected)}function ue(t){const n=e.$findMatchingParent(t,c);return c(n)?n:null}function de(t){const n=e.$findMatchingParent(t,Oe);return Oe(n)?n:null}function he(n,o,r,l,i,s,a){const u=t.$caretFromPoint(r.focus,i?"previous":"next");if(t.$isExtendableTextPointCaret(u))return!1;let d=u;for(const e of t.$extendCaretToRange(u).iterNodeCarets("shadowRoot")){if(!t.$isSiblingCaret(e)||!t.$isElementNode(e.origin))return!1;d=e}const h=d.getParentAtCaret();if(!c(h))return!1;const g=h,f=function(e){for(const n of t.$extendCaretToRange(e).iterNodeCarets("root")){const{origin:o}=n;if(c(o)){if(t.$isChildCaret(n))return t.$getChildCaret(o,e.direction)}else if(!m(o))break}return null}(t.$getSiblingCaret(g,d.direction)),C=e.$findMatchingParent(g,Oe);if(!C||!C.is(s))return!1;const p=n.getElementByKey(g.getKey()),_=G(p);if(!p||!_)return!1;const S=Me(n,C);if(a.table=S,f)if("extend"===l){const e=G(n.getElementByKey(f.origin.getKey()));if(!e)return!1;a.$setAnchorCellForSelection(_),a.$setFocusCellForSelection(e,!0)}else{const e=t.$normalizeCaret(f);t.$setPointFromCaret(r.anchor,e),t.$setPointFromCaret(r.focus,e)}else if("extend"===l)a.$setAnchorCellForSelection(_),a.$setFocusCellForSelection(_,!0);else{const e=function(e){const n=t.$getAdjacentChildCaret(e);return t.$isChildCaret(n)?t.$normalizeCaret(n):e}(t.$getSiblingCaret(C,u.direction));t.$setPointFromCaret(r.anchor,e),t.$setPointFromCaret(r.focus,e)}return fe(o),!0}function ge(n,o,r,l,i){if(("up"===r||"down"===r)&&function(e){const t=e.getRootElement();if(!t)return!1;return t.hasAttribute("aria-controls")&&"typeahead-menu"===t.getAttribute("aria-controls")}(n))return!1;const s=t.$getSelection();if(!ie(s,l)){if(t.$isRangeSelection(s)){if("backward"===r){if(s.focus.offset>0)return!1;const e=function(e){for(let n=e,o=e;null!==o;n=o,o=o.getParent())if(t.$isElementNode(o)){if(o!==n&&o.getFirstChild()!==n)return null;if(!o.isInline())return o}return null}(s.focus.getNode());if(!e)return!1;const n=e.getPreviousSibling();return!!Oe(n)&&(fe(o),o.shiftKey?s.focus.set(n.getParentOrThrow().getKey(),n.getIndexWithinParent(),"element"):n.selectEnd(),!0)}if(o.shiftKey&&("up"===r||"down"===r)){const n=s.focus.getNode();if(!s.isCollapsed()&&("up"===r&&!s.isBackward()||"down"===r&&s.isBackward())){let i=e.$findMatchingParent(n,(e=>Oe(e)));if(c(i)&&(i=e.$findMatchingParent(i,Oe)),i!==l)return!1;if(!i)return!1;const a="down"===r?i.getNextSibling():i.getPreviousSibling();if(!a)return!1;let u=0;"up"===r&&t.$isElementNode(a)&&(u=a.getChildrenSize());let d=a;if("up"===r&&t.$isElementNode(a)){const e=a.getLastChild();d=e||a,u=t.$isTextNode(d)?d.getTextContentSize():0}const h=s.clone();return h.focus.set(d.getKey(),u,t.$isTextNode(d)?"text":"element"),t.$setSelection(h),fe(o),!0}if(t.$isRootOrShadowRoot(n)){const e="up"===r?s.getNodes()[s.getNodes().length-1]:s.getNodes()[0];if(e){if(null!==z(l,e)){const e=l.getFirstDescendant(),t=l.getLastDescendant();if(!e||!t)return!1;const[n]=E(e),[o]=E(t),r=l.getCordsFromCellNode(n,i.table),s=l.getCordsFromCellNode(o,i.table),a=l.getDOMCellFromCordsOrThrow(r.x,r.y,i.table),c=l.getDOMCellFromCordsOrThrow(s.x,s.y,i.table);return i.$setAnchorCellForSelection(a),i.$setFocusCellForSelection(c,!0),!0}}return!1}{let l=e.$findMatchingParent(n,(e=>t.$isElementNode(e)&&!e.isInline()));if(c(l)&&(l=e.$findMatchingParent(l,Oe)),!l)return!1;const a="down"===r?l.getNextSibling():l.getPreviousSibling();if(Oe(a)&&i.tableNodeKey===a.getKey()){const e=a.getFirstDescendant(),n=a.getLastDescendant();if(!e||!n)return!1;const[l]=E(e),[i]=E(n),c=s.clone();return c.focus.set(("up"===r?l:i).getKey(),"up"===r?0:i.getChildrenSize(),"element"),fe(o),t.$setSelection(c),!0}}}}return"down"===r&&ye(n)&&i.setShouldCheckSelection(),!1}if(t.$isRangeSelection(s)){if("backward"===r||"forward"===r){return he(n,o,s,o.shiftKey?"extend":"move","backward"===r,l,i)}if(s.isCollapsed()){const{anchor:a,focus:u}=s,d=e.$findMatchingParent(a.getNode(),c),h=e.$findMatchingParent(u.getNode(),c);if(!c(d)||!d.is(h))return!1;const g=de(d);if(g!==l&&null!=g){const e=B(g,n.getElementByKey(g.getKey()));if(null!=e)return i.table=V(g,e),ge(n,o,r,g,i)}const f=n.getElementByKey(d.__key),m=n.getElementByKey(a.key);if(null==m||null==f)return!1;let C;if("element"===a.type)C=m.getBoundingClientRect();else{const e=t.getDOMSelection(W(n));if(null===e||0===e.rangeCount)return!1;C=e.getRangeAt(0).getBoundingClientRect()}const p="up"===r?d.getFirstChild():d.getLastChild();if(null==p)return!1;const _=n.getElementByKey(p.__key);if(null==_)return!1;const S=_.getBoundingClientRect();if("up"===r?S.top>C.top-C.height:C.bottom+C.height>S.bottom){fe(o);const e=l.getCordsFromCellNode(d,i.table);if(!o.shiftKey)return te(i,l,e.x,e.y,r);{const t=l.getDOMCellFromCordsOrThrow(e.x,e.y,i.table);i.$setAnchorCellForSelection(t),i.$setFocusCellForSelection(t,!0)}return!0}}}else if(F(s)){const{anchor:t,focus:a}=s,u=e.$findMatchingParent(t.getNode(),c),h=e.$findMatchingParent(a.getNode(),c),[g]=s.getNodes();Oe(g)||d(251);const f=B(g,n.getElementByKey(g.getKey()));if(!c(u)||!c(h)||!Oe(g)||null==f)return!1;i.$updateTableTableSelection(s);const m=V(g,f),C=l.getCordsFromCellNode(u,m),p=l.getDOMCellFromCordsOrThrow(C.x,C.y,m);if(i.$setAnchorCellForSelection(p),fe(o),o.shiftKey){const[e,t,n]=R(l,u,h);return le(i,e,t,n,r)}return h.selectEnd(),!0}return!1}function fe(e){e.preventDefault(),e.stopImmediatePropagation(),e.stopPropagation()}function me(e,n,o){const r=t.$createParagraphNode();"first"===e?n.insertBefore(r):n.insertAfter(r),r.append(...o||[]),r.selectEnd()}function Ce(n,o,r){const l=r.getParent();if(!l)return;const i=t.getDOMSelection(W(n));if(!i)return;const s=i.anchorNode,a=n.getElementByKey(l.getKey()),u=B(r,n.getElementByKey(r.getKey()));if(!s||!a||!u||!a.contains(s)||u.contains(s))return;const d=e.$findMatchingParent(o.anchor.getNode(),(e=>c(e)));if(!d)return;const h=e.$findMatchingParent(d,(e=>Oe(e)));if(!Oe(h)||!h.is(r))return;const[g,f]=R(r,d,d),m=g[0][0],C=g[g.length-1][g[0].length-1],{startRow:p,startColumn:_}=f,S=p===m.startRow&&_===m.startColumn,N=p===C.startRow&&_===C.startColumn;return S?"first":N?"last":void 0}function pe(e,t){const{tableNode:n}=e.$lookup(),o=n.getCordsFromCellNode(t,e.table);return n.getDOMCellFromCordsOrThrow(o.x,o.y,e.table)}function _e(e,n,o){return z(e,t.$getNearestNodeFromDOMNode(n,o))}function Se(e,t,n,o){const r=e.querySelector("colgroup");if(!r)return;const l=[];for(let e=0;e<n;e++){const t=document.createElement("col"),n=o&&o[e];n&&(t.style.width=`${n}px`),l.push(t)}r.replaceChildren(...l)}function Ne(t,n,o){o?(e.addClassNamesToElement(t,n.theme.tableRowStriping),t.setAttribute("data-lexical-row-striping","true")):(e.removeClassNamesFromElement(t,n.theme.tableRowStriping),t.removeAttribute("data-lexical-row-striping"))}function we(t,n,o){o>0?(e.addClassNamesToElement(t,n.theme.tableFrozenColumn),t.setAttribute("data-lexical-frozen-column","true")):(e.removeClassNamesFromElement(t,n.theme.tableFrozenColumn),t.removeAttribute("data-lexical-frozen-column"))}function be(t,n,o){o>0?(e.addClassNamesToElement(t,n.theme.tableFrozenRow),t.setAttribute("data-lexical-frozen-row","true")):(e.removeClassNamesFromElement(t,n.theme.tableFrozenRow),t.removeAttribute("data-lexical-frozen-row"))}function Te(t,n,o){if(!n.theme.tableAlignment)return;const r=[],l=[];for(const e of["center","right"]){const t=n.theme.tableAlignment[e];t&&(e===o?l:r).push(t)}e.removeClassNamesFromElement(t,...r),e.addClassNamesToElement(t,...l)}const $e=new WeakSet;function ye(e=t.$getEditor()){return $e.has(e)}class Re extends t.ElementNode{static getType(){return"table"}getColWidths(){return this.getLatest().__colWidths}setColWidths(e){const t=this.getWritable();return t.__colWidths=e,t}static clone(e){return new Re(e.__key)}afterCloneFrom(e){super.afterCloneFrom(e),this.__colWidths=e.__colWidths,this.__rowStriping=e.__rowStriping,this.__frozenColumnCount=e.__frozenColumnCount,this.__frozenRowCount=e.__frozenRowCount}static importDOM(){return{table:e=>({conversion:Ee,priority:1})}}static importJSON(e){return xe().updateFromJSON(e)}updateFromJSON(e){return super.updateFromJSON(e).setRowStriping(e.rowStriping||!1).setFrozenColumns(e.frozenColumnCount||0).setFrozenRows(e.frozenRowCount||0).setColWidths(e.colWidths)}constructor(e){super(e),this.__rowStriping=!1,this.__frozenColumnCount=0,this.__frozenRowCount=0}exportJSON(){return{...super.exportJSON(),colWidths:this.getColWidths(),frozenColumnCount:this.__frozenColumnCount?this.__frozenColumnCount:void 0,frozenRowCount:this.__frozenRowCount?this.__frozenRowCount:void 0,rowStriping:this.__rowStriping?this.__rowStriping:void 0}}extractWithChild(e,t,n){return"html"===n}getDOMSlot(e){const t="TABLE"!==e.nodeName&&e.querySelector("table")||e;return"TABLE"!==t.nodeName&&d(229),super.getDOMSlot(t).withAfter(t.querySelector("colgroup"))}createDOM(n,o){const r=document.createElement("table");this.__style&&(r.style.cssText=this.__style);const l=document.createElement("colgroup");if(r.appendChild(l),Se(r,0,this.getColumnCount(),this.getColWidths()),t.setDOMUnmanaged(l),e.addClassNamesToElement(r,n.theme.table),Te(r,n,this.getFormatType()),this.__frozenColumnCount&&we(r,n,this.__frozenColumnCount),this.__frozenRowCount&&be(r,n,this.__frozenRowCount),this.__rowStriping&&Ne(r,n,!0),ye(o)){const t=document.createElement("div"),o=n.theme.tableScrollableWrapper;return o?e.addClassNamesToElement(t,o):t.style.cssText="overflow-x: auto;",t.appendChild(r),t}return r}updateDOM(e,t,n){e.__rowStriping!==this.__rowStriping&&Ne(t,n,this.__rowStriping),e.__frozenColumnCount!==this.__frozenColumnCount&&we(t,n,this.__frozenColumnCount),e.__frozenRowCount!==this.__frozenRowCount&&be(t,n,this.__frozenRowCount),Se(t,0,this.getColumnCount(),this.getColWidths());const o=this.getDOMSlot(t).element;return e.__style!==this.__style&&(o.style.cssText=this.__style),Te(o,n,this.getFormatType()),!1}exportDOM(t){const n=super.exportDOM(t),{element:o}=n;return{after:o=>{if(n.after&&(o=n.after(o),this.__format&&Te(o,t._config,this.getFormatType())),e.isHTMLElement(o)&&"TABLE"!==o.nodeName&&(o=o.querySelector("table")),!e.isHTMLElement(o))return null;const[r]=M(this,null,null),l=new Map;for(const e of r)for(const t of e){const e=t.cell.getKey();l.has(e)||l.set(e,{colSpan:t.cell.getColSpan(),startColumn:t.startColumn})}const i=new Set;for(const e of o.querySelectorAll(":scope > tr > [data-temporary-table-cell-lexical-key]")){const t=e.getAttribute("data-temporary-table-cell-lexical-key");if(t){const n=l.get(t);if(e.removeAttribute("data-temporary-table-cell-lexical-key"),n){l.delete(t);for(let e=0;e<n.colSpan;e++)i.add(e+n.startColumn)}}}const s=o.querySelector(":scope > colgroup");if(s){const e=Array.from(o.querySelectorAll(":scope > colgroup > col")).filter(((e,t)=>i.has(t)));s.replaceChildren(...e)}const a=o.querySelectorAll(":scope > tr");if(a.length>0){const e=document.createElement("tbody");for(const t of a)e.appendChild(t);o.append(e)}return o},element:e.isHTMLElement(o)&&"TABLE"!==o.nodeName?o.querySelector("table"):o}}canBeEmpty(){return!1}isShadowRoot(){return!0}getCordsFromCellNode(e,t){const{rows:n,domRows:o}=t;for(let t=0;t<n;t++){const n=o[t];if(null!=n)for(let o=0;o<n.length;o++){const r=n[o];if(null==r)continue;const{elem:l}=r,i=_e(this,l);if(null!==i&&e.is(i))return{x:o,y:t}}}throw new Error("Cell not found in table.")}getDOMCellFromCords(e,t,n){const{domRows:o}=n,r=o[t];if(null==r)return null;const l=r[e<r.length?e:r.length-1];return null==l?null:l}getDOMCellFromCordsOrThrow(e,t,n){const o=this.getDOMCellFromCords(e,t,n);if(!o)throw new Error("Cell not found at cords.");return o}getCellNodeFromCords(e,n,o){const r=this.getDOMCellFromCords(e,n,o);if(null==r)return null;const l=t.$getNearestNodeFromDOMNode(r.elem);return c(l)?l:null}getCellNodeFromCordsOrThrow(e,t,n){const o=this.getCellNodeFromCords(e,t,n);if(!o)throw new Error("Node at cords not TableCellNode.");return o}getRowStriping(){return Boolean(this.getLatest().__rowStriping)}setRowStriping(e){const t=this.getWritable();return t.__rowStriping=e,t}setFrozenColumns(e){const t=this.getWritable();return t.__frozenColumnCount=e,t}getFrozenColumns(){return this.getLatest().__frozenColumnCount}setFrozenRows(e){const t=this.getWritable();return t.__frozenRowCount=e,t}getFrozenRows(){return this.getLatest().__frozenRowCount}canSelectBefore(){return!0}canIndent(){return!1}getColumnCount(){const e=this.getFirstChild();if(!e)return 0;let t=0;return e.getChildren().forEach((e=>{c(e)&&(t+=e.getColSpan())})),t}}function Me(e,t){const n=e.getElementByKey(t.getKey());return null===n&&d(230),V(t,n)}function Ee(t){const n=xe();t.hasAttribute("data-lexical-row-striping")&&n.setRowStriping(!0);const r=t.querySelector(":scope > colgroup");if(r){let e=[];for(const t of r.querySelectorAll(":scope > col")){let n=t.style.width||"";if(!o.test(n)&&(n=t.getAttribute("width")||"",!/^\d+$/.test(n))){e=void 0;break}e.push(parseFloat(n))}e&&n.setColWidths(e)}return{after:t=>e.$descendantsMatching(t,m),node:n}}function xe(){return t.$applyNodeReplacement(new Re)}function Oe(e){return e instanceof Re}function Ae({rows:n,columns:o,includeHeaders:r}){const l=t.$getSelection();if(!l||!t.$isRangeSelection(l))return!1;if(de(l.anchor.getNode()))return!1;const i=S(Number(n),Number(o),r);e.$insertNodeToNearestRoot(i);const s=i.getFirstDescendant();return t.$isTextNode(s)&&s.select(),!0}function ve(e){m(e.getParent())?e.isEmpty()&&e.append(t.$createParagraphNode()):e.remove()}function Fe(t){Oe(t.getParent())?e.$unwrapAndFilterDescendants(t,c):t.remove()}function Pe(n){e.$unwrapAndFilterDescendants(n,m);const[o]=M(n,null,null),r=o.reduce(((e,t)=>Math.max(e,t.length)),0),l=n.getChildren();for(let e=0;e<o.length;++e){const n=l[e];if(!n)continue;m(n)||d(254,n.constructor.name,n.getType());const i=o[e].reduce(((e,t)=>t?1+e:e),0);if(i!==r)for(let e=i;e<r;++e){const e=a();e.append(t.$createParagraphNode()),n.append(e)}}}function De(n){if(n.detail<3||!t.isDOMNode(n.target))return!1;const o=t.$getNearestNodeFromDOMNode(n.target);if(null===o)return!1;const r=e.$findMatchingParent(o,(e=>t.$isElementNode(e)&&!e.isInline()));if(null===r)return!1;return!!c(r.getParent())&&(r.select(0),!0)}exports.$computeTableMap=R,exports.$computeTableMapSkipCellCheck=M,exports.$createTableCellNode=a,exports.$createTableNode=xe,exports.$createTableNodeWithDimensions=S,exports.$createTableRowNode=f,exports.$createTableSelection=P,exports.$createTableSelectionFrom=D,exports.$deleteTableColumn=function(e,t){const n=e.getChildren();for(let e=0;e<n.length;e++){const o=n[e];if(m(o)){const e=o.getChildren();if(t>=e.length||t<0)throw new Error("Table column target index out of range");e[t].remove()}}return e},exports.$deleteTableColumn__EXPERIMENTAL=function(){const e=t.$getSelection();t.$isRangeSelection(e)||F(e)||d(188);const n=e.anchor.getNode(),o=e.focus.getNode(),[r,,l]=E(n),[i]=E(o),[s,a,c]=R(l,r,i),{startColumn:u}=a,{startRow:h,startColumn:g}=c,f=Math.min(u,g),m=Math.max(u+r.__colSpan-1,g+i.__colSpan-1),C=m-f+1;if(s[0].length===m-f+1)return l.selectPrevious(),void l.remove();const p=s.length;for(let e=0;e<p;e++)for(let t=f;t<=m;t++){const{cell:n,startColumn:o}=s[e][t];if(o<f){if(t===f){const e=f-o;n.setColSpan(n.__colSpan-Math.min(C,n.__colSpan-e))}}else if(o+n.__colSpan-1>m){if(t===m){const e=m-o+1;n.setColSpan(n.__colSpan-e)}}else n.remove()}const _=s[h],S=u>g?_[u+r.__colSpan]:_[g+i.__colSpan];if(void 0!==S){const{cell:e}=S;$(e)}else{const e=g<u?_[g-1]:_[u-1],{cell:t}=e;$(t)}const N=l.getColWidths();if(N){const e=[...N];e.splice(f,C),l.setColWidths(e)}},exports.$deleteTableRow__EXPERIMENTAL=function(){const e=t.$getSelection();t.$isRangeSelection(e)||F(e)||d(188);const[n,o]=e.isBackward()?[e.focus.getNode(),e.anchor.getNode()]:[e.anchor.getNode(),e.focus.getNode()],[r,,l]=E(n),[i]=E(o),[s,a,c]=R(l,r,i),{startRow:u}=a,{startRow:h}=c,g=h+i.__rowSpan-1;if(s.length===g-u+1)return void l.remove();const f=s[0].length,C=r.__rowSpan,p=s[g+1],_=l.getChildAtIndex(g+1);for(let e=g;e>=u;e--){for(let t=f-1;t>=0;t--){const{cell:n,startRow:o,startColumn:r}=s[e][t];if(r===t){if(e===u&&o<u){const e=u-o;n.setRowSpan(n.__rowSpan-Math.min(C,n.__rowSpan-e))}if(o>=u&&o+n.__rowSpan-1>g){n.setRowSpan(n.__rowSpan-(g-o+1)),null===_&&d(122);let r=null;for(let n=0;n<t;n++){const t=p[n],o=t.cell;t.startRow===e+1&&(r=o),o.__colSpan>1&&(n+=o.__colSpan-1)}null===r?y(_,n):r.insertAfter(n)}}}const t=l.getChildAtIndex(e);m(t)||d(206,String(e)),t.remove()}if(void 0!==p){const{cell:e}=p[0];$(e)}else{const e=s[u-1],{cell:t}=e[0];$(t)}},exports.$findCellNode=ue,exports.$findTableNode=de,exports.$getElementForTableNode=Me,exports.$getNodeTriplet=E,exports.$getTableAndElementByKey=K,exports.$getTableCellNodeFromLexicalNode=function(t){const n=e.$findMatchingParent(t,(e=>c(e)));return c(n)?n:null},exports.$getTableCellNodeRect=O,exports.$getTableColumnIndexFromTableCellNode=function(e){return N(e).getChildren().findIndex((t=>t.is(e)))},exports.$getTableNodeFromLexicalNodeOrThrow=w,exports.$getTableRowIndexFromTableCellNode=function(e){const t=N(e);return w(t).getChildren().findIndex((e=>e.is(t)))},exports.$getTableRowNodeFromTableCellNodeOrThrow=N,exports.$insertTableColumn=function(e,n,o=!0,l,i){const s=e.getChildren(),u=[];for(let e=0;e<s.length;e++){const o=s[e];if(m(o))for(let e=0;e<l;e++){const e=o.getChildren();if(n>=e.length||n<0)throw new Error("Table column target index out of range");const l=e[n];c(l)||d(12);const{left:s,right:h}=b(l,i);let g=r.NO_STATUS;(s&&s.hasHeaderState(r.ROW)||h&&h.hasHeaderState(r.ROW))&&(g|=r.ROW);const f=a(g);f.append(t.$createParagraphNode()),u.push({newTableCell:f,targetCell:l})}}return u.forEach((({newTableCell:e,targetCell:t})=>{o?t.insertAfter(e):t.insertBefore(e)})),e},exports.$insertTableColumn__EXPERIMENTAL=function(e=!0){const n=t.$getSelection();t.$isRangeSelection(n)||F(n)||d(188);const o=n.anchor.getNode(),l=n.focus.getNode(),[i]=E(o),[s,,c]=E(l),[u,h,g]=R(c,s,i),f=u.length,C=e?Math.max(h.startColumn,g.startColumn):Math.min(h.startColumn,g.startColumn),p=e?C+s.__colSpan-1:C-1,_=c.getFirstChild();m(_)||d(120);let S=null;function N(e=r.NO_STATUS){const n=a(e).append(t.$createParagraphNode());return null===S&&(S=n),n}let w=_;e:for(let e=0;e<f;e++){if(0!==e){const e=w.getNextSibling();m(e)||d(121),w=e}const t=u[e],n=t[p<0?0:p].cell.__headerState,o=T(n,r.ROW);if(p<0){y(w,N(o));continue}const{cell:l,startColumn:i,startRow:s}=t[p];if(i+l.__colSpan-1<=p){let n=l,r=s,i=p;for(;r!==e&&n.__rowSpan>1;){if(i-=l.__colSpan,!(i>=0)){w.append(N(o));continue e}{const{cell:e,startRow:o}=t[i];n=e,r=o}}n.insertAfter(N(o))}else l.setColSpan(l.__colSpan+1)}null!==S&&$(S);const b=c.getColWidths();if(b){const e=[...b],t=p<0?0:p,n=e[t];e.splice(t,0,n),c.setColWidths(e)}return S},exports.$insertTableRow=function(e,n,o=!0,l,i){const s=e.getChildren();if(n>=s.length||n<0)throw new Error("Table row target index out of range");const u=s[n];if(!m(u))throw new Error("Row before insertion index does not exist.");for(let e=0;e<l;e++){const e=u.getChildren(),n=e.length,l=f();for(let o=0;o<n;o++){const n=e[o];c(n)||d(12);const{above:s,below:u}=b(n,i);let h=r.NO_STATUS;const g=s&&s.getWidth()||u&&u.getWidth()||void 0;(s&&s.hasHeaderState(r.COLUMN)||u&&u.hasHeaderState(r.COLUMN))&&(h|=r.COLUMN);const f=a(h,1,g);f.append(t.$createParagraphNode()),l.append(f)}o?u.insertAfter(l):u.insertBefore(l)}return e},exports.$insertTableRow__EXPERIMENTAL=function(e=!0){const n=t.$getSelection();t.$isRangeSelection(n)||F(n)||d(188);const o=n.anchor.getNode(),l=n.focus.getNode(),[i]=E(o),[s,,c]=E(l),[u,h,g]=R(c,s,i),C=u[0].length,{startRow:p}=g,{startRow:_}=h;let S=null;if(e){const e=Math.max(_+s.__rowSpan,p+i.__rowSpan)-1,n=u[e],o=f();for(let l=0;l<C;l++){const{cell:i,startRow:s}=n[l];if(s+i.__rowSpan-1<=e){const e=n[l].cell.__headerState,i=T(e,r.COLUMN);o.append(a(i).append(t.$createParagraphNode()))}else i.setRowSpan(i.__rowSpan+1)}const l=c.getChildAtIndex(e);m(l)||d(256),l.insertAfter(o),S=o}else{const e=Math.min(_,p),n=u[e],o=f();for(let l=0;l<C;l++){const{cell:i,startRow:s}=n[l];if(s===e){const e=n[l].cell.__headerState,i=T(e,r.COLUMN);o.append(a(i).append(t.$createParagraphNode()))}else i.setRowSpan(i.__rowSpan+1)}const l=c.getChildAtIndex(e);m(l)||d(257),l.insertBefore(o),S=o}return S},exports.$isScrollableTablesActive=ye,exports.$isTableCellNode=c,exports.$isTableNode=Oe,exports.$isTableRowNode=m,exports.$isTableSelection=F,exports.$removeTableRowAtIndex=function(e,t){const n=e.getChildren();if(t>=n.length||t<0)throw new Error("Expected table cell to be inside of table row.");return n[t].remove(),e},exports.$unmergeCell=function(){const e=t.$getSelection();t.$isRangeSelection(e)||F(e)||d(188);const n=e.anchor.getNode(),[o,l,i]=E(n),s=o.__colSpan,c=o.__rowSpan;if(1===s&&1===c)return;const[u,h]=R(i,o,o),{startColumn:g,startRow:f}=h,C=o.__headerState&r.COLUMN,p=Array.from({length:s},((e,t)=>{let n=C;for(let e=0;0!==n&&e<u.length;e++)n&=u[e][t+g].cell.__headerState;return n})),_=o.__headerState&r.ROW,S=Array.from({length:c},((e,t)=>{let n=_;for(let e=0;0!==n&&e<u[0].length;e++)n&=u[t+f][e].cell.__headerState;return n}));if(s>1){for(let e=1;e<s;e++)o.insertAfter(a(p[e]|S[0]).append(t.$createParagraphNode()));o.setColSpan(1)}if(c>1){let e;for(let n=1;n<c;n++){const o=f+n,r=u[o];e=(e||l).getNextSibling(),m(e)||d(125);let i=null;for(let e=0;e<g;e++){const t=r[e],n=t.cell;t.startRow===o&&(i=n),n.__colSpan>1&&(e+=n.__colSpan-1)}if(null===i)for(let o=s-1;o>=0;o--)y(e,a(p[o]|S[n]).append(t.$createParagraphNode()));else for(let e=s-1;e>=0;e--)i.insertAfter(a(p[e]|S[n]).append(t.$createParagraphNode()))}o.setRowSpan(1)}},exports.INSERT_TABLE_COMMAND=u,exports.TableCellHeaderStates=r,exports.TableCellNode=l,exports.TableNode=Re,exports.TableObserver=L,exports.TableRowNode=h,exports.applyTableHandlers=J,exports.getDOMCellFromTarget=G,exports.getTableElement=B,exports.getTableObserverFromTableElement=q,exports.registerTableCellUnmergeTransform=function(t){return t.registerNodeTransform(l,(t=>{if(t.getColSpan()>1||t.getRowSpan()>1){const[,,n]=E(t),[o]=R(n,t,t),r=o.length,l=o[0].length;let i=n.getFirstChild();m(i)||d(175);const s=[];for(let t=0;t<r;t++){0!==t&&(i=i.getNextSibling(),m(i)||d(175));let n=null;for(let r=0;r<l;r++){const l=o[t][r],u=l.cell;if(l.startRow===t&&l.startColumn===r)n=u,s.push(u);else if(u.getColSpan()>1||u.getRowSpan()>1){c(u)||d(176);const t=a(u.__headerState);null!==n?n.insertAfter(t):e.$insertFirst(i,t)}}}for(const e of s)e.setColSpan(1),e.setRowSpan(1)}}))},exports.registerTablePlugin=function(n){return n.hasNodes([Re])||d(255),e.mergeRegister(n.registerCommand(u,Ae,t.COMMAND_PRIORITY_EDITOR),n.registerCommand(t.SELECTION_INSERT_CLIPBOARD_NODES_COMMAND,(({nodes:e,selection:n})=>{if(!t.$isRangeSelection(n))return!1;return null!==de(n.anchor.getNode())&&e.some(Oe)}),t.COMMAND_PRIORITY_EDITOR),n.registerCommand(t.CLICK_COMMAND,De,t.COMMAND_PRIORITY_EDITOR),n.registerNodeTransform(Re,Pe),n.registerNodeTransform(h,Fe),n.registerNodeTransform(l,ve))},exports.registerTableSelectionObserver=function(e,t=!0){const n=new Map,o=(o,r,l)=>{const i=B(o,l),s=J(o,i,e,t);n.set(r,[s,i])},r=e.registerMutationListener(Re,(t=>{e.getEditorState().read((()=>{for(const[e,r]of t){const t=n.get(e);if("created"===r||"updated"===r){const{tableNode:r,tableElement:l}=K(e);void 0===t?o(r,e,l):l!==t[1]&&(t[0].removeListeners(),n.delete(e),o(r,e,l))}else"destroyed"===r&&void 0!==t&&(t[0].removeListeners(),n.delete(e))}}),{editor:e})}),{skipInitialization:!1});return()=>{r();for(const[,[e]]of n)e.removeListeners()}},exports.setScrollableTablesActive=function(e,t){t?$e.add(e):$e.delete(e)};
