{"name": "@lexical/table", "description": "This package provides the Table feature for Lexical.", "keywords": ["lexical", "editor", "rich-text", "table"], "license": "MIT", "version": "0.27.2", "main": "LexicalTable.js", "types": "index.d.ts", "dependencies": {"@lexical/clipboard": "0.27.2", "@lexical/utils": "0.27.2", "lexical": "0.27.2"}, "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-table"}, "module": "LexicalTable.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalTable.dev.mjs", "production": "./LexicalTable.prod.mjs", "node": "./LexicalTable.node.mjs", "default": "./LexicalTable.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalTable.dev.js", "production": "./LexicalTable.prod.js", "default": "./LexicalTable.js"}}}}