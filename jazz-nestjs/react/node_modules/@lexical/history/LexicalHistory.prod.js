/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var t=require("@lexical/utils"),e=require("lexical");const n=0,r=1,o=2,i=0,s=1,c=2,a=3,u=4;function d(t,n,r,o,d){if(null===t||0===r.size&&0===o.size&&!d)return i;const l=n._selection,_=t._selection;if(d)return s;if(!(e.$isRangeSelection(l)&&e.$isRangeSelection(_)&&_.isCollapsed()&&l.isCollapsed()))return i;const f=function(t,n,r){const o=t._nodeMap,i=[];for(const t of n){const e=o.get(t);void 0!==e&&i.push(e)}for(const[t,n]of r){if(!n)continue;const r=o.get(t);void 0===r||e.$isRootNode(r)||i.push(r)}return i}(n,r,o);if(0===f.length)return i;if(f.length>1){const r=n._nodeMap,o=r.get(l.anchor.key),s=r.get(_.anchor.key);return o&&s&&!t._nodeMap.has(o.__key)&&e.$isTextNode(o)&&1===o.__text.length&&1===l.anchor.offset?c:i}const p=f[0],C=t._nodeMap.get(p.__key);if(!e.$isTextNode(C)||!e.$isTextNode(p)||C.__mode!==p.__mode)return i;const h=C.__text,O=p.__text;if(h===O)return i;const N=l.anchor,M=_.anchor;if(N.key!==M.key||"text"!==N.type)return i;const g=N.offset,m=M.offset,D=O.length-h.length;return 1===D&&m===g-1?c:-1===D&&m===g+1?a:-1===D&&m===g?u:i}function l(t,s){let c=Date.now(),a=i;return(u,l,_,f,p,C)=>{const h=Date.now();if(C.has("historic"))return a=i,c=h,o;const O=d(u,l,f,p,t.isComposing()),N=(()=>{const d=null===_||_.editor===t,N=C.has("history-push");if(!N&&d&&C.has("history-merge"))return n;if(null===u)return r;const M=l._selection;if(!(f.size>0||p.size>0))return null!==M?n:o;if(!1===N&&O!==i&&O===a&&h<c+s&&d)return n;if(1===f.size){if(function(t,n,r){const o=n._nodeMap.get(t),i=r._nodeMap.get(t),s=n._selection,c=r._selection;return!(e.$isRangeSelection(s)&&e.$isRangeSelection(c)&&"element"===s.anchor.type&&"element"===s.focus.type&&"text"===c.anchor.type&&"text"===c.focus.type||!e.$isTextNode(o)||!e.$isTextNode(i)||o.__parent!==i.__parent)&&JSON.stringify(n.read((()=>o.exportJSON())))===JSON.stringify(r.read((()=>i.exportJSON())))}(Array.from(f)[0],u,l))return n}return r})();return c=h,a=O,N}}function _(t){t.undoStack=[],t.redoStack=[],t.current=null}exports.createEmptyHistoryState=function(){return{current:null,redoStack:[],undoStack:[]}},exports.registerHistory=function(n,i,s){const c=l(n,s);return t.mergeRegister(n.registerCommand(e.UNDO_COMMAND,(()=>(function(t,n){const r=n.redoStack,o=n.undoStack;if(0!==o.length){const i=n.current,s=o.pop();null!==i&&(r.push(i),t.dispatchCommand(e.CAN_REDO_COMMAND,!0)),0===o.length&&t.dispatchCommand(e.CAN_UNDO_COMMAND,!1),n.current=s||null,s&&s.editor.setEditorState(s.editorState,{tag:"historic"})}}(n,i),!0)),e.COMMAND_PRIORITY_EDITOR),n.registerCommand(e.REDO_COMMAND,(()=>(function(t,n){const r=n.redoStack,o=n.undoStack;if(0!==r.length){const i=n.current;null!==i&&(o.push(i),t.dispatchCommand(e.CAN_UNDO_COMMAND,!0));const s=r.pop();0===r.length&&t.dispatchCommand(e.CAN_REDO_COMMAND,!1),n.current=s||null,s&&s.editor.setEditorState(s.editorState,{tag:"historic"})}}(n,i),!0)),e.COMMAND_PRIORITY_EDITOR),n.registerCommand(e.CLEAR_EDITOR_COMMAND,(()=>(_(i),!1)),e.COMMAND_PRIORITY_EDITOR),n.registerCommand(e.CLEAR_HISTORY_COMMAND,(()=>(_(i),n.dispatchCommand(e.CAN_REDO_COMMAND,!1),n.dispatchCommand(e.CAN_UNDO_COMMAND,!1),!0)),e.COMMAND_PRIORITY_EDITOR),n.registerUpdateListener((({editorState:t,prevEditorState:s,dirtyLeaves:a,dirtyElements:u,tags:d})=>{const l=i.current,_=i.redoStack,f=i.undoStack,p=null===l?null:l.editorState;if(null!==l&&t===p)return;const C=c(s,t,l,a,u,d);if(C===r)0!==_.length&&(i.redoStack=[],n.dispatchCommand(e.CAN_REDO_COMMAND,!1)),null!==l&&(f.push({...l}),n.dispatchCommand(e.CAN_UNDO_COMMAND,!0));else if(C===o)return;i.current={editor:n,editorState:t}})))};
