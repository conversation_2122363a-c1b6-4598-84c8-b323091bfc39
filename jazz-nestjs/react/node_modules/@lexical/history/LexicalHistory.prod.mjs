/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{mergeRegister as t}from"@lexical/utils";import{UNDO_COMMAND as e,COMMAND_PRIORITY_EDITOR as n,REDO_COMMAND as r,CLEAR_EDITOR_COMMAND as o,CLEAR_HISTORY_COMMAND as i,CAN_REDO_COMMAND as s,CAN_UNDO_COMMAND as c,$isRangeSelection as a,$isTextNode as u,$isRootNode as d}from"lexical";const l=0,f=1,p=2,h=0,m=1,g=2,_=3,S=4;function y(t,e,n,r,o){if(null===t||0===n.size&&0===r.size&&!o)return h;const i=e._selection,s=t._selection;if(o)return m;if(!(a(i)&&a(s)&&s.isCollapsed()&&i.isCollapsed()))return h;const c=function(t,e,n){const r=t._nodeMap,o=[];for(const t of e){const e=r.get(t);void 0!==e&&o.push(e)}for(const[t,e]of n){if(!e)continue;const n=r.get(t);void 0===n||d(n)||o.push(n)}return o}(e,n,r);if(0===c.length)return h;if(c.length>1){const n=e._nodeMap,r=n.get(i.anchor.key),o=n.get(s.anchor.key);return r&&o&&!t._nodeMap.has(r.__key)&&u(r)&&1===r.__text.length&&1===i.anchor.offset?g:h}const l=c[0],f=t._nodeMap.get(l.__key);if(!u(f)||!u(l)||f.__mode!==l.__mode)return h;const p=f.__text,y=l.__text;if(p===y)return h;const k=i.anchor,C=s.anchor;if(k.key!==C.key||"text"!==k.type)return h;const x=k.offset,M=C.offset,z=y.length-p.length;return 1===z&&M===x-1?g:-1===z&&M===x+1?_:-1===z&&M===x?S:h}function k(t,e){let n=Date.now(),r=h;return(o,i,s,c,d,m)=>{const g=Date.now();if(m.has("historic"))return r=h,n=g,p;const _=y(o,i,c,d,t.isComposing()),S=(()=>{const S=null===s||s.editor===t,y=m.has("history-push");if(!y&&S&&m.has("history-merge"))return l;if(null===o)return f;const k=i._selection;if(!(c.size>0||d.size>0))return null!==k?l:p;if(!1===y&&_!==h&&_===r&&g<n+e&&S)return l;if(1===c.size){if(function(t,e,n){const r=e._nodeMap.get(t),o=n._nodeMap.get(t),i=e._selection,s=n._selection;return!(a(i)&&a(s)&&"element"===i.anchor.type&&"element"===i.focus.type&&"text"===s.anchor.type&&"text"===s.focus.type||!u(r)||!u(o)||r.__parent!==o.__parent)&&JSON.stringify(e.read((()=>r.exportJSON())))===JSON.stringify(n.read((()=>o.exportJSON())))}(Array.from(c)[0],o,i))return l}return f})();return n=g,r=_,S}}function C(t){t.undoStack=[],t.redoStack=[],t.current=null}function x(a,u,d){const l=k(a,d),h=t(a.registerCommand(e,(()=>(function(t,e){const n=e.redoStack,r=e.undoStack;if(0!==r.length){const o=e.current,i=r.pop();null!==o&&(n.push(o),t.dispatchCommand(s,!0)),0===r.length&&t.dispatchCommand(c,!1),e.current=i||null,i&&i.editor.setEditorState(i.editorState,{tag:"historic"})}}(a,u),!0)),n),a.registerCommand(r,(()=>(function(t,e){const n=e.redoStack,r=e.undoStack;if(0!==n.length){const o=e.current;null!==o&&(r.push(o),t.dispatchCommand(c,!0));const i=n.pop();0===n.length&&t.dispatchCommand(s,!1),e.current=i||null,i&&i.editor.setEditorState(i.editorState,{tag:"historic"})}}(a,u),!0)),n),a.registerCommand(o,(()=>(C(u),!1)),n),a.registerCommand(i,(()=>(C(u),a.dispatchCommand(s,!1),a.dispatchCommand(c,!1),!0)),n),a.registerUpdateListener((({editorState:t,prevEditorState:e,dirtyLeaves:n,dirtyElements:r,tags:o})=>{const i=u.current,d=u.redoStack,h=u.undoStack,m=null===i?null:i.editorState;if(null!==i&&t===m)return;const g=l(e,t,i,n,r,o);if(g===f)0!==d.length&&(u.redoStack=[],a.dispatchCommand(s,!1)),null!==i&&(h.push({...i}),a.dispatchCommand(c,!0));else if(g===p)return;u.current={editor:a,editorState:t}})));return h}function M(){return{current:null,redoStack:[],undoStack:[]}}export{M as createEmptyHistoryState,x as registerHistory};
