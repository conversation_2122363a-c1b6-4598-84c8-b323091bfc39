/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{$sliceSelectedTextNodeContent as e}from"@lexical/selection";import{isHTMLElement as n,isBlockDomNode as t}from"@lexical/utils";import{$getRoot as o,$isElementNode as l,$cloneWithProperties as r,$isTextNode as i,isDocumentFragment as s,$isRootOrShadowRoot as c,$isBlockElementNode as u,$createLineBreakNode as f,ArtificialNode__DO_NOT_USE as a,isInlineDomNode as d,$createParagraphNode as p}from"lexical";function h(e,n){const t=n.body?n.body.childNodes:[];let o=[];const l=[];for(let n=0;n<t.length;n++){const r=t[n];if(!x.has(r.nodeName)){const n=y(r,e,l,!1);null!==n&&(o=o.concat(n))}}return function(e){for(const n of e)n.getNextSibling()instanceof a&&n.insertAfter(f());for(const n of e){const e=n.getChildren();for(const t of e)n.insertBefore(t);n.remove()}}(l),o}function m(e,n){if("undefined"==typeof document||"undefined"==typeof window&&void 0===global.window)throw new Error("To use $generateHtmlFromNodes in headless mode please initialize a headless browser implementation such as JSDom before calling this function.");const t=document.createElement("div"),l=o().getChildren();for(let o=0;o<l.length;o++){g(e,l[o],t,n)}return t.innerHTML}function g(t,o,c,u=null){let f=null===u||o.isSelected(u);const a=l(o)&&o.excludeFromCopy("html");let d=o;if(null!==u){let n=r(o);n=i(n)&&null!==u?e(u,n):n,d=n}const p=l(d)?d.getChildren():[],h=t._nodes.get(d.getType());let m;m=h&&void 0!==h.exportDOM?h.exportDOM(t,d):d.exportDOM(t);const{element:x,after:y}=m;if(!x)return!1;const w=document.createDocumentFragment();for(let e=0;e<p.length;e++){const n=p[e],r=g(t,n,w,u);!f&&l(o)&&r&&o.extractWithChild(n,u,"html")&&(f=!0)}if(f&&!a){if((n(x)||s(x))&&x.append(w),c.append(x),y){const e=y.call(d,x);e&&(s(x)?x.replaceChildren(e):x.replaceWith(e))}}else c.append(w);return f}const x=new Set(["STYLE","SCRIPT"]);function y(e,n,o,r,i=new Map,s){let h=[];if(x.has(e.nodeName))return h;let m=null;const g=function(e,n){const{nodeName:t}=e,o=n._htmlConversions.get(t.toLowerCase());let l=null;if(void 0!==o)for(const n of o){const t=n(e);null!==t&&(null===l||(l.priority||0)<=(t.priority||0))&&(l=t)}return null!==l?l.conversion:null}(e,n),b=g?g(e):null;let C=null;if(null!==b){C=b.after;const n=b.node;if(m=Array.isArray(n)?n[n.length-1]:n,null!==m){for(const[,e]of i)if(m=e(m,s),!m)break;m&&h.push(...Array.isArray(n)?n:[m])}null!=b.forChild&&i.set(e.nodeName,b.forChild)}const S=e.childNodes;let v=[];const N=(null==m||!c(m))&&(null!=m&&u(m)||r);for(let e=0;e<S.length;e++)v.push(...y(S[e],n,o,N,new Map(i),m));return null!=C&&(v=C(v)),t(e)&&(v=w(e,v,N?()=>{const e=new a;return o.push(e),e}:p)),null==m?v.length>0?h=h.concat(v):t(e)&&function(e){if(null==e.nextSibling||null==e.previousSibling)return!1;return d(e.nextSibling)&&d(e.previousSibling)}(e)&&(h=h.concat(f())):l(m)&&m.append(...v),h}function w(e,n,t){const o=e.style.textAlign,l=[];let r=[];for(let e=0;e<n.length;e++){const i=n[e];if(u(i))o&&!i.getFormat()&&i.setFormat(o),l.push(i);else if(r.push(i),e===n.length-1||e<n.length-1&&u(n[e+1])){const e=t();e.setFormat(o),e.append(...r),l.push(e),r=[]}}return l}export{m as $generateHtmlFromNodes,h as $generateNodesFromDOM};
