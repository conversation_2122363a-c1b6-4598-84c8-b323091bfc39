{"name": "@lexical/link", "description": "This package contains the functionality for Lexical links.", "keywords": ["lexical", "editor", "rich-text", "link"], "license": "MIT", "version": "0.27.2", "main": "LexicalLink.js", "types": "index.d.ts", "dependencies": {"@lexical/utils": "0.27.2", "lexical": "0.27.2"}, "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-link"}, "module": "LexicalLink.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalLink.dev.mjs", "production": "./LexicalLink.prod.mjs", "node": "./LexicalLink.node.mjs", "default": "./LexicalLink.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalLink.dev.js", "production": "./LexicalLink.prod.js", "default": "./LexicalLink.js"}}}}