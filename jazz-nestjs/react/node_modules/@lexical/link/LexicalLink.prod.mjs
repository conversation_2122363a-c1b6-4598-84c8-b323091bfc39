/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{addClassNamesToElement as t,isHTMLAnchorElement as e,$findMatchingParent as r}from"@lexical/utils";import{createCommand as n,ElementNode as i,$isRangeSelection as s,$applyNodeReplacement as l,$isElementNode as o,$getSelection as u,$normalizeSelection__EXPERIMENTAL as a,$setSelection as _}from"lexical";const c=new Set(["http:","https:","mailto:","sms:","tel:"]);class h extends i{static getType(){return"link"}static clone(t){return new h(t.__url,{rel:t.__rel,target:t.__target,title:t.__title},t.__key)}constructor(t="",e={},r){super(r);const{target:n=null,rel:i=null,title:s=null}=e;this.__url=t,this.__target=n,this.__rel=i,this.__title=s}createDOM(e){const r=document.createElement("a");return r.href=this.sanitizeUrl(this.__url),null!==this.__target&&(r.target=this.__target),null!==this.__rel&&(r.rel=this.__rel),null!==this.__title&&(r.title=this.__title),t(r,e.theme.link),r}updateDOM(t,r,n){if(e(r)){const e=this.__url,n=this.__target,i=this.__rel,s=this.__title;e!==t.__url&&(r.href=e),n!==t.__target&&(n?r.target=n:r.removeAttribute("target")),i!==t.__rel&&(i?r.rel=i:r.removeAttribute("rel")),s!==t.__title&&(s?r.title=s:r.removeAttribute("title"))}return!1}static importDOM(){return{a:t=>({conversion:g,priority:1})}}static importJSON(t){return f().updateFromJSON(t)}updateFromJSON(t){return super.updateFromJSON(t).setURL(t.url).setRel(t.rel||null).setTarget(t.target||null).setTitle(t.title||null)}sanitizeUrl(t){try{const e=new URL(t);if(!c.has(e.protocol))return"about:blank"}catch(e){return t}return t}exportJSON(){return{...super.exportJSON(),rel:this.getRel(),target:this.getTarget(),title:this.getTitle(),url:this.getURL()}}getURL(){return this.getLatest().__url}setURL(t){const e=this.getWritable();return e.__url=t,e}getTarget(){return this.getLatest().__target}setTarget(t){const e=this.getWritable();return e.__target=t,e}getRel(){return this.getLatest().__rel}setRel(t){const e=this.getWritable();return e.__rel=t,e}getTitle(){return this.getLatest().__title}setTitle(t){const e=this.getWritable();return e.__title=t,e}insertNewAfter(t,e=!0){const r=f(this.__url,{rel:this.__rel,target:this.__target,title:this.__title});return this.insertAfter(r,e),r}canInsertTextBefore(){return!1}canInsertTextAfter(){return!1}canBeEmpty(){return!1}isInline(){return!0}extractWithChild(t,e,r){if(!s(e))return!1;const n=e.anchor.getNode(),i=e.focus.getNode();return this.isParentOf(n)&&this.isParentOf(i)&&e.getTextContent().length>0}isEmailURI(){return this.__url.startsWith("mailto:")}isWebSiteURI(){return this.__url.startsWith("https://")||this.__url.startsWith("http://")}}function g(t){let r=null;if(e(t)){const e=t.textContent;(null!==e&&""!==e||t.children.length>0)&&(r=f(t.getAttribute("href")||"",{rel:t.getAttribute("rel"),target:t.getAttribute("target"),title:t.getAttribute("title")}))}return{node:r}}function f(t="",e){return l(new h(t,e))}function d(t){return t instanceof h}class p extends h{constructor(t="",e={},r){super(t,e,r),this.__isUnlinked=void 0!==e.isUnlinked&&null!==e.isUnlinked&&e.isUnlinked}static getType(){return"autolink"}static clone(t){return new p(t.__url,{isUnlinked:t.__isUnlinked,rel:t.__rel,target:t.__target,title:t.__title},t.__key)}getIsUnlinked(){return this.__isUnlinked}setIsUnlinked(t){const e=this.getWritable();return e.__isUnlinked=t,e}createDOM(t){return this.__isUnlinked?document.createElement("span"):super.createDOM(t)}updateDOM(t,e,r){return super.updateDOM(t,e,r)||t.__isUnlinked!==this.__isUnlinked}static importJSON(t){return m().updateFromJSON(t)}updateFromJSON(t){return super.updateFromJSON(t).setIsUnlinked(t.isUnlinked||!1)}static importDOM(){return null}exportJSON(){return{...super.exportJSON(),isUnlinked:this.__isUnlinked}}insertNewAfter(t,e=!0){const r=this.getParentOrThrow().insertNewAfter(t,e);if(o(r)){const t=m(this.__url,{isUnlinked:this.__isUnlinked,rel:this.__rel,target:this.__target,title:this.__title});return r.append(t),t}return null}}function m(t="",e){return l(new p(t,e))}function U(t){return t instanceof p}const O=n("TOGGLE_LINK_COMMAND");function k(t,e){if("element"===t.type){const r=t.getNode();o(r)||function(t,...e){const r=new URL("https://lexical.dev/docs/error"),n=new URLSearchParams;n.append("code",t);for(const t of e)n.append("v",t);throw r.search=n.toString(),Error(`Minified Lexical error #${t}; visit ${r.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}(252);return r.getChildren()[t.offset+e]||null}return null}function N(t,e={}){const{target:n,title:i}=e,l=void 0===e.rel?"noreferrer":e.rel,c=u();if(!s(c))return;const h=c.extract();if(null===t)return void h.forEach((t=>{const e=r(t,(t=>!U(t)&&d(t)));if(e){const t=e.getChildren();for(let r=0;r<t.length;r++)e.insertBefore(t[r]);e.remove()}}));const g=new Set,p=e=>{g.has(e.getKey())||(g.add(e.getKey()),e.setURL(t),void 0!==n&&e.setTarget(n),void 0!==l&&e.setRel(l),void 0!==i&&e.setTitle(i))};if(1===h.length){const t=S(h[0],d);if(null!==t)return p(t)}!function(t){const e=u();if(!s(e))return t();const r=a(e),n=r.isBackward(),i=k(r.anchor,n?-1:0),l=k(r.focus,n?0:-1),o=t();if(i||l){const t=u();if(s(t)){const e=t.clone();if(i){const t=i.getParent();t&&e.anchor.set(t.getKey(),i.getIndexWithinParent()+(n?1:0),"element")}if(l){const t=l.getParent();t&&e.focus.set(t.getKey(),l.getIndexWithinParent()+(n?0:1),"element")}_(a(e))}}}((()=>{let e=null;for(const r of h){if(!r.isAttached())continue;const s=S(r,d);if(s){p(s);continue}if(o(r)){if(!r.isInline())continue;if(d(r)){if(!(U(r)||null!==e&&e.getParentOrThrow().isParentOf(r))){p(r),e=r;continue}for(const t of r.getChildren())r.insertBefore(t);r.remove();continue}}const u=r.getPreviousSibling();d(u)&&u.is(e)?u.append(r):(e=f(t,{rel:l,target:n,title:i}),r.insertAfter(e),e.append(r))}}))}const x=N;function S(t,e){let r=t;for(;null!==r&&null!==r.getParent()&&!e(r);)r=r.getParentOrThrow();return e(r)?r:null}export{m as $createAutoLinkNode,f as $createLinkNode,U as $isAutoLinkNode,d as $isLinkNode,N as $toggleLink,p as AutoLinkNode,h as LinkNode,O as TOGGLE_LINK_COMMAND,x as toggleLink};
