/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var t=require("@lexical/utils"),e=require("lexical");const r=new Set(["http:","https:","mailto:","sms:","tel:"]);class n extends e.ElementNode{static getType(){return"link"}static clone(t){return new n(t.__url,{rel:t.__rel,target:t.__target,title:t.__title},t.__key)}constructor(t="",e={},r){super(r);const{target:n=null,rel:i=null,title:s=null}=e;this.__url=t,this.__target=n,this.__rel=i,this.__title=s}createDOM(e){const r=document.createElement("a");return r.href=this.sanitizeUrl(this.__url),null!==this.__target&&(r.target=this.__target),null!==this.__rel&&(r.rel=this.__rel),null!==this.__title&&(r.title=this.__title),t.addClassNamesToElement(r,e.theme.link),r}updateDOM(e,r,n){if(t.isHTMLAnchorElement(r)){const t=this.__url,n=this.__target,i=this.__rel,s=this.__title;t!==e.__url&&(r.href=t),n!==e.__target&&(n?r.target=n:r.removeAttribute("target")),i!==e.__rel&&(i?r.rel=i:r.removeAttribute("rel")),s!==e.__title&&(s?r.title=s:r.removeAttribute("title"))}return!1}static importDOM(){return{a:t=>({conversion:i,priority:1})}}static importJSON(t){return s().updateFromJSON(t)}updateFromJSON(t){return super.updateFromJSON(t).setURL(t.url).setRel(t.rel||null).setTarget(t.target||null).setTitle(t.title||null)}sanitizeUrl(t){try{const e=new URL(t);if(!r.has(e.protocol))return"about:blank"}catch(e){return t}return t}exportJSON(){return{...super.exportJSON(),rel:this.getRel(),target:this.getTarget(),title:this.getTitle(),url:this.getURL()}}getURL(){return this.getLatest().__url}setURL(t){const e=this.getWritable();return e.__url=t,e}getTarget(){return this.getLatest().__target}setTarget(t){const e=this.getWritable();return e.__target=t,e}getRel(){return this.getLatest().__rel}setRel(t){const e=this.getWritable();return e.__rel=t,e}getTitle(){return this.getLatest().__title}setTitle(t){const e=this.getWritable();return e.__title=t,e}insertNewAfter(t,e=!0){const r=s(this.__url,{rel:this.__rel,target:this.__target,title:this.__title});return this.insertAfter(r,e),r}canInsertTextBefore(){return!1}canInsertTextAfter(){return!1}canBeEmpty(){return!1}isInline(){return!0}extractWithChild(t,r,n){if(!e.$isRangeSelection(r))return!1;const i=r.anchor.getNode(),s=r.focus.getNode();return this.isParentOf(i)&&this.isParentOf(s)&&r.getTextContent().length>0}isEmailURI(){return this.__url.startsWith("mailto:")}isWebSiteURI(){return this.__url.startsWith("https://")||this.__url.startsWith("http://")}}function i(e){let r=null;if(t.isHTMLAnchorElement(e)){const t=e.textContent;(null!==t&&""!==t||e.children.length>0)&&(r=s(e.getAttribute("href")||"",{rel:e.getAttribute("rel"),target:e.getAttribute("target"),title:e.getAttribute("title")}))}return{node:r}}function s(t="",r){return e.$applyNodeReplacement(new n(t,r))}function l(t){return t instanceof n}class o extends n{constructor(t="",e={},r){super(t,e,r),this.__isUnlinked=void 0!==e.isUnlinked&&null!==e.isUnlinked&&e.isUnlinked}static getType(){return"autolink"}static clone(t){return new o(t.__url,{isUnlinked:t.__isUnlinked,rel:t.__rel,target:t.__target,title:t.__title},t.__key)}getIsUnlinked(){return this.__isUnlinked}setIsUnlinked(t){const e=this.getWritable();return e.__isUnlinked=t,e}createDOM(t){return this.__isUnlinked?document.createElement("span"):super.createDOM(t)}updateDOM(t,e,r){return super.updateDOM(t,e,r)||t.__isUnlinked!==this.__isUnlinked}static importJSON(t){return u().updateFromJSON(t)}updateFromJSON(t){return super.updateFromJSON(t).setIsUnlinked(t.isUnlinked||!1)}static importDOM(){return null}exportJSON(){return{...super.exportJSON(),isUnlinked:this.__isUnlinked}}insertNewAfter(t,r=!0){const n=this.getParentOrThrow().insertNewAfter(t,r);if(e.$isElementNode(n)){const t=u(this.__url,{isUnlinked:this.__isUnlinked,rel:this.__rel,target:this.__target,title:this.__title});return n.append(t),t}return null}}function u(t="",r){return e.$applyNodeReplacement(new o(t,r))}function a(t){return t instanceof o}const c=e.createCommand("TOGGLE_LINK_COMMAND");function _(t,r){if("element"===t.type){const n=t.getNode();e.$isElementNode(n)||function(t,...e){const r=new URL("https://lexical.dev/docs/error"),n=new URLSearchParams;n.append("code",t);for(const t of e)n.append("v",t);throw r.search=n.toString(),Error(`Minified Lexical error #${t}; visit ${r.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}(252);return n.getChildren()[t.offset+r]||null}return null}function g(r,n={}){const{target:i,title:o}=n,u=void 0===n.rel?"noreferrer":n.rel,c=e.$getSelection();if(!e.$isRangeSelection(c))return;const g=c.extract();if(null===r)return void g.forEach((e=>{const r=t.$findMatchingParent(e,(t=>!a(t)&&l(t)));if(r){const t=r.getChildren();for(let e=0;e<t.length;e++)r.insertBefore(t[e]);r.remove()}}));const h=new Set,f=t=>{h.has(t.getKey())||(h.add(t.getKey()),t.setURL(r),void 0!==i&&t.setTarget(i),void 0!==u&&t.setRel(u),void 0!==o&&t.setTitle(o))};if(1===g.length){const t=d(g[0],l);if(null!==t)return f(t)}!function(t){const r=e.$getSelection();if(!e.$isRangeSelection(r))return t();const n=e.$normalizeSelection__EXPERIMENTAL(r),i=n.isBackward(),s=_(n.anchor,i?-1:0),l=_(n.focus,i?0:-1),o=t();if(s||l){const t=e.$getSelection();if(e.$isRangeSelection(t)){const r=t.clone();if(s){const t=s.getParent();t&&r.anchor.set(t.getKey(),s.getIndexWithinParent()+(i?1:0),"element")}if(l){const t=l.getParent();t&&r.focus.set(t.getKey(),l.getIndexWithinParent()+(i?0:1),"element")}e.$setSelection(e.$normalizeSelection__EXPERIMENTAL(r))}}}((()=>{let t=null;for(const n of g){if(!n.isAttached())continue;const c=d(n,l);if(c){f(c);continue}if(e.$isElementNode(n)){if(!n.isInline())continue;if(l(n)){if(!(a(n)||null!==t&&t.getParentOrThrow().isParentOf(n))){f(n),t=n;continue}for(const t of n.getChildren())n.insertBefore(t);n.remove();continue}}const _=n.getPreviousSibling();l(_)&&_.is(t)?_.append(n):(t=s(r,{rel:u,target:i,title:o}),n.insertAfter(t),t.append(n))}}))}const h=g;function d(t,e){let r=t;for(;null!==r&&null!==r.getParent()&&!e(r);)r=r.getParentOrThrow();return e(r)?r:null}exports.$createAutoLinkNode=u,exports.$createLinkNode=s,exports.$isAutoLinkNode=a,exports.$isLinkNode=l,exports.$toggleLink=g,exports.AutoLinkNode=o,exports.LinkNode=n,exports.TOGGLE_LINK_COMMAND=c,exports.toggleLink=h;
