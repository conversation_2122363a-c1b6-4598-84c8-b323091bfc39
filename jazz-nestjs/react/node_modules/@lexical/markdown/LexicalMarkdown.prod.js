/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

"use strict";var e=require("lexical"),t=require("@lexical/list"),n=require("@lexical/rich-text"),o=require("@lexical/utils"),r=require("@lexical/code"),i=require("@lexical/link");function s(e,t){const n={};for(const o of e){const e=t(o);e&&(n[e]?n[e].push(o):n[e]=[o])}return n}function c(e){const t=s(e,(e=>e.type));return{element:t.element||[],multilineElement:t["multiline-element"]||[],textFormat:t["text-format"]||[],textMatch:t["text-match"]||[]}}const l=/[!-/:-@[-`{-~\s]/,a=/^\s{0,3}$/;function f(t){if(!e.$isParagraphNode(t))return!1;const n=t.getFirstChild();return null==n||1===t.getChildrenSize()&&e.$isTextNode(n)&&a.test(n.getTextContent())}function d(t,n,o,r){for(const e of n){if(!e.export)continue;const n=e.export(t,(e=>u(e,o,r)));if(null!=n)return n}return e.$isElementNode(t)?u(t,o,r):e.$isDecoratorNode(t)?t.getTextContent():null}function u(t,n,o,r,i){const s=[],c=t.getChildren();r||(r=[]),i||(i=[]);e:for(const t of c){for(const e of o){if(!e.export)continue;const c=e.export(t,(e=>u(e,n,o,r,[...i,...r])),((e,t)=>g(e,t,n,r,i)));if(null!=c){s.push(c);continue e}}e.$isLineBreakNode(t)?s.push("\n"):e.$isTextNode(t)?s.push(g(t,t.getTextContent(),n,r,i)):e.$isElementNode(t)?s.push(u(t,n,o,r,i)):e.$isDecoratorNode(t)&&s.push(t.getTextContent())}return s.join("")}function g(e,t,n,o,r){const i=t.trim();let s=i,c="",l="",a="";const f=p(e,!0),d=p(e,!1),u=new Set;for(const t of n){const n=t.format[0],r=t.tag;x(e,n)&&!u.has(n)&&(u.add(n),x(f,n)&&o.find((e=>e.tag===r))||(o.push({format:n,tag:r}),c+=r))}for(let t=0;t<o.length;t++){const n=x(e,o[t].format),i=x(d,o[t].format);if(n&&i)continue;const s=[...o];for(;s.length>t;){const e=s.pop();r&&e&&r.find((t=>t.tag===e.tag))||(e&&"string"==typeof e.tag&&(n?i||(a+=e.tag):l+=e.tag),o.pop())}break}return s=c+s+a,l+t.replace(i,(()=>s))}function p(t,n){let o=n?t.getPreviousSibling():t.getNextSibling();if(!o){const e=t.getParentOrThrow();e.isInline()&&(o=n?e.getPreviousSibling():e.getNextSibling())}for(;o;){if(e.$isElementNode(o)){if(!o.isInline())break;const t=n?o.getLastDescendant():o.getFirstDescendant();if(e.$isTextNode(t))return t;o=n?o.getPreviousSibling():o.getNextSibling()}if(e.$isTextNode(o))return o;if(!e.$isElementNode(o))return null}return null}function x(t,n){return e.$isTextNode(t)&&t.hasFormat(n)}const h="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,m=h&&"documentMode"in document?document.documentMode:null;h&&"InputEvent"in window&&!m&&new window.InputEvent("input");const N=h&&/Version\/[\d.]+.*Safari/.test(navigator.userAgent),$=h&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,T=h&&/^(?=.*Chrome).*/i.test(navigator.userAgent),E=h&&/AppleWebKit\/[\d.]+/.test(navigator.userAgent)&&!T;function S(e,t){const n=function(e,t){const n=e.match(t.openTagsRegExp);if(null==n)return null;for(const o of n){const n=o.replace(/^\s/,""),r=t.fullMatchRegExpByTag[n];if(null==r)continue;const i=e.match(r),s=t.transformersByTag[n];if(null!=i&&null!=s){if(!1!==s.intraword)return i;const{index:t=0}=i,n=e[t-1],o=e[t+i[0].length];if((!n||l.test(n))&&(!o||l.test(o)))return i}}return null}(e.getTextContent(),t);if(!n)return null;const o=n.index||0;return{endIndex:o+n[0].length,match:n,startIndex:o,transformer:t.transformersByTag[n[1]]}}function C(t){return e.$isTextNode(t)&&!t.hasFormat("code")}function L(e,t,n){let o=S(e,t),r=function(e,t){const n=e;let o,r,i,s;for(const e of t){if(!e.replace||!e.importRegExp)continue;const t=n.getTextContent().match(e.importRegExp);if(!t)continue;const c=t.index||0,l=e.getEndIndex?e.getEndIndex(n,t):c+t[0].length;!1!==l&&(void 0===o||void 0===r||c<o&&l>r)&&(o=c,r=l,i=e,s=t)}return void 0===o||void 0===r||void 0===i||void 0===s?null:{endIndex:r,match:s,startIndex:o,transformer:i}}(e,n);if(o&&r&&(o.startIndex<=r.startIndex&&o.endIndex>=r.endIndex?r=null:o=null),o){const r=function(e,t,n,o,r){const i=e.getTextContent();let s,c,l;if(r[0]===i?s=e:0===t?[s,c]=e.splitText(n):[l,s,c]=e.splitText(t,n),s.setTextContent(r[2]),o)for(const e of o.format)s.hasFormat(e)||s.toggleFormat(e);return{nodeAfter:c,nodeBefore:l,transformedNode:s}}(e,o.startIndex,o.endIndex,o.transformer,o.match);return C(r.nodeAfter)&&L(r.nodeAfter,t,n),C(r.nodeBefore)&&L(r.nodeBefore,t,n),void(C(r.transformedNode)&&L(r.transformedNode,t,n))}if(r){const o=function(e,t,n,o,r){let i,s,c;return 0===t?[i,s]=e.splitText(n):[c,i,s]=e.splitText(t,n),o.replace?{nodeAfter:s,nodeBefore:c,transformedNode:o.replace(i,r)||void 0}:null}(e,r.startIndex,r.endIndex,r.transformer,r.match);if(!o)return;return C(o.nodeAfter)&&L(o.nodeAfter,t,n),C(o.nodeBefore)&&L(o.nodeBefore,t,n),void(C(o.transformedNode)&&L(o.transformedNode,t,n))}}function R(t,n=!1){const o=c(t),r=function(e){const t={},n={},o=[],r="(?<![\\\\])";for(const r of e){const{tag:e}=r;t[e]=r;const i=e.replace(/(\*|\^|\+)/g,"\\$1");o.push(i),n[e]=N||$||E?new RegExp(`(${i})(?![${i}\\s])(.*?[^${i}\\s])${i}(?!${i})`):new RegExp(`(?<![\\\\${i}])(${i})((\\\\${i})?.*?[^${i}\\s](\\\\${i})?)((?<!\\\\)|(?<=\\\\\\\\))(${i})(?![\\\\${i}])`)}return{fullMatchRegExpByTag:n,openTagsRegExp:new RegExp((N||$||E?"":`${r}`)+"("+o.join("|")+")","g"),transformersByTag:t}}(o.textFormat);return(t,i)=>{const s=t.split("\n"),c=s.length,l=i||e.$getRoot();l.clear();for(let e=0;e<c;e++){const t=s[e],[n,i]=I(s,e,o.multilineElement,l);n?e=i:v(t,l,o.element,r,o.textMatch)}const a=l.getChildren();for(const e of a)!n&&f(e)&&l.getChildrenSize()>1&&e.remove();null!==e.$getSelection()&&l.selectStart()}}function I(e,t,n,o){for(const r of n){const{handleImportAfterStartMatch:n,regExpEnd:i,regExpStart:s,replace:c}=r,l=e[t].match(s);if(!l)continue;if(n){const i=n({lines:e,rootNode:o,startLineIndex:t,startMatch:l,transformer:r});if(null===i)continue;if(i)return i}const a="object"==typeof i&&"regExp"in i?i.regExp:i,f=i&&"object"==typeof i&&"optional"in i?i.optional:!i;let d=t;const u=e.length;for(;d<u;){const n=a?e[d].match(a):null;if(!n&&(!f||f&&d<u-1)){d++;continue}if(n&&t===d&&n.index===l.index){d++;continue}const r=[];if(n&&t===d)r.push(e[t].slice(l[0].length,-n[0].length));else for(let o=t;o<=d;o++)if(o===t){const t=e[o].slice(l[0].length);r.push(t)}else if(o===d&&n){const t=e[o].slice(0,-n[0].length);r.push(t)}else r.push(e[o]);if(!1!==c(o,null,l,n,r,!0))return[!0,d];break}}return[!1,t]}function v(r,i,s,c,l){const a=e.$createTextNode(r),f=e.$createParagraphNode();f.append(a),i.append(f);for(const{regExp:e,replace:t}of s){const n=r.match(e);if(n&&(a.setTextContent(r.slice(n[0].length)),!1!==t(f,[a],n,!0)))break}if(L(a,c,l),f.isAttached()&&r.length>0){const r=f.getPreviousSibling();if(e.$isParagraphNode(r)||n.$isQuoteNode(r)||t.$isListNode(r)){let n=r;if(t.$isListNode(r)){const e=r.getLastDescendant();n=null==e?null:o.$findMatchingParent(e,t.$isListItemNode)}null!=n&&n.getTextContentSize()>0&&(n.splice(n.getChildrenSize(),0,[e.$createLineBreakNode(),...f.getChildren()]),f.remove())}}}function y(e,...t){const n=new URL("https://lexical.dev/docs/error"),o=new URLSearchParams;o.append("code",e);for(const e of t)o.append("v",e);throw n.search=o.toString(),Error(`Minified Lexical error #${e}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}function w(e,t,n){const o=n.length;for(let r=t;r>=o;r--){const t=r-o;if(b(e,t,n,0,o)&&" "!==e[t+o])return t}return-1}function b(e,t,n,o,r){for(let i=0;i<r;i++)if(e[t+i]!==n[o+i])return!1;return!0}const _=/^(\s*)(\d{1,})\.\s/,A=/^(\s*)[-*+]\s/,M=/^(\s*)(?:-\s)?\s?(\[(\s|x)?\])\s/i,F=/^(#{1,6})\s/,k=/^>\s/,O=/^[ \t]*```(\w+)?/,B=/[ \t]*```$/,D=/^[ \t]*```[^`]+(?:(?:`{1,2}|`{4,})[^`]+)*```(?:[^`]|$)/,P=/^(?:\|)(.+)(?:\|)\s?$/,U=/^(\| ?:?-*:? ?)+\|\s?$/,H=e=>(t,n,o)=>{const r=e(o);r.append(...n),t.replace(r),r.select(0,0)};const j=e=>(n,o,r)=>{const i=n.getPreviousSibling(),s=n.getNextSibling(),c=t.$createListItemNode("check"===e?"x"===r[3]:void 0);if(t.$isListNode(s)&&s.getListType()===e){const e=s.getFirstChild();null!==e?e.insertBefore(c):s.append(c),n.remove()}else if(t.$isListNode(i)&&i.getListType()===e)i.append(c),n.remove();else{const o=t.$createListNode(e,"number"===e?Number(r[2]):void 0);o.append(c),n.replace(o)}c.append(...o),c.select(0,0);const l=function(e){const t=e.match(/\t/g),n=e.match(/ /g);let o=0;return t&&(o+=t.length),n&&(o+=Math.floor(n.length/4)),o}(r[1]);l&&c.setIndent(l)},q=(e,n,o)=>{const r=[],i=e.getChildren();let s=0;for(const c of i)if(t.$isListItemNode(c)){if(1===c.getChildrenSize()){const e=c.getFirstChild();if(t.$isListNode(e)){r.push(q(e,n,o+1));continue}}const i=" ".repeat(4*o),l=e.getListType(),a="number"===l?`${e.getStart()+s}. `:"check"===l?`- [${c.getChecked()?"x":" "}] `:"- ";r.push(i+a+n(c)),s++}return r.join("\n")},z={dependencies:[n.HeadingNode],export:(e,t)=>{if(!n.$isHeadingNode(e))return null;const o=Number(e.getTag().slice(1));return"#".repeat(o)+" "+t(e)},regExp:F,replace:H((e=>{const t="h"+e[1].length;return n.$createHeadingNode(t)})),type:"element"},Q={dependencies:[n.QuoteNode],export:(e,t)=>{if(!n.$isQuoteNode(e))return null;const o=t(e).split("\n"),r=[];for(const e of o)r.push("> "+e);return r.join("\n")},regExp:k,replace:(t,o,r,i)=>{if(i){const r=t.getPreviousSibling();if(n.$isQuoteNode(r))return r.splice(r.getChildrenSize(),0,[e.$createLineBreakNode(),...o]),r.select(0,0),void t.remove()}const s=n.$createQuoteNode();s.append(...o),t.replace(s),s.select(0,0)},type:"element"},W={dependencies:[r.CodeNode],export:e=>{if(!r.$isCodeNode(e))return null;const t=e.getTextContent();return"```"+(e.getLanguage()||"")+(t?"\n"+t:"")+"\n```"},regExpEnd:{optional:!0,regExp:B},regExpStart:O,replace:(t,n,o,i,s,c)=>{let l,a;if(!n&&s){if(1===s.length)i?(l=r.$createCodeNode(),a=o[1]+s[0]):(l=r.$createCodeNode(o[1]),a=s[0].startsWith(" ")?s[0].slice(1):s[0]);else{if(l=r.$createCodeNode(o[1]),0===s[0].trim().length)for(;s.length>0&&!s[0].length;)s.shift();else s[0]=s[0].startsWith(" ")?s[0].slice(1):s[0];for(;s.length>0&&!s[s.length-1].length;)s.pop();a=s.join("\n")}const n=e.$createTextNode(a);l.append(n),t.append(l)}else n&&H((e=>r.$createCodeNode(e?e[1]:void 0)))(t,n,o,c)},type:"multiline-element"},G={dependencies:[t.ListNode,t.ListItemNode],export:(e,n)=>t.$isListNode(e)?q(e,n,0):null,regExp:A,replace:j("bullet"),type:"element"},K={dependencies:[t.ListNode,t.ListItemNode],export:(e,n)=>t.$isListNode(e)?q(e,n,0):null,regExp:M,replace:j("check"),type:"element"},X={dependencies:[t.ListNode,t.ListItemNode],export:(e,n)=>t.$isListNode(e)?q(e,n,0):null,regExp:_,replace:j("number"),type:"element"},V={format:["code"],tag:"`",type:"text-format"},J={format:["highlight"],tag:"==",type:"text-format"},Y={format:["bold","italic"],tag:"***",type:"text-format"},Z={format:["bold","italic"],intraword:!1,tag:"___",type:"text-format"},ee={format:["bold"],tag:"**",type:"text-format"},te={format:["bold"],intraword:!1,tag:"__",type:"text-format"},ne={format:["strikethrough"],tag:"~~",type:"text-format"},oe={format:["italic"],tag:"*",type:"text-format"},re={format:["italic"],intraword:!1,tag:"_",type:"text-format"},ie={dependencies:[i.LinkNode],export:(e,t,n)=>{if(!i.$isLinkNode(e))return null;const o=e.getTitle(),r=t(e);return o?`[${r}](${e.getURL()} "${o}")`:`[${r}](${e.getURL()})`},importRegExp:/(?:\[([^[]+)\])(?:\((?:([^()\s]+)(?:\s"((?:[^"]*\\")*[^"]*)"\s*)?)\))/,regExp:/(?:\[([^[]+)\])(?:\((?:([^()\s]+)(?:\s"((?:[^"]*\\")*[^"]*)"\s*)?)\))$/,replace:(t,n)=>{const[,o,r,s]=n,c=i.$createLinkNode(r,{title:s}),l=e.$createTextNode(o);return l.setFormat(t.getFormat()),c.append(l),t.replace(c),l},trigger:")",type:"text-match"};const se=[z,Q,G,X],ce=[W],le=[V,Y,Z,ee,te,J,oe,re,ne],ae=[ie],fe=[...se,...ce,...le,...ae];exports.$convertFromMarkdownString=function(e,t=fe,n,o=!1,r=!1){const i=o?e:function(e,t=!1){const n=e.split("\n");let o=!1;const r=[];for(let e=0;e<n.length;e++){const i=n[e],s=r[r.length-1];D.test(i)?r.push(i):O.test(i)||B.test(i)?(o=!o,r.push(i)):o||""===i||""===s||!s||F.test(s)||F.test(i)||k.test(i)||_.test(i)||A.test(i)||M.test(i)||P.test(i)||U.test(i)||!t?r.push(i):r[r.length-1]=s+i}return r.join("\n")}(e,r);return R(t,o)(i,n)},exports.$convertToMarkdownString=function(t=fe,n,o=!1){return function(t,n=!1){const o=c(t),r=[...o.multilineElement,...o.element],i=!n,s=o.textFormat.filter((e=>1===e.format.length)).sort(((e,t)=>Number(e.format.includes("code"))-Number(t.format.includes("code"))));return t=>{const n=[],c=(t||e.$getRoot()).getChildren();for(let e=0;e<c.length;e++){const t=c[e],l=d(t,r,s,o.textMatch);null!=l&&n.push(i&&e>0&&!f(t)&&!f(c[e-1])?"\n".concat(l):l)}return n.join("\n")}}(t,o)(n)},exports.BOLD_ITALIC_STAR=Y,exports.BOLD_ITALIC_UNDERSCORE=Z,exports.BOLD_STAR=ee,exports.BOLD_UNDERSCORE=te,exports.CHECK_LIST=K,exports.CODE=W,exports.ELEMENT_TRANSFORMERS=se,exports.HEADING=z,exports.HIGHLIGHT=J,exports.INLINE_CODE=V,exports.ITALIC_STAR=oe,exports.ITALIC_UNDERSCORE=re,exports.LINK=ie,exports.MULTILINE_ELEMENT_TRANSFORMERS=ce,exports.ORDERED_LIST=X,exports.QUOTE=Q,exports.STRIKETHROUGH=ne,exports.TEXT_FORMAT_TRANSFORMERS=le,exports.TEXT_MATCH_TRANSFORMERS=ae,exports.TRANSFORMERS=fe,exports.UNORDERED_LIST=G,exports.registerMarkdownShortcuts=function(t,n=fe){const o=c(n),i=s(o.textFormat,(({tag:e})=>e[e.length-1])),a=s(o.textMatch,(({trigger:e})=>e));for(const e of n){const n=e.type;if("element"===n||"text-match"===n||"multiline-element"===n){const n=e.dependencies;for(const e of n)t.hasNode(e)||y(173,e.getType())}}const f=(t,n,r)=>{(function(t,n,o,r){const i=t.getParent();if(!e.$isRootOrShadowRoot(i)||t.getFirstChild()!==n)return!1;const s=n.getTextContent();if(" "!==s[o-1])return!1;for(const{regExp:e,replace:i}of r){const r=s.match(e);if(r&&r[0].length===(r[0].endsWith(" ")?o:o-1)){const e=n.getNextSiblings(),[s,c]=n.splitText(o);if(s.remove(),!1!==i(t,c?[c,...e]:e,r,!1))return!0}}return!1})(t,n,r,o.element)||function(t,n,o,r){const i=t.getParent();if(!e.$isRootOrShadowRoot(i)||t.getFirstChild()!==n)return!1;const s=n.getTextContent();if(" "!==s[o-1])return!1;for(const{regExpStart:e,replace:i,regExpEnd:c}of r){if(c&&!("optional"in c)||c&&"optional"in c&&!c.optional)continue;const r=s.match(e);if(r&&r[0].length===(r[0].endsWith(" ")?o:o-1)){const e=n.getNextSiblings(),[s,c]=n.splitText(o);if(s.remove(),!1!==i(t,c?[c,...e]:e,r,null,null,!1))return!0}}return!1}(t,n,r,o.multilineElement)||function(e,t,n){let o=e.getTextContent();const r=n[o[t-1]];if(null==r)return!1;t<o.length&&(o=o.slice(0,t));for(const t of r){if(!t.replace||!t.regExp)continue;const n=o.match(t.regExp);if(null===n)continue;const r=n.index||0,i=r+n[0].length;let s;return 0===r?[s]=e.splitText(i):[,s]=e.splitText(r,i),s.selectNext(0,0),t.replace(s,n),!0}return!1}(n,r,a)||function(t,n,o){const r=t.getTextContent(),i=n-1,s=r[i],c=o[s];if(!c)return!1;for(const n of c){const{tag:o}=n,c=o.length,a=i-c+1;if(c>1&&!b(r,a,o,0,c))continue;if(" "===r[a-1])continue;const f=r[i+1];if(!1===n.intraword&&f&&!l.test(f))continue;const d=t;let u=d,g=w(r,a,o),p=u;for(;g<0&&(p=p.getPreviousSibling())&&!e.$isLineBreakNode(p);)if(e.$isTextNode(p)){const e=p.getTextContent();u=p,g=w(e,e.length,o)}if(g<0)continue;if(u===d&&g+c===a)continue;const x=u.getTextContent();if(g>0&&x[g-1]===s)continue;const h=x[g-1];if(!1===n.intraword&&h&&!l.test(h))continue;const m=d.getTextContent(),N=m.slice(0,a)+m.slice(i+1);d.setTextContent(N);const $=u===d?N:x;u.setTextContent($.slice(0,g)+$.slice(g+c));const T=e.$getSelection(),E=e.$createRangeSelection();e.$setSelection(E);const S=i-c*(u===d?2:1)+1;E.anchor.set(u.__key,g,"text"),E.focus.set(d.__key,S,"text");for(const e of n.format)E.hasFormat(e)||E.formatText(e);E.anchor.set(E.focus.key,E.focus.offset,E.focus.type);for(const e of n.format)E.hasFormat(e)&&E.toggleFormat(e);return e.$isRangeSelection(T)&&(E.format=T.format),!0}}(n,r,i)};return t.registerUpdateListener((({tags:n,dirtyLeaves:o,editorState:i,prevEditorState:s})=>{if(n.has("collaboration")||n.has("historic"))return;if(t.isComposing())return;const c=i.read(e.$getSelection),l=s.read(e.$getSelection);if(!e.$isRangeSelection(l)||!e.$isRangeSelection(c)||!c.isCollapsed()||c.is(l))return;const a=c.anchor.key,d=c.anchor.offset,u=i._nodeMap.get(a);!e.$isTextNode(u)||!o.has(a)||1!==d&&d>l.anchor.offset+1||t.update((()=>{if(!C(u))return;const e=u.getParent();null===e||r.$isCodeNode(e)||f(e,u,c.anchor.offset)}))}))};
