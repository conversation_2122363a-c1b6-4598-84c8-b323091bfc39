/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{$isParagraphNode as t,$isTextNode as e,$getRoot as n,$isElementNode as o,$isDecoratorNode as r,$isLineBreakNode as i,$getSelection as s,$createTextNode as c,$createParagraphNode as l,$createLineBreakNode as f,$isRangeSelection as a,$isRootOrShadowRoot as u,$createRangeSelection as g,$setSelection as p}from"lexical";import{$isListNode as d,$isListItemNode as m,ListNode as h,ListItemNode as x,$createListItemNode as E,$createListNode as T}from"@lexical/list";import{$isQuoteNode as v,HeadingNode as C,$isHeadingNode as y,QuoteNode as S,$createQuoteNode as b,$createHeadingNode as w}from"@lexical/rich-text";import{$findMatchingParent as $}from"@lexical/utils";import{$isCodeNode as I,CodeNode as F,$createCodeNode as N}from"@lexical/code";import{LinkNode as P,$isLinkNode as k,$createLinkNode as L}from"@lexical/link";function M(t,e){const n={};for(const o of t){const t=e(o);t&&(n[t]?n[t].push(o):n[t]=[o])}return n}function R(t){const e=M(t,(t=>t.type));return{element:e.element||[],multilineElement:e["multiline-element"]||[],textFormat:e["text-format"]||[],textMatch:e["text-match"]||[]}}const A=/[!-/:-@[-`{-~\s]/,B=/^\s{0,3}$/;function _(n){if(!t(n))return!1;const o=n.getFirstChild();return null==o||1===n.getChildrenSize()&&e(o)&&B.test(o.getTextContent())}function j(t,e,n,i){for(const o of e){if(!o.export)continue;const e=o.export(t,(t=>z(t,n,i)));if(null!=e)return e}return o(t)?z(t,n,i):r(t)?t.getTextContent():null}function z(t,n,s,c,l){const f=[],a=t.getChildren();c||(c=[]),l||(l=[]);t:for(const t of a){for(const e of s){if(!e.export)continue;const o=e.export(t,(t=>z(t,n,s,c,[...l,...c])),((t,e)=>U(t,e,n,c,l)));if(null!=o){f.push(o);continue t}}i(t)?f.push("\n"):e(t)?f.push(U(t,t.getTextContent(),n,c,l)):o(t)?f.push(z(t,n,s,c,l)):r(t)&&f.push(t.getTextContent())}return f.join("")}function U(t,e,n,o,r){const i=e.trim();let s=i,c="",l="",f="";const a=W(t,!0),u=W(t,!1),g=new Set;for(const e of n){const n=e.format[0],r=e.tag;D(t,n)&&!g.has(n)&&(g.add(n),D(a,n)&&o.find((t=>t.tag===r))||(o.push({format:n,tag:r}),c+=r))}for(let e=0;e<o.length;e++){const n=D(t,o[e].format),i=D(u,o[e].format);if(n&&i)continue;const s=[...o];for(;s.length>e;){const t=s.pop();r&&t&&r.find((e=>e.tag===t.tag))||(t&&"string"==typeof t.tag&&(n?i||(f+=t.tag):l+=t.tag),o.pop())}break}return s=c+s+f,l+e.replace(i,(()=>s))}function W(t,n){let r=n?t.getPreviousSibling():t.getNextSibling();if(!r){const e=t.getParentOrThrow();e.isInline()&&(r=n?e.getPreviousSibling():e.getNextSibling())}for(;r;){if(o(r)){if(!r.isInline())break;const t=n?r.getLastDescendant():r.getFirstDescendant();if(e(t))return t;r=n?r.getPreviousSibling():r.getNextSibling()}if(e(r))return r;if(!o(r))return null}return null}function D(t,n){return e(t)&&t.hasFormat(n)}const K="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,O=K&&"documentMode"in document?document.documentMode:null;K&&"InputEvent"in window&&!O&&new window.InputEvent("input");const V=K&&/Version\/[\d.]+.*Safari/.test(navigator.userAgent),q=K&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,G=K&&/^(?=.*Chrome).*/i.test(navigator.userAgent),H=K&&/AppleWebKit\/[\d.]+/.test(navigator.userAgent)&&!G;function J(t,e){const n=function(t,e){const n=t.match(e.openTagsRegExp);if(null==n)return null;for(const o of n){const n=o.replace(/^\s/,""),r=e.fullMatchRegExpByTag[n];if(null==r)continue;const i=t.match(r),s=e.transformersByTag[n];if(null!=i&&null!=s){if(!1!==s.intraword)return i;const{index:e=0}=i,n=t[e-1],o=t[e+i[0].length];if((!n||A.test(n))&&(!o||A.test(o)))return i}}return null}(t.getTextContent(),e);if(!n)return null;const o=n.index||0;return{endIndex:o+n[0].length,match:n,startIndex:o,transformer:e.transformersByTag[n[1]]}}function Q(t){return e(t)&&!t.hasFormat("code")}function X(t,e,n){let o=J(t,e),r=function(t,e){const n=t;let o,r,i,s;for(const t of e){if(!t.replace||!t.importRegExp)continue;const e=n.getTextContent().match(t.importRegExp);if(!e)continue;const c=e.index||0,l=t.getEndIndex?t.getEndIndex(n,e):c+e[0].length;!1!==l&&(void 0===o||void 0===r||c<o&&l>r)&&(o=c,r=l,i=t,s=e)}return void 0===o||void 0===r||void 0===i||void 0===s?null:{endIndex:r,match:s,startIndex:o,transformer:i}}(t,n);if(o&&r&&(o.startIndex<=r.startIndex&&o.endIndex>=r.endIndex?r=null:o=null),o){const r=function(t,e,n,o,r){const i=t.getTextContent();let s,c,l;if(r[0]===i?s=t:0===e?[s,c]=t.splitText(n):[l,s,c]=t.splitText(e,n),s.setTextContent(r[2]),o)for(const t of o.format)s.hasFormat(t)||s.toggleFormat(t);return{nodeAfter:c,nodeBefore:l,transformedNode:s}}(t,o.startIndex,o.endIndex,o.transformer,o.match);return Q(r.nodeAfter)&&X(r.nodeAfter,e,n),Q(r.nodeBefore)&&X(r.nodeBefore,e,n),void(Q(r.transformedNode)&&X(r.transformedNode,e,n))}if(r){const o=function(t,e,n,o,r){let i,s,c;return 0===e?[i,s]=t.splitText(n):[c,i,s]=t.splitText(e,n),o.replace?{nodeAfter:s,nodeBefore:c,transformedNode:o.replace(i,r)||void 0}:null}(t,r.startIndex,r.endIndex,r.transformer,r.match);if(!o)return;return Q(o.nodeAfter)&&X(o.nodeAfter,e,n),Q(o.nodeBefore)&&X(o.nodeBefore,e,n),void(Q(o.transformedNode)&&X(o.transformedNode,e,n))}}function Y(t,e=!1){const o=R(t),r=function(t){const e={},n={},o=[],r="(?<![\\\\])";for(const r of t){const{tag:t}=r;e[t]=r;const i=t.replace(/(\*|\^|\+)/g,"\\$1");o.push(i),n[t]=V||q||H?new RegExp(`(${i})(?![${i}\\s])(.*?[^${i}\\s])${i}(?!${i})`):new RegExp(`(?<![\\\\${i}])(${i})((\\\\${i})?.*?[^${i}\\s](\\\\${i})?)((?<!\\\\)|(?<=\\\\\\\\))(${i})(?![\\\\${i}])`)}return{fullMatchRegExpByTag:n,openTagsRegExp:new RegExp((V||q||H?"":`${r}`)+"("+o.join("|")+")","g"),transformersByTag:e}}(o.textFormat);return(t,i)=>{const c=t.split("\n"),l=c.length,f=i||n();f.clear();for(let t=0;t<l;t++){const e=c[t],[n,i]=Z(c,t,o.multilineElement,f);n?t=i:tt(e,f,o.element,r,o.textMatch)}const a=f.getChildren();for(const t of a)!e&&_(t)&&f.getChildrenSize()>1&&t.remove();null!==s()&&f.selectStart()}}function Z(t,e,n,o){for(const r of n){const{handleImportAfterStartMatch:n,regExpEnd:i,regExpStart:s,replace:c}=r,l=t[e].match(s);if(!l)continue;if(n){const i=n({lines:t,rootNode:o,startLineIndex:e,startMatch:l,transformer:r});if(null===i)continue;if(i)return i}const f="object"==typeof i&&"regExp"in i?i.regExp:i,a=i&&"object"==typeof i&&"optional"in i?i.optional:!i;let u=e;const g=t.length;for(;u<g;){const n=f?t[u].match(f):null;if(!n&&(!a||a&&u<g-1)){u++;continue}if(n&&e===u&&n.index===l.index){u++;continue}const r=[];if(n&&e===u)r.push(t[e].slice(l[0].length,-n[0].length));else for(let o=e;o<=u;o++)if(o===e){const e=t[o].slice(l[0].length);r.push(e)}else if(o===u&&n){const e=t[o].slice(0,-n[0].length);r.push(e)}else r.push(t[o]);if(!1!==c(o,null,l,n,r,!0))return[!0,u];break}}return[!1,e]}function tt(e,n,o,r,i){const s=c(e),a=l();a.append(s),n.append(a);for(const{regExp:t,replace:n}of o){const o=e.match(t);if(o&&(s.setTextContent(e.slice(o[0].length)),!1!==n(a,[s],o,!0)))break}if(X(s,r,i),a.isAttached()&&e.length>0){const e=a.getPreviousSibling();if(t(e)||v(e)||d(e)){let t=e;if(d(e)){const n=e.getLastDescendant();t=null==n?null:$(n,m)}null!=t&&t.getTextContentSize()>0&&(t.splice(t.getChildrenSize(),0,[f(),...a.getChildren()]),a.remove())}}}function et(t,...e){const n=new URL("https://lexical.dev/docs/error"),o=new URLSearchParams;o.append("code",t);for(const t of e)o.append("v",t);throw n.search=o.toString(),Error(`Minified Lexical error #${t}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}function nt(t,e,n){const o=n.length;for(let r=e;r>=o;r--){const e=r-o;if(ot(t,e,n,0,o)&&" "!==t[e+o])return e}return-1}function ot(t,e,n,o,r){for(let i=0;i<r;i++)if(t[e+i]!==n[o+i])return!1;return!0}function rt(t,n=jt){const o=R(n),r=M(o.textFormat,(({tag:t})=>t[t.length-1])),c=M(o.textMatch,(({trigger:t})=>t));for(const e of n){const n=e.type;if("element"===n||"text-match"===n||"multiline-element"===n){const n=e.dependencies;for(const e of n)t.hasNode(e)||et(173,e.getType())}}const l=(t,n,l)=>{(function(t,e,n,o){const r=t.getParent();if(!u(r)||t.getFirstChild()!==e)return!1;const i=e.getTextContent();if(" "!==i[n-1])return!1;for(const{regExp:r,replace:s}of o){const o=i.match(r);if(o&&o[0].length===(o[0].endsWith(" ")?n:n-1)){const r=e.getNextSiblings(),[i,c]=e.splitText(n);if(i.remove(),!1!==s(t,c?[c,...r]:r,o,!1))return!0}}return!1})(t,n,l,o.element)||function(t,e,n,o){const r=t.getParent();if(!u(r)||t.getFirstChild()!==e)return!1;const i=e.getTextContent();if(" "!==i[n-1])return!1;for(const{regExpStart:r,replace:s,regExpEnd:c}of o){if(c&&!("optional"in c)||c&&"optional"in c&&!c.optional)continue;const o=i.match(r);if(o&&o[0].length===(o[0].endsWith(" ")?n:n-1)){const r=e.getNextSiblings(),[i,c]=e.splitText(n);if(i.remove(),!1!==s(t,c?[c,...r]:r,o,null,null,!1))return!0}}return!1}(t,n,l,o.multilineElement)||function(t,e,n){let o=t.getTextContent();const r=n[o[e-1]];if(null==r)return!1;e<o.length&&(o=o.slice(0,e));for(const e of r){if(!e.replace||!e.regExp)continue;const n=o.match(e.regExp);if(null===n)continue;const r=n.index||0,i=r+n[0].length;let s;return 0===r?[s]=t.splitText(i):[,s]=t.splitText(r,i),s.selectNext(0,0),e.replace(s,n),!0}return!1}(n,l,c)||function(t,n,o){const r=t.getTextContent(),c=n-1,l=r[c],f=o[l];if(!f)return!1;for(const n of f){const{tag:o}=n,f=o.length,u=c-f+1;if(f>1&&!ot(r,u,o,0,f))continue;if(" "===r[u-1])continue;const d=r[c+1];if(!1===n.intraword&&d&&!A.test(d))continue;const m=t;let h=m,x=nt(r,u,o),E=h;for(;x<0&&(E=E.getPreviousSibling())&&!i(E);)if(e(E)){const t=E.getTextContent();h=E,x=nt(t,t.length,o)}if(x<0)continue;if(h===m&&x+f===u)continue;const T=h.getTextContent();if(x>0&&T[x-1]===l)continue;const v=T[x-1];if(!1===n.intraword&&v&&!A.test(v))continue;const C=m.getTextContent(),y=C.slice(0,u)+C.slice(c+1);m.setTextContent(y);const S=h===m?y:T;h.setTextContent(S.slice(0,x)+S.slice(x+f));const b=s(),w=g();p(w);const $=c-f*(h===m?2:1)+1;w.anchor.set(h.__key,x,"text"),w.focus.set(m.__key,$,"text");for(const t of n.format)w.hasFormat(t)||w.formatText(t);w.anchor.set(w.focus.key,w.focus.offset,w.focus.type);for(const t of n.format)w.hasFormat(t)&&w.toggleFormat(t);return a(b)&&(w.format=b.format),!0}}(n,l,r)};return t.registerUpdateListener((({tags:n,dirtyLeaves:o,editorState:r,prevEditorState:i})=>{if(n.has("collaboration")||n.has("historic"))return;if(t.isComposing())return;const c=r.read(s),f=i.read(s);if(!a(f)||!a(c)||!c.isCollapsed()||c.is(f))return;const u=c.anchor.key,g=c.anchor.offset,p=r._nodeMap.get(u);!e(p)||!o.has(u)||1!==g&&g>f.anchor.offset+1||t.update((()=>{if(!Q(p))return;const t=p.getParent();null===t||I(t)||l(t,p,c.anchor.offset)}))}))}const it=/^(\s*)(\d{1,})\.\s/,st=/^(\s*)[-*+]\s/,ct=/^(\s*)(?:-\s)?\s?(\[(\s|x)?\])\s/i,lt=/^(#{1,6})\s/,ft=/^>\s/,at=/^[ \t]*```(\w+)?/,ut=/[ \t]*```$/,gt=/^[ \t]*```[^`]+(?:(?:`{1,2}|`{4,})[^`]+)*```(?:[^`]|$)/,pt=/^(?:\|)(.+)(?:\|)\s?$/,dt=/^(\| ?:?-*:? ?)+\|\s?$/,mt=t=>(e,n,o)=>{const r=t(o);r.append(...n),e.replace(r),r.select(0,0)};const ht=t=>(e,n,o)=>{const r=e.getPreviousSibling(),i=e.getNextSibling(),s=E("check"===t?"x"===o[3]:void 0);if(d(i)&&i.getListType()===t){const t=i.getFirstChild();null!==t?t.insertBefore(s):i.append(s),e.remove()}else if(d(r)&&r.getListType()===t)r.append(s),e.remove();else{const n=T(t,"number"===t?Number(o[2]):void 0);n.append(s),e.replace(n)}s.append(...n),s.select(0,0);const c=function(t){const e=t.match(/\t/g),n=t.match(/ /g);let o=0;return e&&(o+=e.length),n&&(o+=Math.floor(n.length/4)),o}(o[1]);c&&s.setIndent(c)},xt=(t,e,n)=>{const o=[],r=t.getChildren();let i=0;for(const s of r)if(m(s)){if(1===s.getChildrenSize()){const t=s.getFirstChild();if(d(t)){o.push(xt(t,e,n+1));continue}}const r=" ".repeat(4*n),c=t.getListType(),l="number"===c?`${t.getStart()+i}. `:"check"===c?`- [${s.getChecked()?"x":" "}] `:"- ";o.push(r+l+e(s)),i++}return o.join("\n")},Et={dependencies:[C],export:(t,e)=>{if(!y(t))return null;const n=Number(t.getTag().slice(1));return"#".repeat(n)+" "+e(t)},regExp:lt,replace:mt((t=>{const e="h"+t[1].length;return w(e)})),type:"element"},Tt={dependencies:[S],export:(t,e)=>{if(!v(t))return null;const n=e(t).split("\n"),o=[];for(const t of n)o.push("> "+t);return o.join("\n")},regExp:ft,replace:(t,e,n,o)=>{if(o){const n=t.getPreviousSibling();if(v(n))return n.splice(n.getChildrenSize(),0,[f(),...e]),n.select(0,0),void t.remove()}const r=b();r.append(...e),t.replace(r),r.select(0,0)},type:"element"},vt={dependencies:[F],export:t=>{if(!I(t))return null;const e=t.getTextContent();return"```"+(t.getLanguage()||"")+(e?"\n"+e:"")+"\n```"},regExpEnd:{optional:!0,regExp:ut},regExpStart:at,replace:(t,e,n,o,r,i)=>{let s,l;if(!e&&r){if(1===r.length)o?(s=N(),l=n[1]+r[0]):(s=N(n[1]),l=r[0].startsWith(" ")?r[0].slice(1):r[0]);else{if(s=N(n[1]),0===r[0].trim().length)for(;r.length>0&&!r[0].length;)r.shift();else r[0]=r[0].startsWith(" ")?r[0].slice(1):r[0];for(;r.length>0&&!r[r.length-1].length;)r.pop();l=r.join("\n")}const e=c(l);s.append(e),t.append(s)}else e&&mt((t=>N(t?t[1]:void 0)))(t,e,n,i)},type:"multiline-element"},Ct={dependencies:[h,x],export:(t,e)=>d(t)?xt(t,e,0):null,regExp:st,replace:ht("bullet"),type:"element"},yt={dependencies:[h,x],export:(t,e)=>d(t)?xt(t,e,0):null,regExp:ct,replace:ht("check"),type:"element"},St={dependencies:[h,x],export:(t,e)=>d(t)?xt(t,e,0):null,regExp:it,replace:ht("number"),type:"element"},bt={format:["code"],tag:"`",type:"text-format"},wt={format:["highlight"],tag:"==",type:"text-format"},$t={format:["bold","italic"],tag:"***",type:"text-format"},It={format:["bold","italic"],intraword:!1,tag:"___",type:"text-format"},Ft={format:["bold"],tag:"**",type:"text-format"},Nt={format:["bold"],intraword:!1,tag:"__",type:"text-format"},Pt={format:["strikethrough"],tag:"~~",type:"text-format"},kt={format:["italic"],tag:"*",type:"text-format"},Lt={format:["italic"],intraword:!1,tag:"_",type:"text-format"},Mt={dependencies:[P],export:(t,e,n)=>{if(!k(t))return null;const o=t.getTitle(),r=e(t);return o?`[${r}](${t.getURL()} "${o}")`:`[${r}](${t.getURL()})`},importRegExp:/(?:\[([^[]+)\])(?:\((?:([^()\s]+)(?:\s"((?:[^"]*\\")*[^"]*)"\s*)?)\))/,regExp:/(?:\[([^[]+)\])(?:\((?:([^()\s]+)(?:\s"((?:[^"]*\\")*[^"]*)"\s*)?)\))$/,replace:(t,e)=>{const[,n,o,r]=e,i=L(o,{title:r}),s=c(n);return s.setFormat(t.getFormat()),i.append(s),t.replace(i),s},trigger:")",type:"text-match"};const Rt=[Et,Tt,Ct,St],At=[vt],Bt=[bt,$t,It,Ft,Nt,wt,kt,Lt,Pt],_t=[Mt],jt=[...Rt,...At,...Bt,..._t];function zt(t,e=jt,n,o=!1,r=!1){const i=o?t:function(t,e=!1){const n=t.split("\n");let o=!1;const r=[];for(let t=0;t<n.length;t++){const i=n[t],s=r[r.length-1];gt.test(i)?r.push(i):at.test(i)||ut.test(i)?(o=!o,r.push(i)):o||""===i||""===s||!s||lt.test(s)||lt.test(i)||ft.test(i)||it.test(i)||st.test(i)||ct.test(i)||pt.test(i)||dt.test(i)||!e?r.push(i):r[r.length-1]=s+i}return r.join("\n")}(t,r);return Y(e,o)(i,n)}function Ut(t=jt,e,o=!1){const r=function(t,e=!1){const o=R(t),r=[...o.multilineElement,...o.element],i=!e,s=o.textFormat.filter((t=>1===t.format.length)).sort(((t,e)=>Number(t.format.includes("code"))-Number(e.format.includes("code"))));return t=>{const e=[],c=(t||n()).getChildren();for(let t=0;t<c.length;t++){const n=c[t],l=j(n,r,s,o.textMatch);null!=l&&e.push(i&&t>0&&!_(n)&&!_(c[t-1])?"\n".concat(l):l)}return e.join("\n")}}(t,o);return r(e)}export{zt as $convertFromMarkdownString,Ut as $convertToMarkdownString,$t as BOLD_ITALIC_STAR,It as BOLD_ITALIC_UNDERSCORE,Ft as BOLD_STAR,Nt as BOLD_UNDERSCORE,yt as CHECK_LIST,vt as CODE,Rt as ELEMENT_TRANSFORMERS,Et as HEADING,wt as HIGHLIGHT,bt as INLINE_CODE,kt as ITALIC_STAR,Lt as ITALIC_UNDERSCORE,Mt as LINK,At as MULTILINE_ELEMENT_TRANSFORMERS,St as ORDERED_LIST,Tt as QUOTE,Pt as STRIKETHROUGH,Bt as TEXT_FORMAT_TRANSFORMERS,_t as TEXT_MATCH_TRANSFORMERS,jt as TRANSFORMERS,Ct as UNORDERED_LIST,rt as registerMarkdownShortcuts};
