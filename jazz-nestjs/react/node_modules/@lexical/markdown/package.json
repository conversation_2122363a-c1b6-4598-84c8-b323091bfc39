{"name": "@lexical/markdown", "description": "This package contains Markdown helpers and functionality for Lexical.", "keywords": ["lexical", "editor", "rich-text", "markdown"], "license": "MIT", "version": "0.27.2", "main": "LexicalMarkdown.js", "types": "index.d.ts", "dependencies": {"@lexical/code": "0.27.2", "@lexical/link": "0.27.2", "@lexical/list": "0.27.2", "@lexical/rich-text": "0.27.2", "@lexical/text": "0.27.2", "@lexical/utils": "0.27.2", "lexical": "0.27.2"}, "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-markdown"}, "module": "LexicalMarkdown.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalMarkdown.dev.mjs", "production": "./LexicalMarkdown.prod.mjs", "node": "./LexicalMarkdown.node.mjs", "default": "./LexicalMarkdown.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalMarkdown.dev.js", "production": "./LexicalMarkdown.prod.js", "default": "./LexicalMarkdown.js"}}}}