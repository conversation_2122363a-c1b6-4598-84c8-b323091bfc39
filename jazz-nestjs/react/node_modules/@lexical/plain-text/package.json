{"name": "@lexical/plain-text", "description": "This package contains plain text helpers for Lexical.", "keywords": ["lexical", "editor", "plain-text"], "license": "MIT", "version": "0.27.2", "main": "LexicalPlainText.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-plain-text"}, "module": "LexicalPlainText.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalPlainText.dev.mjs", "production": "./LexicalPlainText.prod.mjs", "node": "./LexicalPlainText.node.mjs", "default": "./LexicalPlainText.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalPlainText.dev.js", "production": "./LexicalPlainText.prod.js", "default": "./LexicalPlainText.js"}}}, "dependencies": {"@lexical/clipboard": "0.27.2", "@lexical/selection": "0.27.2", "@lexical/utils": "0.27.2", "lexical": "0.27.2"}}