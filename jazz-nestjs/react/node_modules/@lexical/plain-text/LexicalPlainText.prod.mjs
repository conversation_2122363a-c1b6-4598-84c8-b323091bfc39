/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{$insertDataTransferForPlainText as e,$getHtmlContent as t}from"@lexical/clipboard";import{$shouldOverrideDefaultCharacterSelection as n,$move<PERSON>haracter as r}from"@lexical/selection";import{mergeRegister as o,objectKlassEquals as i}from"@lexical/utils";import{DELETE_CHARACTER_COMMAND as a,$getSelection as s,$isRangeSelection as u,COMMAND_PRIORITY_EDITOR as m,DELETE_WORD_COMMAND as d,DELETE_LINE_COMMAND as l,CONTROLLED_TEXT_INSERTION_COMMAND as c,REMOVE_TEXT_COMMAND as g,INSERT_LINE_BREAK_COMMAND as f,INSERT_PARAGRAPH_COMMAND as p,KEY_ARROW_LEFT_COMMAND as C,KEY_ARROW_RIGHT_COMMAND as v,KEY_BACKSPACE_COMMAND as w,KEY_DELETE_COMMAND as D,KEY_ENTER_COMMAND as x,SELECT_ALL_COMMAND as h,$selectAll as T,COPY_COMMAND as b,CUT_COMMAND as y,PASTE_COMMAND as A,DROP_COMMAND as K,DRAGSTART_COMMAND as E}from"lexical";const k="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,L=k&&"documentMode"in document?document.documentMode:null,M=!(!k||!("InputEvent"in window)||L)&&"getTargetRanges"in new window.InputEvent("input"),P=k&&/Version\/[\d.]+.*Safari/.test(navigator.userAgent),S=k&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,B=k&&/^(?=.*Chrome).*/i.test(navigator.userAgent),I=k&&/AppleWebKit\/[\d.]+/.test(navigator.userAgent)&&!B;function R(e,n){n.update((()=>{if(null!==e){const r=i(e,KeyboardEvent)?null:e.clipboardData,o=s();if(null!==o&&null!=r){e.preventDefault();const i=t(n);null!==i&&r.setData("text/html",i),r.setData("text/plain",o.getTextContent())}}}))}function W(t){return o(t.registerCommand(a,(e=>{const t=s();return!!u(t)&&(t.deleteCharacter(e),!0)}),m),t.registerCommand(d,(e=>{const t=s();return!!u(t)&&(t.deleteWord(e),!0)}),m),t.registerCommand(l,(e=>{const t=s();return!!u(t)&&(t.deleteLine(e),!0)}),m),t.registerCommand(c,(t=>{const n=s();if(!u(n))return!1;if("string"==typeof t)n.insertText(t);else{const r=t.dataTransfer;if(null!=r)e(r,n);else{const e=t.data;e&&n.insertText(e)}}return!0}),m),t.registerCommand(g,(()=>{const e=s();return!!u(e)&&(e.removeText(),!0)}),m),t.registerCommand(f,(e=>{const t=s();return!!u(t)&&(t.insertLineBreak(e),!0)}),m),t.registerCommand(p,(()=>{const e=s();return!!u(e)&&(e.insertLineBreak(),!0)}),m),t.registerCommand(C,(e=>{const t=s();if(!u(t))return!1;const o=e,i=o.shiftKey;return!!n(t,!0)&&(o.preventDefault(),r(t,i,!0),!0)}),m),t.registerCommand(v,(e=>{const t=s();if(!u(t))return!1;const o=e,i=o.shiftKey;return!!n(t,!1)&&(o.preventDefault(),r(t,i,!1),!0)}),m),t.registerCommand(w,(e=>{const n=s();return!!u(n)&&((!S||"ko-KR"!==navigator.language)&&(e.preventDefault(),t.dispatchCommand(a,!0)))}),m),t.registerCommand(D,(e=>{const n=s();return!!u(n)&&(e.preventDefault(),t.dispatchCommand(a,!1))}),m),t.registerCommand(x,(e=>{const n=s();if(!u(n))return!1;if(null!==e){if((S||P||I)&&M)return!1;e.preventDefault()}return t.dispatchCommand(f,!1)}),m),t.registerCommand(h,(()=>(T(),!0)),m),t.registerCommand(b,(e=>{const n=s();return!!u(n)&&(R(e,t),!0)}),m),t.registerCommand(y,(e=>{const n=s();return!!u(n)&&(function(e,t){R(e,t),t.update((()=>{const e=s();u(e)&&e.removeText()}))}(e,t),!0)}),m),t.registerCommand(A,(n=>{const r=s();return!!u(r)&&(function(t,n){t.preventDefault(),n.update((()=>{const n=s(),{clipboardData:r}=t;null!=r&&u(n)&&e(r,n)}),{tag:"paste"})}(n,t),!0)}),m),t.registerCommand(K,(e=>{const t=s();return!!u(t)&&(e.preventDefault(),!0)}),m),t.registerCommand(E,(e=>{const t=s();return!!u(t)&&(e.preventDefault(),!0)}),m))}export{W as registerPlainText};
