/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import{isHTMLElement as t,$getSelection as e,$isRangeSelection as n,$isElementNode as o,getDOMTextNode as r,$getRoot as i,$getChildCaret as l,$getSiblingCaret as s,$getAdjacentChildCaret as u,$getChildCaretOrSelf as c,makeStepwiseIterator as f,$isChildCaret as a,$cloneWithProperties as d,$setSelection as g,$getPreviousSelection as p,$isRootOrShadowRoot as m,$getChildCaretAtIndex as h,$isTextNode as v,$splitNode as y,$createParagraphNode as w,$isSiblingCaret as x,$rewindSiblingCaret as E,$getState as S,$setState as A}from"lexical";export{$splitNode,isBlockDomNode,isHTMLAnchorElement,isHTMLElement,isInlineDomNode}from"lexical";import{createRectsFromDOMRange as C}from"@lexical/selection";function L(t,...e){const n=new URL("https://lexical.dev/docs/error"),o=new URLSearchParams;o.append("code",t);for(const t of e)o.append("v",t);throw n.search=o.toString(),Error(`Minified Lexical error #${t}; visit ${n.toString()} for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`)}const N="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,P=N&&"documentMode"in document?document.documentMode:null,b=N&&/Mac|iPod|iPhone|iPad/.test(navigator.platform),T=N&&/^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent),M=!(!N||!("InputEvent"in window)||P)&&"getTargetRanges"in new window.InputEvent("input"),R=N&&/Version\/[\d.]+.*Safari/.test(navigator.userAgent),B=N&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,_=N&&/Android/.test(navigator.userAgent),O=N&&/^(?=.*Chrome).*/i.test(navigator.userAgent),k=N&&_&&O,K=N&&/AppleWebKit\/[\d.]+/.test(navigator.userAgent)&&!O;function H(...t){const e=[];for(const n of t)if(n&&"string"==typeof n)for(const[t]of n.matchAll(/\S+/g))e.push(t);return e}function $(...t){return()=>{for(let e=t.length-1;e>=0;e--)t[e]();t.length=0}}function D(t){return`${t}px`}const I={attributes:!0,characterData:!0,childList:!0,subtree:!0};function j(e,n,o){let r=null,i=null,l=null,s=[];const u=document.createElement("div");function c(){null===r&&L(182),null===i&&L(183);const{left:t,top:l}=i.getBoundingClientRect(),c=C(e,n);var f,a;u.isConnected||(a=u,(f=i).insertBefore(a,f.firstChild));let d=!1;for(let e=0;e<c.length;e++){const n=c[e],o=s[e]||document.createElement("div"),r=o.style;"absolute"!==r.position&&(r.position="absolute",d=!0);const i=D(n.left-t);r.left!==i&&(r.left=i,d=!0);const f=D(n.top-l);r.top!==f&&(o.style.top=f,d=!0);const a=D(n.width);r.width!==a&&(o.style.width=a,d=!0);const g=D(n.height);r.height!==g&&(o.style.height=g,d=!0),o.parentNode!==u&&(u.append(o),d=!0),s[e]=o}for(;s.length>c.length;)s.pop();d&&o(s)}function f(){i=null,r=null,null!==l&&l.disconnect(),l=null,u.remove();for(const t of s)t.remove();s=[]}u.style.position="relative";const a=e.registerRootListener((function n(){const o=e.getRootElement();if(null===o)return f();const s=o.parentElement;if(!t(s))return f();f(),r=o,i=s,l=new MutationObserver((t=>{const o=e.getRootElement(),l=o&&o.parentElement;if(o!==r||l!==i)return n();for(const e of t)if(!u.contains(e.target))return c()})),l.observe(s,I),c()}));return()=>{a(),f()}}function U(t,e,n){if("text"!==t.type&&o(e)){const o=e.getDOMSlot(n);return[o.element,o.getFirstChildOffset()+t.offset]}return[r(n)||n,t.offset]}function z(t,o){let r=null,i=null,l=null,s=null,u=null,c=null,f=()=>{};function a(a){a.read((()=>{const a=e();if(!n(a))return r=null,l=null,s=null,c=null,f(),void(f=()=>{});const{anchor:d,focus:g}=a,p=d.getNode(),m=p.getKey(),h=d.offset,v=g.getNode(),y=v.getKey(),w=g.offset,x=t.getElementByKey(m),E=t.getElementByKey(y),S=null===r||x!==i||h!==l||m!==r.getKey(),A=null===s||E!==u||w!==c||y!==s.getKey();if((S||A)&&null!==x&&null!==E){const e=function(t,e,n,o,r,i,l){const s=(t._window?t._window.document:document).createRange();return i.isBefore(n)?(s.setStart(...U(r,i,l)),s.setEnd(...U(e,n,o))):(s.setStart(...U(e,n,o)),s.setEnd(...U(r,i,l))),s}(t,d,p,x,g,v,E);f(),f=j(t,e,(t=>{if(void 0===o)for(const e of t){const t=e.style;"Highlight"!==t.background&&(t.background="Highlight"),"HighlightText"!==t.color&&(t.color="HighlightText"),t.marginTop!==D(-1.5)&&(t.marginTop=D(-1.5)),t.paddingTop!==D(4)&&(t.paddingTop=D(4)),t.paddingBottom!==D(0)&&(t.paddingBottom=D(0))}else o(t)}))}r=p,i=x,l=h,s=v,u=E,c=w}))}return a(t.getEditorState()),$(t.registerUpdateListener((({editorState:t})=>a(t))),(()=>{f()}))}function F(t){let e=null;const n=()=>{const n=getSelection(),o=n&&n.anchorNode,r=t.getRootElement();null!==o&&null!==r&&r.contains(o)?null!==e&&(e(),e=null):null===e&&(e=z(t))};return document.addEventListener("selectionchange",n),()=>{null!==e&&e(),document.removeEventListener("selectionchange",n)}}const W=M,V=N,G=_,q=k,J=b,Q=K,X=O,Y=T,Z=B,tt=R;function et(t,...e){const n=H(...e);n.length>0&&t.classList.add(...n)}function nt(t,...e){const n=H(...e);n.length>0&&t.classList.remove(...n)}function ot(t,e){for(const n of e)if(t.type.startsWith(n))return!0;return!1}function rt(t,e){const n=t[Symbol.iterator]();return new Promise(((t,o)=>{const r=[],i=()=>{const{done:l,value:s}=n.next();if(l)return t(r);const u=new FileReader;u.addEventListener("error",o),u.addEventListener("load",(()=>{const t=u.result;"string"==typeof t&&r.push({file:s,result:t}),i()})),ot(s,e)?u.readAsDataURL(s):i()};i()}))}function it(t,e){return Array.from(ut(t,e))}function lt(t){return t?t.getAdjacentCaret():null}function st(t,e){return Array.from(gt(t,e))}function ut(t,e){return ct("next",t,e)}function ct(t,e,n){const r=i(),d=e||r,g=o(d)?l(d,t):s(d,t),p=at(d),m=n?u(c(s(n,t))):function(t,e){const n=Ot(s(t,e));return n&&n[0]}(d,t);let h=p;return f({hasNext:t=>null!==t,initial:g,map:t=>({depth:h,node:t.origin}),step:t=>{if(t.isSameNodeCaret(m))return null;a(t)&&h++;const e=Ot(t);return!e||e[0].isSameNodeCaret(m)?null:(h+=e[1],e[0])}})}function ft(t){const e=Ot(s(t,"next"));return e&&[e[0].origin,e[1]]}function at(t){let e=-1;for(let n=t;null!==n;n=n.getParent())e++;return e}function dt(t){const e=Ot(c(s(t,"previous")),"root");return e&&e[0].origin}function gt(t,e){return ct("previous",t,e)}function pt(t,e){let n=t;for(;null!=n;){if(n instanceof e)return n;n=n.getParent()}return null}function mt(t){const e=ht(t,(t=>o(t)&&!t.isInline()));return o(e)||L(4,t.__key),e}const ht=(t,e)=>{let n=t;for(;n!==i()&&null!=n;){if(e(n))return n;n=n.getParent()}return null};function vt(t,e,n,o){const r=t=>t instanceof e;return t.registerNodeTransform(e,(t=>{const e=(t=>{const e=t.getChildren();for(let t=0;t<e.length;t++){const n=e[t];if(r(n))return null}let n=t,o=t;for(;null!==n;)if(o=n,n=n.getParent(),r(n))return{child:o,parent:n};return null})(t);if(null!==e){const{child:r,parent:i}=e;if(r.is(t)){o(i,t);const e=r.getNextSiblings(),l=e.length;if(i.insertAfter(r),0!==l){const t=n(i);r.insertAfter(t);for(let n=0;n<l;n++)t.append(e[n])}i.canBeEmpty()||0!==i.getChildrenSize()||i.remove()}}}))}function yt(t,e){const n=new Map,o=t._pendingEditorState;for(const[t,o]of e._nodeMap)n.set(t,d(o));o&&(o._nodeMap=n),t._dirtyType=2;const r=e._selection;g(null===r?null:r.clone())}function wt(t){const o=e()||p();if(n(o)){const{focus:e}=o,n=e.getNode(),r=e.offset;if(m(n))h(n,r,"next").insert(t),t.selectNext();else{let e,o;v(n)?(e=n.getParentOrThrow(),o=n.getIndexWithinParent(),r>0&&(o+=1,n.splitText(r))):(e=n,o=r);const[,i]=y(e,o);i.insertBefore(t),i.selectStart()}}else{if(null!=o){const e=o.getNodes();e[e.length-1].getTopLevelElementOrThrow().insertAfter(t)}else i().append(t);const e=w();t.insertAfter(e),e.select()}return t.getLatest()}function xt(t,e){const n=e();return t.replace(n),n.append(t),n}function Et(t,e){return null!==t&&Object.getPrototypeOf(t).constructor.name===e.name}function St(t,e){const n=[];for(let o=0;o<t.length;o++){const r=e(t[o]);null!==r&&n.push(r)}return n}function At(t,e){l(t,"next").insert(e)}let Ct=!(Y||!V)&&void 0;function Lt(t){let e=1;if(function(){if(void 0===Ct){const t=document.createElement("div");t.style.cssText="position: absolute; opacity: 0; width: 100px; left: -1000px;",document.body.appendChild(t);const e=t.getBoundingClientRect();t.style.setProperty("zoom","2"),Ct=t.getBoundingClientRect().width===e.width,document.body.removeChild(t)}return Ct}())for(;t;)e*=Number(window.getComputedStyle(t).getPropertyValue("zoom")),t=t.parentElement;return e}function Nt(t){return null!==t._parentEditor}function Pt(t,e){return bt(t,e,null)}function bt(t,e,n){let r=!1;for(const i of Rt(t))e(i)?null!==n&&n(i):(r=!0,o(i)&&bt(i,e,n||(t=>i.insertAfter(t))),i.remove());return r}function Tt(t,e){const n=[],r=Array.from(t).reverse();for(let t=r.pop();void 0!==t;t=r.pop())if(e(t))n.push(t);else if(o(t))for(const e of Rt(t))r.push(e);return n}function Mt(t){return Bt(l(t,"next"))}function Rt(t){return Bt(l(t,"previous"))}function Bt(t){return f({hasNext:x,initial:t.getAdjacentCaret(),map:t=>t.origin.getLatest(),step:t=>t.getAdjacentCaret()})}function _t(t){E(s(t,"next")).splice(1,t.getChildren())}function Ot(t,e="root"){let n=0,o=t,r=u(o);for(;null===r;){if(n--,r=o.getParentCaret(e),!r)return null;o=r,r=u(o)}return r&&[r,n]}function kt(t){const e=e=>S(e,t),n=(e,n)=>A(e,t,n);return{$get:e,$set:n,accessors:[e,n],makeGetterMethod:()=>function(){return e(this)},makeSetterMethod:()=>function(t){return n(this,t)},stateConfig:t}}export{Tt as $descendantsMatching,it as $dfs,ut as $dfsIterator,St as $filter,ht as $findMatchingParent,Mt as $firstToLastIterator,lt as $getAdjacentCaret,Ot as $getAdjacentSiblingOrParentSiblingCaret,at as $getDepth,mt as $getNearestBlockElementAncestorOrThrow,pt as $getNearestNodeOfType,dt as $getNextRightPreorderNode,ft as $getNextSiblingOrParentSibling,At as $insertFirst,wt as $insertNodeToNearestRoot,Nt as $isEditorIsNestedEditor,Rt as $lastToFirstIterator,yt as $restoreEditorState,st as $reverseDfs,gt as $reverseDfsIterator,Pt as $unwrapAndFilterDescendants,_t as $unwrapNode,xt as $wrapNodeInElement,W as CAN_USE_BEFORE_INPUT,V as CAN_USE_DOM,G as IS_ANDROID,q as IS_ANDROID_CHROME,J as IS_APPLE,Q as IS_APPLE_WEBKIT,X as IS_CHROME,Y as IS_FIREFOX,Z as IS_IOS,tt as IS_SAFARI,et as addClassNamesToElement,Lt as calculateZoomLevel,ot as isMimeType,kt as makeStateWrapper,z as markSelection,rt as mediaFileReader,$ as mergeRegister,Et as objectKlassEquals,j as positionNodeOnRange,vt as registerNestedElementResolver,nt as removeClassNamesFromElement,F as selectionAlwaysOnDisplay};
