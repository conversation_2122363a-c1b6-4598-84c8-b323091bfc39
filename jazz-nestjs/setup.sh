#!/bin/bash

echo "🎵 Setting up Jazz NestJS Application..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing backend dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install backend dependencies"
    exit 1
fi

echo "📦 Installing frontend dependencies..."
cd react
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install frontend dependencies"
    exit 1
fi

cd ..

echo "🏗️ Building the application..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Failed to build the application"
    exit 1
fi

echo "🏗️ Building the frontend..."
cd react
npm run build
cd ..

if [ $? -ne 0 ]; then
    echo "❌ Failed to build the frontend"
    exit 1
fi

echo "✅ Setup completed successfully!"
echo ""
echo "🚀 To start the application:"
echo "   npm start                 # Start Electron app"
echo "   npm run dev              # Start in development mode"
echo "   npm run start:prod       # Start production server only"
echo ""
echo "📚 Check README.md for more information."
