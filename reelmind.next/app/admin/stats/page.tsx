"use client";

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { AdminLayout } from '../blog/AdminLayout';
import { GrowthStatsCard } from './components/growth-stats-card';
import { GrowthChart } from './components/growth-chart';
import { adminStatsApi } from '@/lib/api/admin-stats';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, CreditCard, TrendingUp } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

export default function AdminGrowthStatsPage() {
    // 获取固定30天的统计数据
    const {
        data: statsData,
        isLoading,
        error,
        refetch
    } = useQuery({
        queryKey: ['admin-growth-stats'],
        queryFn: () => adminStatsApi.getGrowthStats({ days: 30 }),
        staleTime: 5 * 60 * 1000, // 5分钟缓存
    });

    // 计算转化率
    const calculateConversionRate = (newUsers: number, newSubscriptions: number): string => {
        if (newUsers === 0) return '0%';
        const rate = (newSubscriptions / newUsers) * 100;
        return `${rate.toFixed(1)}%`;
    };



    if (isLoading) {
        return (
            <AdminLayout>
                <div className="space-y-8">
                    {/* 标题骨架 */}
                    <div className="space-y-4">
                        <Skeleton className="h-8 w-64" />
                        <Skeleton className="h-4 w-96" />
                    </div>

                    {/* 统计卡片骨架 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {[1, 2].map((i) => (
                            <Skeleton key={i} className="h-32" />
                        ))}
                    </div>

                    {/* 图表骨架 */}
                    <Skeleton className="h-96" />
                </div>
            </AdminLayout>
        );
    }

    if (error) {
        return (
            <AdminLayout>
                <div className="flex items-center justify-center h-96">
                    <Card className="w-full max-w-md">
                        <CardHeader>
                            <CardTitle className="text-red-600">Error Loading Stats</CardTitle>
                            <CardDescription>
                                Failed to load growth statistics. Please try again.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <button
                                onClick={() => refetch()}
                                className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                            >
                                Retry
                            </button>
                        </CardContent>
                    </Card>
                </div>
            </AdminLayout>
        );
    }

    const summary = statsData?.summary;
    const dailyStats = statsData?.stats || [];

    // 按时间降序排列（最新的在前）
    const sortedDailyStats = [...dailyStats].sort((a, b) =>
        new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    return (
        <AdminLayout>
            <div className="space-y-8">
                {/* 页面标题 */}
                <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                        <TrendingUp className="w-8 h-8 text-primary" />
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                            Growth Statistics
                        </h1>
                    </div>
                    <p className="text-lg text-gray-600 dark:text-gray-300">
                        Monitor daily growth across key metrics including users and subscriptions over the last 30 days
                    </p>
                </div>

                {/* 汇总统计卡片 */}
                {summary && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <GrowthStatsCard
                            title="Total Registered Users"
                            value={summary.total_new_users}
                            description="All time"
                            icon={<Users className="w-5 h-5" />}
                        />
                        <GrowthStatsCard
                            title="New Subscriptions"
                            value={summary.total_new_subscriptions}
                            description="All time"
                            icon={<CreditCard className="w-5 h-5" />}
                        />
                    </div>
                )}

                {/* 详细数据表格 */}
                {sortedDailyStats.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Daily Breakdown</CardTitle>
                            <CardDescription>
                                Detailed daily statistics for the last 30 days (most recent first)
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <table className="w-full text-sm">
                                    <thead>
                                        <tr className="border-b border-border">
                                            <th className="text-left py-2 px-4 font-medium">Date</th>
                                            <th className="text-right py-2 px-4 font-medium">New Users</th>
                                            <th className="text-right py-2 px-4 font-medium">Subscriptions</th>
                                            <th className="text-right py-2 px-4 font-medium">Conversion Rate</th>
                                            <th className="text-right py-2 px-4 font-medium">Credits</th>
                                            <th className="text-right py-2 px-4 font-medium">Video Tasks</th>
                                            <th className="text-right py-2 px-4 font-medium">Image Tasks</th>
                                            <th className="text-right py-2 px-4 font-medium">Sound Tasks</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {sortedDailyStats.map((day, index) => (
                                            <tr key={index} className="border-b border-border/50 hover:bg-muted/50">
                                                <td className="py-2 px-4">
                                                    {new Date(day.date).toLocaleDateString('en-US', {
                                                        month: 'short',
                                                        day: 'numeric'
                                                    })}
                                                </td>
                                                <td className="text-right py-2 px-4">{day.new_users}</td>
                                                <td className="text-right py-2 px-4">{day.new_subscriptions}</td>
                                                <td className="text-right py-2 px-4 font-medium text-blue-600">
                                                    {calculateConversionRate(day.new_users, day.new_subscriptions)}
                                                </td>
                                                <td className="text-right py-2 px-4 font-medium text-green-600">
                                                    ${Math.floor(day.credits_purchased / 100)}
                                                </td>
                                                <td className="text-right py-2 px-4">{day.video_tasks_submitted}</td>
                                                <td className="text-right py-2 px-4">{day.image_tasks_submitted}</td>
                                                <td className="text-right py-2 px-4">{day.sound_tasks_submitted}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* 增长趋势图表 */}
                {dailyStats.length > 0 && (
                    <GrowthChart
                        data={dailyStats}
                        title="Daily Growth Trends"
                        description="Daily breakdown of growth metrics over the last 30 days"
                    />
                )}
            </div>
        </AdminLayout>
    );
} 