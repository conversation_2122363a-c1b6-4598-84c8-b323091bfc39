"use client";

import { Blocks } from "lucide-react";
import { useLegoStore } from "../store";
import { useBatchLegoHistory } from "../hooks";
import { useEffect, useRef, useMemo } from "react";
import HistoryCard from "./HistoryCard";
import useAuthStore from "@/store/useAuthStore";
import { cn } from "@/lib/utils";

export default function HistoryList() {
    const { generationHistory, isGenerating } = useLegoStore();
    const { isAuthenticated } = useAuthStore();

    const {
        isLoading,
        isError,
        hasMore,
        loadMore,
        isLoadingMore
    } = useBatchLegoHistory(isAuthenticated);

    const historyRef = useRef<HTMLDivElement>(null);
    const loaderRef = useRef<HTMLDivElement>(null);

    // 只在第一次加载时滚动到顶部
    useEffect(() => {
        if (historyRef.current) {
            historyRef.current.scrollTo({ top: 0 });
        }
    }, []);

    // 设置Intersection Observer来监测滚动到底部
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                const target = entries[0];
                console.log('Intersection Observer triggered:', {
                    isIntersecting: target.isIntersecting,
                    hasMore,
                    isLoadingMore,
                    loadedCount: generationHistory.length,
                    totalItems: generationHistory.length
                });

                if (target.isIntersecting && hasMore && !isLoadingMore) {
                    console.log("Loader element is visible, loading more items...");
                    loadMore();
                }
            },
            {
                root: historyRef.current, // 使用历史容器作为root
                rootMargin: '50px', // 减少提前触发距离
                threshold: 0.1
            }
        );

        const currentLoaderRef = loaderRef.current;
        if (currentLoaderRef) {
            observer.observe(currentLoaderRef);
            console.log("Intersection Observer set up for loader element");
        }

        return () => {
            if (currentLoaderRef) {
                observer.unobserve(currentLoaderRef);
                console.log("Intersection Observer cleaned up");
            }
        };
    }, [hasMore, isLoadingMore, loadMore, generationHistory.length]);

    // 使用 useMemo 缓存渲染的历史卡片，避免每次渲染都重新创建组件
    const historyCards = useMemo(() => {
        // 显示所有加载的图片
        return generationHistory.map((item) => (
            <HistoryCard key={item.id} item={item} />
        ));
    }, [generationHistory]);

    // 如果用户未登录，显示登录提示
    if (!isAuthenticated) {
        return (
            <div ref={historyRef} className="flex-1 px-3 md:px-6 py-3 md:py-6 overflow-auto">
                <div className="flex flex-col items-center justify-center h-[calc(100vh-250px)] md:h-[calc(100vh-250px)] rounded-xl">
                    <Blocks className="h-12 w-12 md:h-16 md:w-16 mb-3 md:mb-4 text-gray-600 dark:text-white" />
                    <h1 className="text-xl md:text-2xl font-bold mb-2 text-gray-900 dark:text-white">
                        Lego AI Image Generator
                    </h1>
                </div>
            </div>
        );
    }

    // 如果正在生成但历史记录为空，显示占位内容
    if (isGenerating && generationHistory.length === 0) {
        return (
            <div ref={historyRef} className="flex-1 px-3 md:px-6 py-3 md:py-6 overflow-auto">
                <div className="flex justify-center items-center py-8 md:py-12">
                    <div className="animate-spin w-6 md:w-8 h-6 md:h-8 border-2 rounded-full border-gray-300 border-t-gray-600 dark:border-primary/30 dark:border-t-primary"></div>
                    <span className="ml-3 text-sm md:text-base text-gray-700 dark:text-primary">
                        Generating your first image...
                    </span>
                </div>
            </div>
        );
    }

    return (
        <div ref={historyRef} className="flex-1 p-2 md:p-3 pb-28 md:pb-60 overflow-auto bg-transparent">
            {isLoading && generationHistory.length === 0 && (
                <div className="flex justify-center items-center py-8 md:py-12">
                    <div className="animate-spin w-6 md:w-8 h-6 md:h-8 border-2 rounded-full border-gray-300 border-t-gray-600 dark:border-primary/30 dark:border-t-primary"></div>
                </div>
            )}

            {isError && (
                <div className="text-center py-6 md:py-10 text-red-600 dark:text-destructive">
                    Failed to load history. Please try again.
                </div>
            )}

            {isAuthenticated && generationHistory.length === 0 && !isLoading && !isError ? (
                <div className="flex flex-col items-center justify-center h-[calc(100vh-250px)] md:h-[calc(100vh-250px)] rounded-xl">
                    <Blocks className="h-12 w-12 md:h-16 md:w-16 mb-3 md:mb-4 text-gray-600 dark:text-white" />
                    <h1 className="text-xl md:text-2xl font-bold mb-2 text-gray-900 dark:text-white">
                        Lego AI Image Generator
                    </h1>
                    <p className="text-base md:text-lg max-w-md text-center px-4 text-gray-600 dark:text-muted-foreground">
                        Create stunning images by describing your idea and adding resources to guide the AI
                    </p>
                </div>
            ) : (
                <>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6">
                        {historyCards}
                    </div>

                    {/* Loading indicator */}
                    <div ref={loaderRef} className="py-4 flex justify-center">
                        {isLoadingMore && (
                            <div className="flex flex-col items-center">
                                <div className="animate-spin w-6 h-6 border-2 rounded-full mb-2 border-gray-300 border-t-gray-600 dark:border-primary/30 dark:border-t-primary"></div>
                                <div className="text-sm text-gray-600 dark:text-muted-foreground">
                                    Loading history images...
                                </div>
                            </div>
                        )}
                        {!isLoadingMore && hasMore && generationHistory.length > 0 && (
                            <div className="text-sm text-gray-600 dark:text-muted-foreground">
                                Scroll for more...
                            </div>
                        )}
                        {!hasMore && generationHistory.length > 0 && (
                            <div className="text-sm text-gray-600 dark:text-muted-foreground">
                                All {generationHistory.length} images loaded
                            </div>
                        )}
                    </div>
                </>
            )}
        </div>
    );
}
