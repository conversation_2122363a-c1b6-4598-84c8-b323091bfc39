"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import useCouponStore from "@/store/useCouponStore"
import { CouponStatus } from "@/app/membership/types/coupon"
import { useCouponLogic } from "@/hooks/useCouponLogic"

interface CouponPriceDisplayProps {
    monthlyPrice: number;
    yearlyPrice: number;
    billingCycle: "monthly" | "yearly";
    planName: string;
    className?: string;
}

export function CouponPriceDisplay({
    monthlyPrice,
    yearlyPrice,
    billingCycle,
    planName,
    className
}: CouponPriceDisplayProps) {
    const [discountedPrice, setDiscountedPrice] = useState<number>(0)
    const [discountAmount, setDiscountAmount] = useState<number>(0)
    const [hasDiscount, setHasDiscount] = useState<boolean>(false)
    const [discountType, setDiscountType] = useState<'percentage' | 'fixed_amount'>('percentage')
    const [originalPrice, setOriginalPrice] = useState<number>(0)

    const { activeCoupon, getActiveCoupon } = useCouponStore()
    const { isActiveMember, isEligibleForFirstMonth, isAuthenticated } = useCouponLogic()

    // 计算折扣价格 - 前端固定逻辑
    useEffect(() => {
        // 计算原价和显示价格
        let originalDisplayPrice: number
        let currentDisplayPrice: number

        if (billingCycle === 'yearly') {
            // 年度订阅：原价显示年度总价，计算基于年度价格
            originalDisplayPrice = yearlyPrice
            currentDisplayPrice = yearlyPrice
        } else {
            // 月度订阅：原价显示月度价格
            originalDisplayPrice = monthlyPrice
            currentDisplayPrice = monthlyPrice
        }

        setOriginalPrice(originalDisplayPrice)

        // 如果用户已经是活跃会员，不显示优惠券折扣
        if (isActiveMember) {
            setDiscountAmount(0)
            setDiscountedPrice(currentDisplayPrice)
            setHasDiscount(false)
            return
        }

        // UNLIMITED计划不参与任何优惠
        if (planName === 'UNLIMITED' || planName === 'TEST_UNLIMITED') {
            setDiscountAmount(0)
            setDiscountedPrice(currentDisplayPrice)
            setHasDiscount(false)
            return
        }

        const validCoupon = getActiveCoupon()

        // 如果用户有活跃优惠券，或者符合领券条件，或者是未登录用户（显示预期优惠价格以提高转化率）
        if ((validCoupon && validCoupon.status === CouponStatus.ACTIVE) ||
            (isAuthenticated && isEligibleForFirstMonth && !validCoupon) ||
            (!isAuthenticated)) {
            let discount = 0
            let finalPrice = currentDisplayPrice
            let type: 'percentage' | 'fixed_amount' = 'percentage'

            if (billingCycle === 'monthly') {
                // 月度订阅：首月90%优惠
                // 优惠金额 = 月度价格 * 0.9（精确计算，不取整）
                discount = monthlyPrice * 0.9
                finalPrice = currentDisplayPrice - discount
                type = 'percentage'
            } else if (billingCycle === 'yearly') {
                // 年度订阅：前6个月免费
                // 优惠金额 = 年度价格的一半（6个月免费 = 50% off）
                discount = yearlyPrice * 0.5
                finalPrice = currentDisplayPrice - discount
                type = 'fixed_amount'
            }

            setDiscountAmount(discount)
            setDiscountedPrice(finalPrice)
            setHasDiscount(true)
            setDiscountType(type)
        } else {
            setDiscountAmount(0)
            setDiscountedPrice(currentDisplayPrice)
            setHasDiscount(false)
        }
    }, [monthlyPrice, yearlyPrice, billingCycle, planName, activeCoupon, getActiveCoupon, isActiveMember, isEligibleForFirstMonth, isAuthenticated])

    // 格式化价格显示
    const formatPrice = (price: number) => {
        return price.toFixed(2)
    }

    // 如果没有折扣，显示原价
    if (!hasDiscount) {
        return (
            <div className={`space-y-2 text-center ${className}`}>
                <div className="text-3xl md:text-4xl font-bold">
                    ${formatPrice(originalPrice)}
                </div>
                <div className="text-sm text-muted-foreground">
                    per {billingCycle === "yearly" ? "year" : "month"}
                </div>
            </div>
        )
    }

    // 计算折扣百分比用于显示
    const discountPercentage = discountType === 'percentage' ? 90 : Math.round((discountAmount / originalPrice) * 100)

    // 检查是否为预期优惠价格（用户符合条件但还没领券，或者是未登录用户）
    const isPreviewDiscount = (isAuthenticated && isEligibleForFirstMonth && !getActiveCoupon()) || (!isAuthenticated)

    // 有折扣时的显示
    return (
        <div className={`space-y-2 md:space-y-3 ${className}`}>
            {/* 折扣标识 - 简化显示，移除重复信息 */}
            <div className="flex items-center justify-center">
                <span className={`text-sm font-medium px-3 py-1 rounded-full ${isPreviewDiscount
                    ? 'bg-gradient-to-r from-orange-500/20 to-yellow-500/20 text-orange-400 border border-orange-500/30'
                    : 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-400 border border-green-500/30'
                    }`}>
                    {isPreviewDiscount ? (
                        discountType === 'fixed_amount' && billingCycle === 'yearly'
                            ? '🎁 6 Months Free Available'
                            : '🔥 90% OFF Available'
                    ) : (
                        discountType === 'fixed_amount' && billingCycle === 'yearly'
                            ? '🎁 First 6 Months Free'
                            : '🔥 First Month 90% OFF'
                    )}
                </span>
            </div>

            {/* 价格显示 */}
            <div className="space-y-1 md:space-y-2">
                {/* 原价（划线） - 简化显示 */}
                <div className="text-center">
                    <span className="text-lg md:text-xl text-muted-foreground line-through">
                        ${formatPrice(originalPrice)}
                    </span>
                </div>

                {/* 折扣价 */}
                <div className="text-center">
                    <div className={`text-2xl md:text-3xl lg:text-4xl font-bold bg-clip-text text-transparent ${isPreviewDiscount
                        ? 'bg-gradient-to-r from-orange-400 to-yellow-400'
                        : 'bg-gradient-to-r from-green-400 to-emerald-400'
                        }`}>
                        ${formatPrice(discountedPrice)}
                    </div>
                    <div className="text-xs md:text-sm text-muted-foreground">
                        per {billingCycle === "yearly" ? "year" : "month"}
                        {isPreviewDiscount && " (after claiming)"}
                    </div>
                </div>
            </div>

            {/* 简化的折扣说明 - 仅在大屏幕显示详细信息 */}
            <div className="hidden lg:block p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl">
                <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Original Price:</span>
                        <span className="font-medium">${formatPrice(originalPrice)}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Your Discount:</span>
                        <span className="text-green-400 font-semibold">-${formatPrice(discountAmount)}</span>
                    </div>
                    <Separator className="my-2 bg-green-500/20" />
                    <div className="flex items-center justify-between text-sm font-semibold">
                        <span className="text-green-400">
                            {discountType === 'fixed_amount' && billingCycle === 'yearly'
                                ? 'You Pay (First Year):'
                                : 'First Month:'
                            }
                        </span>
                        <span className="text-green-400 text-lg">${formatPrice(discountedPrice)}</span>
                    </div>
                </div>
            </div>

            {/* 简化的说明文字
            <div className="text-center text-xs text-muted-foreground">
                {discountType === 'fixed_amount' && billingCycle === 'yearly'
                    ? '💡 50% off your first year'
                    : '💡 90% discount on first month only'
                }
            </div> */}
        </div>
    )
}
