"use client"

import { Gift, Percent } from "lucide-react"

interface BillingToggleProps {
  billingCycle: "monthly" | "yearly"
  onChange: (cycle: "monthly" | "yearly") => void
}

export function BillingToggle({ billingCycle, onChange }: BillingToggleProps) {
  return (
    <div className="w-full mb-8 flex justify-center">
      {/* Tab Container - More pill-like */}
      <div className="relative inline-flex bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-full p-1.5 border border-gray-200/50 dark:border-gray-700/50 shadow-lg backdrop-blur-sm">
        {/* Sliding background indicator - Pill shaped */}
        <div
          className={`absolute top-1.5 h-[calc(100%-12px)] rounded-full shadow-xl border border-white/20 dark:border-gray-600/30 transition-all duration-300 ease-out ${billingCycle === "yearly"
            ? "left-1.5 w-[calc(50%-6px)] bg-gradient-to-r from-orange-500 to-red-500"
            : "left-[calc(50%+1.5px)] w-[calc(50%-6px)] bg-gradient-to-r from-blue-500 to-purple-500"
            }`}
        />

        {/* Annual Tab - Pill shaped */}
        <button
          className={`relative z-10 px-12 rounded-full transition-all duration-300 font-medium transform hover:scale-105 ${billingCycle === "yearly"
            ? 'text-white shadow-sm'
            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
            }`}
          onClick={() => onChange("yearly")}
        >
          <div className="flex flex-col items-center gap-2">
            <span className="text-base font-semibold">Annual</span>
            <span className={`text-xs font-bold px-3 py-1 rounded-full transition-all duration-300 ${billingCycle === "yearly"
              ? 'bg-white text-orange-600 shadow-sm'
              : 'bg-orange-500 text-white shadow-md'
              }`}>
              6 Months FREE
            </span>
          </div>
        </button>

        {/* Monthly Tab - Pill shaped */}
        <button
          className={`relative z-10 px-8 py-4 rounded-full transition-all duration-300 font-medium transform hover:scale-105 ${billingCycle === "monthly"
            ? 'text-white shadow-sm'
            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
            }`}
          onClick={() => onChange("monthly")}
        >
          <div className="flex flex-col items-center gap-2">
            <span className="text-base font-semibold">Monthly</span>
            <span className={`text-xs font-bold px-3 py-1 rounded-full transition-all duration-300 ${billingCycle === "monthly"
              ? 'bg-white text-blue-600 shadow-sm'
              : 'bg-blue-500 text-white shadow-md'
              }`}>
              90% OFF First Month
            </span>
          </div>
        </button>
      </div>
    </div>
  )
}

