"use client"

import { useState } from "react"
import { Check, Rocket, Crown, OctagonX, Gift, Zap } from "lucide-react"
import { MembershipPlan, MembershipPlanName, UserMembership } from "../types"
import { paymentApi, CreateMembershipPaymentRequest } from "@/lib/api/payment"
import { CouponPriceDisplay } from "./coupon-price-display"
import useCouponStore from "@/store/useCouponStore"
import { useCouponLogic } from "@/hooks/useCouponLogic"
import useLoginDialogStore from "@/store/useLoginDialogStore"
import { toast } from "sonner"

interface PricingCardProps {
  plan: MembershipPlan
  billingCycle: "monthly" | "yearly"
  userMembership?: UserMembership
  isLoadingUserMembership: boolean
}

export function PricingCard({ plan, billingCycle, userMembership, isLoadingUserMembership }: PricingCardProps) {
  const [isProcessing, setIsProcessing] = useState(false)

  // 获取优惠券状态
  const { getActiveCoupon, claimFirstMonthDiscount } = useCouponStore()
  const { isEligibleForFirstMonth, activeCoupon, isAuthenticated } = useCouponLogic()
  const { openLoginDialog } = useLoginDialogStore()

  // 计算价格
  const price = billingCycle === "yearly" ? plan.yearly_price / 12 : plan.monthly_price;

  // 计算折扣

  // 判断是否为当前会员等级
  const isCurrentPlan = (userMembership?.plan_id === plan.id) && userMembership?.is_active;

  // 一键领券+订阅处理函数
  const handleClaimAndSubscribe = async () => {
    // 如果用户未登录，打开登录对话框
    if (!isAuthenticated) {
      openLoginDialog({
        pendingAction: {
          type: 'claim_and_subscribe',
          payload: { planId: plan.id, billingCycle },
          onSuccess: () => {
            // 登录成功后会重新渲染组件，用户需要再次点击
            toast.success("Login successful! Click the button again to claim your discount and subscribe", {
              duration: 4000
            });
          }
        }
      });
      return;
    }

    try {
      setIsProcessing(true);

      // 1. 先领取优惠券
      const claimResponse = await claimFirstMonthDiscount();

      if (!claimResponse.success) {
        toast.error("Failed to claim coupon", {
          description: claimResponse.message || "Please try again later"
        });
        setIsProcessing(false);
        return;
      }

      toast.success("🎉 Coupon claimed successfully!");

      // 2. 短暂延迟确保优惠券状态更新
      await new Promise(resolve => setTimeout(resolve, 500));

      // 3. 继续订阅流程
      await proceedWithSubscription(claimResponse.coupon?.id);

    } catch (err) {
      setIsProcessing(false);
      console.error('领券+订阅错误:', err);
      toast.error("Failed to process", {
        description: "Please try again later"
      });
    }
  };

  // 常规订阅处理函数
  const handleSubscribe = async () => {
    try {
      setIsProcessing(true);
      await proceedWithSubscription();
    } catch (err) {
      setIsProcessing(false);
      console.error('支付创建错误:', err);
    }
  };

  // 统一的订阅处理逻辑
  const proceedWithSubscription = async (couponId?: string) => {
    const priceId = billingCycle === "yearly" ? plan.stripe_price_id_year : plan.stripe_price_id_month;

    // 获取当前活跃的优惠券（如果没有传入couponId）
    const currentActiveCoupon = couponId ? { id: couponId } : getActiveCoupon();

    const paymentRequest: CreateMembershipPaymentRequest = {
      price_id: priceId,
      billing_cycle: billingCycle,
    };

    // 如果用户有活跃的优惠券且不是UNLIMITED计划，添加coupon_id
    // UNLIMITED计划不参与任何优惠
    if (currentActiveCoupon && plan.plan_name !== MembershipPlanName.UNLIMITED) {
      paymentRequest.coupon_id = currentActiveCoupon.id;
    }

    // 创建支付订单
    const { checkoutUrl } = await paymentApi.createMembershipPayment(paymentRequest);

    // 重定向到Stripe结账页面
    window.location.href = checkoutUrl;
  };

  // 计算会员积分数量
  const getMonthlyCredits = (planName: MembershipPlanName): number => {
    switch (planName) {
      case MembershipPlanName.PRO:
        return 1288;
      case MembershipPlanName.MAX:
        return 2888;
      case MembershipPlanName.UNLIMITED:
        return 0; // 无限制，不需要积分
      case MembershipPlanName.FREE:
      default:
        return 80;
    }
  };

  // 计算权益估算
  const calculateUsageEstimate = (credits: number) => {
    if (credits === 0) return null; // UNLIMITED计划不显示估算

    const videos = Math.floor(credits / 30);
    const images = Math.floor(credits / 4);
    const audios = Math.floor(credits / 5);

    return { videos, images, audios };
  };

  const monthlyCredits = getMonthlyCredits(plan.plan_name as MembershipPlanName);
  const usageEstimate = calculateUsageEstimate(monthlyCredits);

  // 将特权字符串转换为特权对象数组
  const featureObjects = plan.features.map(text => {
    // 如果特权文本以"无"或"不"开头，则标记为未包含
    const isNegative = text.startsWith('-') || text.startsWith('不');

    // Replace specific text with new wording
    let modifiedText = text.replace(/^[-不]/, '');
    if (modifiedText === "Access to WAN-T2V-1.3B, WAN-I2V-14B-720P") {
      modifiedText = "Fully access to 101 top AI video models";
    }

    return {
      text: modifiedText,
      included: !isNegative
    };
  });

  const renderIcon = () => {
    // 根据会员等级选择图标
    switch (plan.plan_name) {
      case MembershipPlanName.FREE:
        return <Rocket className="h-8 w-8" />
      case MembershipPlanName.PRO:
        return <Zap className="h-8 w-8" />
      case MembershipPlanName.MAX:
        return <Crown className="h-8 w-8 text-purple-500" />
      case MembershipPlanName.UNLIMITED:
        return <Crown className="h-8 w-8 text-amber-500" /> // 使用金色皇冠图标
      default:
        return null
    }
  }

  return (
    <>
      <div className={`relative rounded-2xl overflow-hidden border-2 transition-all duration-300 hover:scale-105 hover:shadow-2xl
        ${plan.plan_name === MembershipPlanName.UNLIMITED
          ? 'border-amber-500/50 bg-gradient-to-br from-amber-50/5 via-card to-amber-100/10 shadow-amber-500/20'
          : plan.plan_name === MembershipPlanName.MAX
            ? 'border-purple-500/50 bg-gradient-to-br from-purple-50/5 via-card to-purple-100/10 shadow-purple-500/20'
            : plan.is_popular
              ? 'border-blue-500/30 bg-gradient-to-br from-blue-50/3 via-card to-blue-100/5 shadow-blue-500/15'
              : 'border-border/50 bg-gradient-to-br from-card to-muted/20 shadow-lg'}
        shadow-xl backdrop-blur-sm`}>

        {/* 简化PRO的popular标识 */}
        {plan.is_popular && (
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-blue-500/80 to-purple-500/80 text-white text-xs font-medium py-1.5 text-center shadow-md">
            🔥 Popular
          </div>
        )}

        {/* 为MAX添加特殊标识 */}
        {plan.plan_name === MembershipPlanName.MAX && (
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-purple-500 to-indigo-500 text-white text-xs font-medium py-1.5 text-center shadow-md">
            👑 Advanced
          </div>
        )}

        {plan.plan_name === MembershipPlanName.UNLIMITED && (
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-amber-500 to-orange-500 text-white text-sm font-bold py-2 text-center shadow-lg">
            ⚡ Unlimited Generation • Premium Experience
          </div>
        )}

        <div className={`p-4 md:p-6 ${plan.plan_name === MembershipPlanName.UNLIMITED ? 'pt-12 md:pt-14' : (plan.is_popular || plan.plan_name === MembershipPlanName.MAX) ? 'pt-8 md:pt-10' : ''}`}>
          <div className="flex items-center gap-2 md:gap-3 mb-3 md:mb-4">
            {renderIcon()}
            <h3 className="text-lg md:text-xl font-bold">{plan.plan_name}</h3>
          </div>

          <div className="mb-4 md:mb-6">
            <CouponPriceDisplay
              monthlyPrice={plan.monthly_price}
              yearlyPrice={plan.yearly_price}
              billingCycle={billingCycle}
              planName={plan.plan_name}
            />
            {billingCycle === "yearly" && plan.monthly_price > price && (
              <div className="text-sm text-muted-foreground mt-1">
                <span className="line-through">${plan.monthly_price.toFixed(2)}/Monthly</span>
              </div>
            )}
          </div>

          {/* UNLIMITED计划的特殊说明 */}
          {plan.plan_name === MembershipPlanName.UNLIMITED && (
            <div className="mb-4 md:mb-6 p-3 rounded-lg bg-gradient-to-r from-amber-50/50 to-orange-50/50 border border-amber-200/50">
              <div className="text-sm font-medium text-amber-800 dark:text-amber-200 mb-1">Unlimited Generation</div>
              <div className="text-xs text-amber-700 dark:text-amber-300">
                Create unlimited videos, images, and audios without credit limits
              </div>
            </div>
          )}

          {plan.level > 0 && !isLoadingUserMembership && (
            <button
              type="button"
              onClick={
                // 如果用户已认证、符合领券条件、没有活跃优惠券且不是UNLIMITED计划，使用一键领券+订阅
                // 如果用户未登录且不是UNLIMITED计划，也使用一键领券+订阅流程（会触发登录）
                (isAuthenticated && isEligibleForFirstMonth && !activeCoupon && plan.plan_name !== MembershipPlanName.UNLIMITED) ||
                  (!isAuthenticated && plan.plan_name !== MembershipPlanName.UNLIMITED)
                  ? handleClaimAndSubscribe
                  : handleSubscribe
              }
              disabled={isCurrentPlan || isProcessing}
              className={`group relative w-full py-3 md:py-4 px-4 md:px-6 rounded-xl font-semibold text-base md:text-lg transition-all duration-300 mb-4 md:mb-6 overflow-hidden
                ${isCurrentPlan
                  ? 'bg-muted/50 text-muted-foreground cursor-not-allowed border border-muted'
                  : isProcessing
                    ? 'bg-gradient-to-r from-blue-500/80 to-purple-500/80 text-white cursor-wait'
                    : plan.plan_name === 'UNLIMITED'
                      ? 'bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                      : plan.plan_name === 'MAX'
                        ? 'bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                        : plan.is_popular
                          ? 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-md hover:shadow-lg transform hover:scale-105'
                          : 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white shadow-md hover:shadow-lg transform hover:scale-102'
                }`}
            >
              {/* 背景动画效果 */}
              {!isCurrentPlan && !isProcessing && (
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              )}

              {/* 按钮文字 */}
              <span className="relative z-10 flex items-center justify-center gap-2">
                {isCurrentPlan ? (
                  <>
                    <Check className="h-5 w-5" />
                    Current Plan
                  </>
                ) : isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                    Processing...
                  </>
                ) : (
                  // 根据优惠券状态显示不同的按钮文字
                  (isAuthenticated && isEligibleForFirstMonth && !activeCoupon && plan.plan_name !== MembershipPlanName.UNLIMITED) ||
                    (!isAuthenticated && plan.plan_name !== MembershipPlanName.UNLIMITED) ? (
                    <>
                      <Gift className="h-5 w-5 group-hover:animate-pulse" />
                      <span className="hidden md:inline">
                        {billingCycle === "yearly" ? 'Claim 6-Month Free & Subscribe' : 'Claim 90% OFF & Subscribe'}
                      </span>
                      <span className="md:hidden">
                        {billingCycle === "yearly" ? 'Claim & Subscribe' : 'Get 90% OFF'}
                      </span>
                    </>
                  ) : (
                    <>
                      {plan.plan_name === 'UNLIMITED'
                        ? <Crown className="h-5 w-5 group-hover:animate-pulse" />
                        : <Rocket className="h-5 w-5 group-hover:animate-pulse" />}
                      <span className="hidden md:inline">
                        {plan.plan_name === 'UNLIMITED' ? 'Get Unlimited Access' : 'Start Subscription'}
                      </span>
                      <span className="md:hidden">
                        {plan.plan_name === 'UNLIMITED' ? 'Get Unlimited' : 'Subscribe'}
                      </span>
                    </>
                  )
                )}
              </span>

              {/* 特殊标识 */}
              {!isCurrentPlan && !isProcessing && plan.is_popular && (
                <div className="absolute top-0.5 right-0.5 bg-white/15 text-white text-[10px] px-1.5 py-0.5 rounded-full">
                  Popular
                </div>
              )}
            </button>
          )}

          {/* 权益估算 */}
          {usageEstimate && (
            <div className="mb-4 md:mb-6">
              <div className="text-sm font-medium text-muted-foreground mb-3">Monthly Usage Estimate</div>
              <ul className="space-y-2 md:space-y-3">
                <li className="flex items-start gap-2">
                  <Check className="h-4 w-4 md:h-5 md:w-5 text-blue-600 dark:text-blue-400 shrink-0 mt-0.5" />
                  <span className="text-sm md:text-base text-foreground">Up to {usageEstimate.videos} videos per month</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="h-4 w-4 md:h-5 md:w-5 text-blue-600 dark:text-blue-400 shrink-0 mt-0.5" />
                  <span className="text-sm md:text-base text-foreground">Up to {usageEstimate.images} images per month</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="h-4 w-4 md:h-5 md:w-5 text-blue-600 dark:text-blue-400 shrink-0 mt-0.5" />
                  <span className="text-sm md:text-base text-foreground">Up to {usageEstimate.audios} audios per month</span>
                </li>
              </ul>
            </div>
          )}

          <ul className="space-y-2 md:space-y-3">
            {featureObjects.map((feature, index) => (
              <li key={index} className="flex items-start gap-2">
                {feature.included ? (
                  <Check className="h-4 w-4 md:h-5 md:w-5 text-green-600 dark:text-green-400 shrink-0 mt-0.5" />
                ) : (
                  <OctagonX className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground shrink-0 mt-0.5" />
                )}
                <span className={`text-sm md:text-base ${feature.included ? "text-foreground" : "text-muted-foreground"}`}>{feature.text}</span>
              </li>
            ))}

            {/* {plan.plan_name === MembershipPlanName.UNLIMITED && (
              <li className="mt-4 pt-4 border-t border-amber-200/50">
                <div className="text-amber-700 text-sm font-medium">
                  Note: Maximum 5 concurrent video tasks per 10 minutes
                </div>
              </li>
            )} */}
          </ul>
        </div>
      </div>
    </>
  )
}

