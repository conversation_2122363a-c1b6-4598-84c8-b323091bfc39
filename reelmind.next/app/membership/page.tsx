"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { BillingToggle } from "./components/billing-toggle"
import { PricingCard } from "./components/pricing-card"
import { FAQSection } from "./components/faq-section"
import Placeholder from "./components/placeholder"
import { CreditsCard } from "./components/credits-card"
import { CreditsHistory } from "./components/credits-history"
import { CouponStatusCard } from "./components/coupon-status-card"
import { membershipApi } from "@/lib/api/membership"
import { creditsApi } from "@/lib/api/credits"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2, CreditCard, Crown, ChevronDown, ChevronUp } from "lucide-react"
import useAuthStore from "@/store/useAuthStore"

export default function MembershipPage() {
    const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly")
    const [showCreditsHistory, setShowCreditsHistory] = useState(false)
    const { isAuthenticated: isAuth, session, hasHydrated, user, isInitialized } = useAuthStore()

    // 只有在已经完成初始化且确实有认证信息时才启用API查询
    const shouldEnableQueries = hasHydrated && isInitialized && isAuth && !!session && !!user

    // 使用 React Query 获取会员信息 - 复用 user-nav 中已加载的数据
    const {
        data: userMembership,
        isLoading: isLoadingMembership
    } = useQuery({
        queryKey: ["membership", "user"],
        queryFn: async () => {
            const data = await membershipApi.getUserMembership();
            return data;
        },
        enabled: shouldEnableQueries,
    });

    // 使用 React Query 获取积分余额 - 复用 user-nav 中已加载的数据
    const {
        data: balance,
        isLoading: isLoadingCredits
    } = useQuery({
        queryKey: ["credits", "balance"],
        queryFn: async () => {
            const data = await creditsApi.getUserBalance();
            return data.data.balance;
        },
        enabled: shouldEnableQueries,
    });

    // 使用 React Query 获取会员计划列表
    const {
        data: membershipPlans,
        isLoading: isLoadingPlans,
        error
    } = useQuery({
        queryKey: ["membership", "plans"],
        queryFn: async () => {
            const plans = await membershipApi.getMembershipPlans();
            return plans;
        },
        staleTime: 1000 * 60 * 5, // 5分钟内不重新请求
    });

    // 对会员计划进行排序
    const sortedPlans = membershipPlans && membershipPlans.length > 0
        ? [...membershipPlans].sort((a, b) => a.level - b.level)
        : [];

    // 判断用户是否有活跃会员
    const hasActiveMembership = userMembership && userMembership.is_active;

    // 合并加载状态
    const isLoading = isLoadingMembership || isLoadingPlans;

    // 渲染会员订阅部分
    const renderMembershipSection = () => (
        <section className="mb-12 md:mb-16">
            <div className="flex flex-col items-center mb-8 md:mb-12">
                <div className="text-center mb-8">
                    <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                        Membership Plans
                    </h2>
                </div>

                {/* Billing Toggle */}
                <BillingToggle billingCycle={billingCycle} onChange={(cycle) => setBillingCycle(cycle)} />
            </div>

            {/* Pricing Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-6">
                {isLoading
                    ? <Placeholder />
                    : sortedPlans.length > 0
                        ? (
                            // 显示会员计划
                            sortedPlans.map((plan) => (
                                <PricingCard
                                    key={`plan-${plan.level}`}
                                    plan={plan}
                                    billingCycle={billingCycle}
                                    userMembership={userMembership || undefined}
                                    isLoadingUserMembership={isLoading}
                                />
                            ))
                        ) : error
                            ? (
                                // 获取会员计划失败时显示错误信息
                                <div className="col-span-full text-center p-8 border border-destructive/30 rounded-xl bg-destructive/10">
                                    <p className="text-destructive">Failed to get membership plans, please try again later.</p>
                                </div>
                            )
                            : <Placeholder />
                }
            </div>
        </section>
    );

    // 渲染积分购买部分
    const renderCreditsSection = () => (
        <section className="mb-12 md:mb-16">
            <div className="flex flex-col items-center mb-8 md:mb-10">
                <div className="text-center">
                    <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                        Purchase Credits
                    </h2>
                    <p className="text-base md:text-lg text-muted-foreground max-w-2xl mx-auto">
                        Get instant access to AI video generation with our flexible credit packages. Perfect for occasional use or trying out our platform.
                    </p>
                </div>
            </div>

            <div className="mx-auto">
                <CreditsCard />

                <div className="mt-6">
                    <button
                        type="button"
                        onClick={() => setShowCreditsHistory(!showCreditsHistory)}
                        className="flex items-center justify-center w-full py-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                        {showCreditsHistory ? (
                            <>
                                Hide Transaction History <ChevronUp className="ml-1 h-4 w-4" />
                            </>
                        ) : (
                            <>
                                Show Transaction History <ChevronDown className="ml-1 h-4 w-4" />
                            </>
                        )}
                    </button>

                    {showCreditsHistory && (
                        <div className="mt-4">
                            <CreditsHistory />
                        </div>
                    )}
                </div>
            </div>
        </section>
    );

    return (
        <div className="relative min-h-screen bg-background text-foreground">
            {/* Main Content */}
            <main className="px-4 py-8 pb-20 lg:pb-8">
                <div className="text-center mb-12">
                    <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full px-4 py-2 mb-6">
                        <Crown className="h-5 w-5 text-blue-400" />
                        <span className="text-sm font-medium text-blue-400">Premium Membership</span>
                    </div>
                    <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 bg-clip-text">
                        Choose Your Perfect Plan
                    </h1>
                    {/* <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
                        Unlock the full potential of AI video generation with our flexible subscription plans and credit packages
                    </p> */}


                </div>

                {/* User Status Dashboard - 显示会员、积分和优惠券信息 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-8 md:mb-12">
                    {/* 会员信息卡片 */}
                    <Card className="bg-card shadow-sm">
                        <CardContent className="p-4 md:p-6">
                            <div className="flex items-center gap-3 mb-4">
                                <Crown className="h-5 w-5 md:h-6 md:w-6" />
                                <h3 className="text-lg md:text-xl font-bold">Membership Status</h3>
                            </div>

                            {isLoading ? (
                                <div className="flex items-center space-x-2 py-4">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span>Loading membership info...</span>
                                </div>
                            ) : userMembership && userMembership.is_active ? (
                                <div className="space-y-2">
                                    <div className="flex items-baseline justify-between">
                                        <span className="text-muted-foreground">Current Plan:</span>
                                        <span className="text-lg md:text-xl font-bold">{userMembership.plan_name}</span>
                                    </div>
                                    {userMembership.expires_at && (
                                        <div className="flex items-baseline justify-between">
                                            <span className="text-muted-foreground">Expires on:</span>
                                            <span>{new Date(userMembership.expires_at).toLocaleDateString()}</span>
                                        </div>
                                    )}
                                    <div className="mt-4 pt-4 border-t border-border">
                                        <p className="text-xs md:text-sm text-muted-foreground">
                                            {userMembership.plan_name === 'UNLIMITED'
                                                ? 'You have unlimited access to all generation features without credit consumption.'
                                                : `You have access to all ${userMembership.plan_name} features and benefits.`
                                            }
                                        </p>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-2">
                                    <p className="text-base md:text-lg">You don't have an active membership.</p>
                                    <p className="text-xs md:text-sm text-muted-foreground">
                                        Subscribe to a plan below to unlock premium features.
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* 积分信息卡片 */}
                    <Card className={`shadow-sm ${userMembership?.plan_name === 'UNLIMITED'
                        ? 'bg-gradient-to-br from-amber-50/10 via-card to-purple-50/10 border-2 border-amber-500/30 shadow-amber-500/20'
                        : 'bg-card'
                        }`}>
                        <CardContent className="p-4 md:p-6 relative">
                            {userMembership?.plan_name === 'UNLIMITED' && (
                                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/3 via-purple-500/3 to-pink-500/3 rounded-lg"></div>
                            )}
                            <div className="flex items-center gap-3 mb-4 relative z-10">
                                <CreditCard className={`h-5 w-5 md:h-6 md:w-6 ${userMembership?.plan_name === 'UNLIMITED' ? 'text-amber-500' : ''}`} />
                                <h3 className={`text-lg md:text-xl font-bold ${userMembership?.plan_name === 'UNLIMITED'
                                    ? 'text-transparent bg-gradient-to-r from-amber-400 via-purple-500 to-pink-500 bg-clip-text'
                                    : ''
                                    }`}>Credits Balance</h3>
                            </div>

                            {isLoadingCredits ? (
                                <div className="flex items-center space-x-2 py-4">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span>Loading credits info...</span>
                                </div>
                            ) : (
                                <div className="space-y-2 relative z-10">
                                    <div className="flex items-baseline justify-between">
                                        <span className="text-muted-foreground">Available Credits:</span>
                                        <span className={`text-2xl md:text-3xl font-bold ${userMembership?.plan_name === 'UNLIMITED'
                                            ? 'text-transparent bg-gradient-to-r from-amber-400 via-purple-500 to-pink-500 bg-clip-text text-4xl unlimited-infinity'
                                            : ''
                                            }`}>
                                            {userMembership?.plan_name === 'UNLIMITED' ? '∞' : (balance !== null ? balance : '—')}
                                        </span>
                                    </div>
                                    <div className="mt-4 pt-4 border-t border-border">
                                        <div className="text-xs md:text-sm text-muted-foreground space-y-1">
                                            {userMembership?.plan_name === 'UNLIMITED' ? (
                                                <>
                                                    <p>• Unlimited video generation</p>
                                                    <p>• Unlimited image generation</p>
                                                    <p>• Unlimited sound generation</p>
                                                </>
                                            ) : balance !== null && balance !== undefined && balance > 0 ? (
                                                <>
                                                    <p>• Up to {Math.floor(balance / 5)} videos</p>
                                                    <p>• Up to {Math.floor(balance / 3)} images</p>
                                                    <p>• Up to {Math.floor(balance / 8)} sounds</p>
                                                </>
                                            ) : (
                                                <>
                                                    <p>• Videos: from 5 credits</p>
                                                    <p>• Images: from 3 credits</p>
                                                    <p>• Sounds: from 8 credits</p>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* 优惠券状态卡片 */}
                    <CouponStatusCard billingCycle={billingCycle} />
                </div>

                {/* 根据用户会员状态决定显示顺序 */}
                {hasActiveMembership ? (
                    <>
                        {/* 当用户已订阅会员时，先显示积分部分 */}
                        {renderCreditsSection()}
                        <hr className="my-8 md:my-12 border-t border-border" />
                        {renderMembershipSection()}
                    </>
                ) : (
                    <>
                        {/* 当用户未订阅会员时，保持原有顺序 */}
                        {renderMembershipSection()}
                        <hr className="my-8 md:my-12 border-t border-border" />
                        {renderCreditsSection()}
                    </>
                )}

                {/* FAQ Section */}
                <FAQSection />
            </main>

            {/* Space to account for mobile navigation bar */}
            <div className="h-16 lg:h-0"></div>

            {/* Footer */}
            <footer className="border-t border-border py-6 md:py-8 text-center text-xs md:text-sm text-muted-foreground">
                <p>© {new Date().getFullYear()} ReelMind. All rights reserved.</p>
            </footer>
        </div>
    )
}

