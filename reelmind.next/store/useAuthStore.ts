import { create } from 'zustand';
import { createClient } from '@/lib/supabase/client';
import { User, Session } from '@supabase/supabase-js';
import { persist } from 'zustand/middleware';

// 定义认证状态接口
interface AuthState {
    // 状态
    isAuthenticated: boolean;
    isLoading: boolean;
    user: User | null;
    session: Session | null;
    lastChecked: number | null;
    isInitialized: boolean;

    // 认证操作
    signIn: (email: string, password: string) => Promise<void>;
    signUp: (email: string, password: string) => Promise<{ data: any; error: Error | null }>;
    signOut: () => Promise<void>;
    signInWithGoogle: () => Promise<void>;

    // 状态管理操作
    checkAuthStatus: () => Promise<boolean>;
    clearAuthStatus: () => void;
    setAuthState: (user: User | null, session: Session | null, isInitial?: boolean) => void;

    // 系统操作标记
    hasHydrated: boolean;
    setHasHydrated: (state: boolean) => void;
}

// 刷新间隔常量
const AUTH_REFRESH_INTERVAL = 5 * 60 * 1000; // 5分钟刷新一次认证状态

// 创建认证存储
const useAuthStore = create<AuthState>()(
    persist(
        (set, get) => ({
            // 初始状态
            isAuthenticated: false,
            isLoading: true,
            user: null,
            session: null,
            lastChecked: null,
            isInitialized: false,
            hasHydrated: false,

            // 设置初始化标记
            setHasHydrated: (state: boolean) => {
                set({ hasHydrated: state });
            },

            // 登录方法
            signIn: async (email: string, password: string) => {
                set({ isLoading: true });
                try {
                    const supabase = createClient();
                    const { error } = await supabase.auth.signInWithPassword({
                        email,
                        password,
                    });

                    if (error) throw error;

                    // 状态将通过onAuthStateChange更新
                } catch (error) {
                    console.error("登录失败:", error);
                    throw error;
                } finally {
                    set({ isLoading: false });
                }
            },

            // 注册方法
            signUp: async (email: string, password: string) => {
                set({ isLoading: true });
                try {
                    const supabase = createClient();
                    const result = await supabase.auth.signUp({
                        email,
                        password,
                        options: {
                            emailRedirectTo: typeof window !== 'undefined'
                                ? `${window.location.origin}/auth/callback`
                                : undefined,
                        },
                    });

                    return { data: result.data, error: result.error };
                } catch (error) {
                    console.error("注册失败:", error);
                    return { data: null, error: error as Error };
                } finally {
                    set({ isLoading: false });
                }
            },

            // Google登录
            signInWithGoogle: async () => {
                set({ isLoading: true });
                try {
                    const supabase = createClient();
                    const { error } = await supabase.auth.signInWithOAuth({
                        provider: 'google',
                        options: {
                            redirectTo: typeof window !== 'undefined'
                                ? `${window.location.origin}/auth/callback`
                                : undefined,
                            queryParams: {
                                access_type: 'offline',
                                prompt: 'consent',
                            },
                        },
                    });

                    if (error) throw error;
                    // 状态将通过onAuthStateChange更新
                } catch (error) {
                    console.error("Google登录失败:", error);
                    throw error;
                } finally {
                    set({ isLoading: false });
                }
            },

            // 登出方法
            signOut: async () => {
                set({ isLoading: true });
                try {
                    const supabase = createClient();
                    const { error } = await supabase.auth.signOut();

                    if (error) throw error;

                    // 清除认证状态 - 这里立即清除，不等待onAuthStateChange
                    get().clearAuthStatus();
                } catch (error) {
                    console.error("登出失败:", error);
                    throw error;
                } finally {
                    set({ isLoading: false });
                }
            },

            // 检查认证状态
            checkAuthStatus: async () => {
                // 如果已经在加载中，跳过
                if (get().isLoading && get().lastChecked) return get().isAuthenticated;

                // 如果最近检查过且未过期，直接返回状态
                const lastChecked = get().lastChecked;
                if (lastChecked && Date.now() - lastChecked < AUTH_REFRESH_INTERVAL) {
                    return get().isAuthenticated;
                }

                // 开始检查
                set({ isLoading: true });

                try {
                    const supabase = createClient();
                    const { data } = await supabase.auth.getSession();
                    const { session } = data;

                    if (session && session.user) {
                        // 更新认证状态
                        set({
                            isAuthenticated: true,
                            user: session.user,
                            session: session,
                            lastChecked: Date.now(),
                            isLoading: false
                        });

                        // 存储token到sessionStorage
                        if (typeof window !== 'undefined') {
                            sessionStorage.setItem('token', session.access_token);
                        }

                        return true;
                    } else {
                        // 清除认证状态
                        get().clearAuthStatus();
                        return false;
                    }
                } catch (error) {
                    console.error('认证状态检查失败:', error);
                    // 出错时清除认证状态
                    get().clearAuthStatus();
                    return false;
                } finally {
                    set({ isLoading: false });
                }
            },

            // 清除认证状态
            clearAuthStatus: () => {
                set({
                    isAuthenticated: false,
                    user: null,
                    session: null,
                    lastChecked: Date.now(),
                    isInitialized: true,
                });

                // 清除sessionStorage中的token
                if (typeof window !== 'undefined') {
                    sessionStorage.removeItem('token');
                }
            },

            // 设置认证状态
            setAuthState: (user: User | null, session: Session | null, isInitial?: boolean) => {
                const isAuthenticated = !!user && !!session;

                set({
                    isAuthenticated,
                    user,
                    session,
                    lastChecked: Date.now(),
                    isLoading: false,
                    isInitialized: isInitial || isAuthenticated,
                });

                // 存储token到sessionStorage
                if (isAuthenticated && typeof window !== 'undefined') {
                    sessionStorage.setItem('token', session!.access_token);
                }
            }
        }),
        {
            name: 'auth-storage',
            // 仅持久化部分安全的字段
            partialize: (state) => ({
                isAuthenticated: state.isAuthenticated,
                lastChecked: state.lastChecked,
                hasHydrated: state.hasHydrated
            }),
            onRehydrateStorage: () => (state) => {
                if (state) {
                    state.setHasHydrated(true);
                    // 不在这里检查认证状态，让 AuthListener 处理
                }
            },
        }
    )
);

export default useAuthStore; 