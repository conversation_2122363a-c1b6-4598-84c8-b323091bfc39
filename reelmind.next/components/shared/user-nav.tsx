"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import {
  User,
  LogOut,
  BookMarked,
  Moon,
  Sun,
  Crown,
  Sparkles,
  Zap
} from "lucide-react";
import { Avatar } from "./avatar";
import { useThemeContext } from "@/contexts/theme-context";
import { ClientOnlyWithFallback } from "@/components/client-only";
import { MembershipPlanName } from "@/app/membership/types";
import useLoginDialogStore from "@/store/useLoginDialogStore";
import useAuthStore from "@/store/useAuthStore";
import { useQuery } from "@tanstack/react-query";
import { membershipApi } from "@/lib/api/membership";
import { creditsApi } from "@/lib/api/credits";
import { GiftIcon } from "@/components/credits/gift-icon";

export function UserNav() {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user, signOut } = useAuth();
  const { theme, setTheme } = useThemeContext();
  const { openLoginDialog } = useLoginDialogStore();
  const { isAuthenticated, session, hasHydrated, isInitialized } = useAuthStore();

  // 只有在已经完成初始化且确实有认证信息时才启用API查询
  const shouldEnableQueries = hasHydrated && isInitialized && isAuthenticated && !!session && !!user;

  // 使用 react-query 获取会员信息
  const { data: userMembership } = useQuery({
    queryKey: ["membership", "user"],
    queryFn: async () => {
      const data = await membershipApi.getUserMembership();
      return data;
    },
    enabled: shouldEnableQueries,
    retry: false, // 不重试，避免反复调用
    staleTime: 1000 * 60 * 5, // 5分钟内不重新请求
  });

  // 使用 react-query 获取积分余额
  const { data: creditBalance, isLoading } = useQuery({
    queryKey: ["credits", "balance"],
    queryFn: async () => {
      const data = await creditsApi.getUserBalance();
      return data.data.balance;
    },
    enabled: shouldEnableQueries,
    retry: false, // 不重试，避免反复调用
    staleTime: 1000 * 60 * 5, // 5分钟内不重新请求
  });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleLogout = async () => {
    try {
      await signOut();
      setShowDropdown(false);
    } catch (error) {
      console.error("Failed to sign out:", error);
    }
  };

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  // 组合会员和积分的胶囊UI
  const renderMembershipAndCredits = () => {
    if (!user) return null;

    // 检查是否为UNLIMITED会员
    const isUnlimitedMember = userMembership?.plan_name === MembershipPlanName.UNLIMITED && userMembership?.is_active;

    const membershipIcon = userMembership?.plan_name === MembershipPlanName.PRO
      ? <Sparkles className="h-4 w-4 text-zinc-800 dark:text-zinc-100" />
      : userMembership?.plan_name === MembershipPlanName.MAX
        ? <Crown className="h-4 w-4 text-amber-500" />
        : userMembership?.plan_name === MembershipPlanName.UNLIMITED
          ? (
            <div className="relative flex items-center">
              <Crown className="h-4 w-4 text-amber-400" />
              <Sparkles className="h-3 w-3 text-purple-400 absolute -top-1 -right-1" />
            </div>
          )
          : <Crown className="h-4 w-4 text-zinc-600 dark:text-zinc-400" />;

    const membershipText = userMembership?.plan_name;

    const membershipClass = userMembership?.plan_name === MembershipPlanName.PRO
      ? "text-zinc-800 dark:text-zinc-100"
      : userMembership?.plan_name === MembershipPlanName.MAX
        ? "text-amber-500"
        : userMembership?.plan_name === MembershipPlanName.UNLIMITED
          ? "text-transparent bg-gradient-to-r from-amber-400 via-purple-500 to-pink-500 bg-clip-text font-bold"
          : "text-zinc-600 dark:text-zinc-400";

    const capsuleGradient = userMembership?.plan_name === MembershipPlanName.PRO
      ? "bg-gradient-to-r from-zinc-200/80 to-zinc-300/80 dark:from-zinc-500/30 dark:to-zinc-600/30 border-zinc-300 dark:border-zinc-400/30 hover:from-zinc-200/90 hover:to-zinc-300/90 dark:hover:from-zinc-500/40 dark:hover:to-zinc-600/40"
      : userMembership?.plan_name === MembershipPlanName.MAX
        ? "bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-500/40 hover:from-amber-500/30 hover:to-orange-500/30"
        : userMembership?.plan_name === MembershipPlanName.UNLIMITED
          ? "bg-gradient-to-r from-amber-500/20 via-purple-500/20 to-pink-500/20 border-2 border-amber-400/50 hover:from-amber-500/30 hover:via-purple-500/30 hover:to-pink-500/30 shadow-lg shadow-amber-500/20"
          : "bg-card/40 hover:bg-card/60 border-border/50";

    return (
      <Link
        href="/membership"
        className={`px-2 lg:px-3 py-1 text-sm rounded-full transition-all flex items-center gap-1 lg:gap-2 border backdrop-blur-sm relative overflow-hidden h-8 ${capsuleGradient}`}
      >
        {/* UNLIMITED会员专属光效背景 */}
        {isUnlimitedMember && (
          <div className="absolute inset-0 bg-gradient-to-r from-amber-400/5 via-purple-500/5 to-pink-500/5"></div>
        )}

        <div className={`flex items-center relative z-10 ${membershipClass}`}>
          {membershipIcon}
          <span className="ml-1 hidden lg:inline font-medium">{membershipText}</span>
        </div>
        <div className="h-4 w-px bg-border/30 hidden lg:block relative z-10"></div>
        <div className="flex items-center text-zinc-800 dark:text-zinc-100 relative z-10">
          <Zap className={`h-4 w-4 mr-0.5 lg:mr-1 ${isUnlimitedMember ? 'text-amber-400' : ''}`} />
          <span className={`font-medium ${isUnlimitedMember ? 'text-transparent bg-gradient-to-r from-amber-400 to-purple-500 bg-clip-text text-lg font-bold unlimited-infinity' : ''}`}>
            {isLoading ? "..." : isUnlimitedMember ? "∞" : creditBalance}
          </span>
        </div>
      </Link>
    );
  };

  return (
    <div className="flex justify-end items-center gap-1.5 lg:gap-2 flex-1">
      <ClientOnlyWithFallback
        fallback={<div className="w-[36px] lg:w-[106px] h-8" />}
      >
        <button
          onClick={toggleTheme}
          className="flex items-center px-2 lg:px-3 py-1 text-sm text-muted-foreground rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 h-8"
          aria-label={theme === "dark" ? "Switch to Light Mode" : "Switch to Dark Mode"}
        >
          {theme === "dark" ? (
            <>
              <Sun className="h-4 w-4 lg:mr-2" />
              <span className="hidden lg:inline">Light Mode</span>
            </>
          ) : (
            <>
              <Moon className="h-4 w-4 lg:mr-2" />
              <span className="hidden lg:inline">Dark Mode</span>
            </>
          )}
        </button>
      </ClientOnlyWithFallback>

      {/* 组合会员和积分的胶囊 */}
      {user && renderMembershipAndCredits()}

      {/* 礼品图标 - 新用户积分领取 */}
      {user && (
        <div className="flex items-center h-8">
          <GiftIcon variant="header" size="md" />
        </div>
      )}

      {user ? (
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            className="relative ml-2 hover:ring-2 hover:ring-primary/20 rounded-full transition-all flex items-center justify-center h-8 w-8"
            title="User menu"
            aria-label="Open user menu"
          >
            <Avatar
              size={28}
            />
          </button>

          {showDropdown && (
            <div className="absolute right-0 mt-2 w-56 rounded-lg bg-background border shadow-lg py-1 z-50">
              <div className="px-4 py-2 border-b">
                <p className="text-sm font-medium">
                  {user.user_metadata?.full_name || "Creator"}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {user.email}
                </p>
              </div>

              <div className="py-1">
                <Link
                  href="/favorite"
                  className="flex items-center px-4 py-2 text-sm hover:bg-muted transition-colors"
                  onClick={() => setShowDropdown(false)}
                >
                  <BookMarked className="mr-2 h-4 w-4" />
                  <span>My Favorites</span>
                </Link>
              </div>

              <div className="border-t py-1">
                <Link
                  href="/profile"
                  className="flex items-center px-4 py-2 text-sm hover:bg-muted transition-colors"
                  onClick={() => setShowDropdown(false)}
                >
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </Link>
                {/* <Link
                  href="/settings/preferences"
                  className="flex items-center px-4 py-2 text-sm hover:bg-muted transition-colors"
                  onClick={() => setShowDropdown(false)}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Preferences</span>
                </Link> */}
              </div>

              <div className="border-t py-1">
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Sign Out</span>
                </button>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="flex items-center gap-2 whitespace-nowrap">
          <Link
            href="/membership"
            className="px-3 py-1 text-sm text-muted-foreground hover:text-foreground border border-border hover:border-border/80 rounded-md transition-colors h-8 flex items-center"
          >
            Pricing
          </Link>
          <button
            onClick={() => openLoginDialog()}
            className="px-4 py-1 text-sm bg-gray-800 hover:bg-gray-700 text-white rounded-md transition-colors h-8 flex items-center"
          >
            Sign In
          </button>
        </div>
      )}
    </div>
  );
}
