"use client";

import React, { useState, useEffect } from 'react';
import { Gift } from "lucide-react";
import { cn } from "@/lib/utils";
import useCreditsStore from "@/store/useCreditsStore";
import useAuthStore from "@/store/useAuthStore";
import { CreditClaimModal } from "./credit-claim-modal";

interface GiftIconProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "header" | "button";
}

export function GiftIcon({ className, size = "md", variant = "header" }: GiftIconProps) {
  const { isAuthenticated, session, hasHydrated, user, isInitialized } = useAuthStore();
  const {
    canClaimBonus,
    isCheckingClaimStatus,
    hasCheckedClaimStatus,
    checkClaimStatus
  } = useCreditsStore();
  const [showModal, setShowModal] = useState(false);

  // 只有在已经完成初始化且确实有认证信息时才检查状态
  const shouldCheckClaim = hasHydrated && isInitialized && isAuthenticated && !!session && !!user;

  // Check claim status when component mounts and user is authenticated
  useEffect(() => {
    // 只在用户认证时检查状态，避免未认证时的API调用
    if (shouldCheckClaim && !hasCheckedClaimStatus && !isCheckingClaimStatus) {
      console.log('[GiftIcon] Checking claim status');
      checkClaimStatus();
    }
  }, [shouldCheckClaim]);

  // Don't render if user is not authenticated or can't claim bonus
  if (!shouldCheckClaim || !canClaimBonus) {
    return null;
  }

  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  };

  const containerClasses = {
    header: "relative cursor-pointer transition-all hover:scale-110 flex items-center justify-center w-8 h-8",
    button: "flex items-center gap-2 px-3 py-2 rounded-lg bg-gradient-to-r from-amber-500 to-orange-600 hover:brightness-110 text-white transition-all"
  };

  const handleClick = () => {
    setShowModal(true);
  };

  if (variant === "button") {
    return (
      <>
        <button
          onClick={handleClick}
          className={cn(containerClasses.button, className)}
          data-gift-button
        >
          <Gift className={sizeClasses[size]} />
          <span className="text-sm font-medium">Get 88 Credits</span>
        </button>
        <CreditClaimModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
        />
      </>
    );
  }

  return (
    <>
      <div
        onClick={handleClick}
        className={cn(containerClasses.header, className)}
      >
        {/* Gift icon with glow effect */}
        <div className="relative">
          <Gift className={cn(sizeClasses[size], "text-amber-500")} />

          {/* Animated glow effect */}
          <div className="absolute inset-0 animate-ping">
            <Gift className={cn(sizeClasses[size], "text-amber-400 opacity-75")} />
          </div>

          {/* Notification dot */}
          <div className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full animate-pulse" />
        </div>
      </div>

      <CreditClaimModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
      />
    </>
  );
}
