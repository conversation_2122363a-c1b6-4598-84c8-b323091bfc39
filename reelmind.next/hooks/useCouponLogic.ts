"use client"

import { useState, useEffect } from "react"
import useCouponStore from "@/store/useCouponStore"
import useAuthStore from "@/store/useAuthStore"
import useLoginDialogStore from "@/store/useLoginDialogStore"
import { useQuery } from "@tanstack/react-query"
import { membershipApi } from "@/lib/api/membership"
import { toast } from "sonner"

/**
 * 共享的优惠券逻辑Hook
 * 用于复用优惠券相关的状态管理和业务逻辑
 */
export function useCouponLogic() {
    const [timeLeft, setTimeLeft] = useState<string>("")

    const { isAuthenticated } = useAuthStore()
    const { openLoginDialog } = useLoginDialogStore()
    const {
        isEligibleForFirstMonth,
        hasCheckedEligibility,
        activeCoupon,
        isLoading,
        checkFirstMonthEligibility,
        checkCouponStatus,
        claimFirstMonthDiscount,
        clearError
    } = useCouponStore()

    // 获取认证状态的更详细信息
    const { session, hasHydrated, user, isInitialized } = useAuthStore()

    // 只有在已经完成初始化且确实有认证信息时才启用API查询
    const shouldEnableQueries = hasHydrated && isInitialized && isAuthenticated && !!session && !!user

    // 获取用户会员状态
    const { data: userMembership } = useQuery({
        queryKey: ["membership", "user"],
        queryFn: async () => {
            const data = await membershipApi.getUserMembership();
            return data;
        },
        enabled: shouldEnableQueries,
    })

    // 检查用户是否已经是活跃会员
    const isActiveMember = userMembership?.is_active === true

    // Check user eligibility and coupon status - 合并到一个 useEffect 中
    useEffect(() => {
        // 只在用户认证且必要时才检查
        if (!shouldEnableQueries) {
            // 清理状态，避免显示过期数据
            return;
        }

        console.log('[useCouponLogic] User authenticated, checking coupon status and eligibility');

        // 并行检查状态和资格，但只在需要时执行
        const checkAll = async () => {
            try {
                // 总是检查优惠券状态（因为状态可能会变化）
                await checkCouponStatus();

                // 只在未检查过资格时检查资格
                if (!hasCheckedEligibility) {
                    await checkFirstMonthEligibility();
                }
            } catch (error) {
                console.error('[useCouponLogic] Error checking coupon status:', error);
                // 如果是认证错误，不要继续重试
                if (error && typeof error === 'object' && 'code' in error && error.code === 401) {
                    console.log('[useCouponLogic] Authentication error, stopping checks');
                    return;
                }
            }
        };

        checkAll();
    }, [shouldEnableQueries])

    // Countdown effect for active coupon (precise to seconds)
    useEffect(() => {
        if (!activeCoupon) return

        const updateTimeLeft = () => {
            const now = new Date().getTime()
            const expiry = new Date(activeCoupon.expires_at).getTime()
            const difference = expiry - now

            if (difference > 0) {
                const hours = Math.floor(difference / (1000 * 60 * 60))
                const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
                const seconds = Math.floor((difference % (1000 * 60)) / 1000)

                if (hours > 0) {
                    setTimeLeft(`${hours}h ${minutes}m ${seconds}s`)
                } else if (minutes > 0) {
                    setTimeLeft(`${minutes}m ${seconds}s`)
                } else {
                    setTimeLeft(`${seconds}s`)
                }
            } else {
                setTimeLeft("Expired")
            }
        }

        updateTimeLeft()
        const interval = setInterval(updateTimeLeft, 1000) // Update every second

        return () => clearInterval(interval)
    }, [activeCoupon])

    // Handle claim coupon
    const handleClaimCoupon = async () => {
        // If user is not authenticated, open login dialog
        if (!isAuthenticated) {
            openLoginDialog({
                pendingAction: {
                    type: 'claim_coupon',
                    payload: {},
                    onSuccess: () => {
                        // After login, the user will need to click claim again
                        // The component will re-render with authenticated state
                    }
                }
            })
            return
        }

        try {
            clearError()
            const response = await claimFirstMonthDiscount()

            if (response.success) {
                toast.success("🎉 Coupon claimed successfully!", {
                    description: "90% OFF coupon added to your account, valid for 24 hours"
                })
            } else {
                toast.error("Failed to claim", {
                    description: response.message || "Please try again later"
                })
            }
        } catch (error) {
            toast.error("Failed to claim", {
                description: "Network error, please try again later"
            })
        }
    }

    return {
        // State
        timeLeft,
        isAuthenticated,
        isEligibleForFirstMonth,
        hasCheckedEligibility,
        activeCoupon,
        isLoading,

        // Actions
        handleClaimCoupon,

        // Computed values
        // Don't show coupons if user is already an active member
        // For unauthenticated users, show coupon to encourage signup
        // For authenticated non-members, use the original logic
        shouldShow: isActiveMember ? false : (isAuthenticated ? (isEligibleForFirstMonth || activeCoupon) : true),
        canClaim: isActiveMember ? false : (isAuthenticated ? (isEligibleForFirstMonth && !activeCoupon) : true),
        hasActiveCoupon: !!activeCoupon,
        isActiveMember
    }
}
