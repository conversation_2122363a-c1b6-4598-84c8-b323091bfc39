import { Injectable, Inject, BadRequestException, forwardRef } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import { PaymentResponseDto, PaymentStatus } from './dto/payment.dto';
import { MembershipService } from '../membership/membership.service';
import { StripeService } from './stripe.service';
import { CreateSubscriptionDto } from './dto/stripe-payment.dto';
import { CancelMembershipDto } from '../membership/dto/membership.dto';
import { CreditsService } from '../credits/credits.service';
import { CouponService } from '../coupon/coupon.service';
import { CreditTransactionType, CreditTransactionStatus } from '../credits/constant';

const PRICE_ID_KEY_MAP = {
    monthly: 'stripe_price_id_month',
    yearly: 'stripe_price_id_year'
}

const PRICE_KEY_MAP = {
    monthly: 'monthly_price',
    yearly: 'yearly_price'
}

@Injectable()
export class PaymentService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
        @Inject(forwardRef(() => MembershipService))
        private readonly membershipService: MembershipService,
        private readonly stripeService: StripeService,
        @Inject(forwardRef(() => CreditsService))
        private readonly creditsService: CreditsService,
        private readonly couponService: CouponService
    ) {
        this.logger.setContext(PaymentService.name);
    }

    /**
     * 取消支付
     * @param userId 用户ID
     * @param paymentId 支付ID
     * @returns 是否成功
     */
    async cancelPayment(userId: string, paymentId: string): Promise<boolean> {
        const { data: paymentData, error } = await this.supabase
            .from('payments')
            .update({
                status: PaymentStatus.CANCELED
            })
            .eq('id', paymentId)
            .eq('user_id', userId)
            .single();

        if (error) {
            this.logger.error(`取消支付失败: ${paymentId}`, error);
            return false;
        }

        return true;
    }

    /**
     * 创建会员订阅支付
     * @param userId 用户ID
     * @param createSubscriptionDto 创建订阅DTO
     * @param userOrigin 用户来源URL
     * @returns 结账URL
     */
    async createMembershipSubscription(
        userId: string,
        createSubscriptionDto: CreateSubscriptionDto,
        userOrigin: string
    ): Promise<{ checkoutUrl: string }> {
        const { price_id, billing_cycle, coupon_id } = createSubscriptionDto;
        const priceIdKey = PRICE_ID_KEY_MAP[billing_cycle];
        const priceKey = PRICE_KEY_MAP[billing_cycle];

        // 获取会员计划
        const { data: plan, error: planError } = await this.supabase
            .from('membership_plans')
            .select('*')
            .eq('is_active', true)
            .eq(priceIdKey, price_id)
            .single();

        if (!plan || planError) {
            this.logger.error(`获取会员计划失败: ${price_id}`, planError);
            throw new BadRequestException(`无效的会员计划: ${price_id}`);
        }

        const amount = plan[priceKey] * 100;
        let couponData = null;
        const isUnlimitedPlan = plan.plan_name.includes('UNLIMITED');

        // 如果提供了优惠券ID，验证并应用优惠券
        if (coupon_id && !isUnlimitedPlan) {
            const couponValidation = await this.couponService.validateCouponForPayment(
                userId,
                coupon_id,
                'subscription'
            );

            if (!couponValidation.isValid) {
                throw new BadRequestException(couponValidation.message);
            }

            couponData = couponValidation.coupon;
        }

        // 创建支付记录
        const { data: paymentData, error } = await this.supabase
            .from('payments')
            .insert({
                user_id: userId,
                plan_id: plan.id,
                amount,
                payment_type: 'subscription',
                status: PaymentStatus.PENDING,
                metadata: {
                    user_id: userId,
                    plan_name: plan.plan_name,
                    billing_cycle,
                    coupon_id: coupon_id || null,
                }
            })
            .select('*')
            .single();

        // 构建成功和取消URL
        const payment_id = paymentData.id;

        const successUrl = `${userOrigin}/payment/success?payment_id=${payment_id}&type=membership`;
        const cancelUrl = `${userOrigin}/payment/cancel?payment_id=${payment_id}&type=membership`;

        if (error) {
            this.logger.error(`创建支付记录失败: ${userId}`, error);
            throw new BadRequestException('创建支付记录失败');
        }

        try {
            // 创建Stripe结账会话
            const { sessionId, url } = await this.stripeService.createSubscriptionSession({
                userId,
                plan,
                price_id,
                payment_id,
                billing_cycle,
                successUrl,
                cancelUrl,
                couponData
            });

            return { checkoutUrl: url };
        } catch (error) {
            // 如果创建会话失败，更新支付记录为失败状态
            await this.supabase
                .from('payments')
                .update({
                    status: PaymentStatus.FAILED,
                    details: { error: error.message }
                })
                .eq('id', payment_id);

            this.logger.error(`创建Stripe会话失败，支付订单号: ${payment_id}`, error);
            throw new BadRequestException('创建支付会话失败，请重试');
        }
    }

    /**
     * 处理Stripe Webhook事件
     * @param event Stripe事件
     * @returns 处理结果
     */
    async handleStripeWebhookEvent(event: any): Promise<boolean> {
        try {
            switch (event.type) {
                case 'checkout.session.completed':
                    // 判断是积分购买还是会员订阅
                    if (event.data.object.mode === 'payment' &&
                        event.data.object.metadata &&
                        event.data.object.metadata.payment_type === 'credits_purchase'
                    ) {
                        // 处理积分购买支付完成事件
                        return await this.handleCreditsPurchaseCompleted(event.data.object);
                    } else if (event.data.object.subscription) {
                        // 处理首次订阅的结账完成事件
                        return await this.handleSubscriptionCompleted(event.data.object);
                    }
                    this.logger.log(`跳过无法识别的checkout session`);
                    return true;
                case 'invoice.paid':
                    // 只处理自动续费的发票支付事件（非首次订阅）
                    if (this.isRenewalInvoice(event.data.object)) {
                        return await this.handleStripeInvoicePaid(event.data.object);
                    }
                    this.logger.log(`跳过首次订阅的invoice.paid事件`);
                    return true;
                case 'customer.subscription.updated':
                    // 只处理状态变更，不处理支付相关的更新
                    // 例如用户在订阅后去掉了后续的续费，这里需要标记cancel_at_period_end
                    if (!this.isPaymentRelatedUpdate(event.data.object)) {
                        return await this.handleStripeSubscriptionUpdated(event.data.object);
                    }
                    this.logger.log(`跳过支付相关的subscription.updated事件`);
                    return true;
                case 'customer.subscription.deleted':
                    return await this.handleStripeSubscriptionDeleted(event.data.object);
                default:
                    return true;
            }
        } catch (error) {
            this.logger.error(`处理Stripe webhook事件失败: ${event.type}`, error);
            return false;
        }
    }

    /**
     * 判断是否为续费发票（非首次订阅）
     * @param invoice Stripe发票
     * @returns 是否为续费发票
     */
    private isRenewalInvoice(invoice: any): boolean {
        // 对于续费，billing_reason 应该是 'subscription_cycle'
        // 这是 Stripe 明确用于周期性续费的值
        return invoice.billing_reason === 'subscription_cycle';
    }

    /**
     * 判断订阅更新是否与支付相关
     * @param subscription Stripe订阅
     * @returns 是否为支付相关的更新
     */
    private isPaymentRelatedUpdate(subscription: any): boolean {
        // 检查previous_attributes中是否包含支付相关的字段
        const previousAttributes = subscription.previous_attributes || {};

        // 检查是否是状态从incomplete变为active的更新(这种情况通常是首次订阅支付成功)
        if (previousAttributes.status === 'incomplete' && subscription.status === 'active') {
            return true;
        }

        // 检查是否是与当前周期相关的更新(通常是续费)
        // 只有当周期结束时间发生变化时，才认为是与支付相关的更新
        if (previousAttributes.current_period_end) {
            return true;
        }

        return false;
    }

    /**
     * 处理Stripe结账完成事件
     * @param session Stripe结账会话
     * @returns 处理结果
     */
    private async handleSubscriptionCompleted(session: any): Promise<boolean> {
        const { subscription, client_reference_id, metadata } = session;

        if (!subscription || !client_reference_id) {
            this.logger.warn('结账会话缺少订阅ID或用户ID');
            return false;
        }

        try {
            // 检查是否已经处理过该订阅
            const { data: existingSubscription } = await this.supabase
                .from('user_memberships')
                .select('*')
                .or(`subscription_id.eq.${subscription},subscription_id.eq.${subscription}`)
                .eq('is_active', true)
                .maybeSingle();

            if (existingSubscription) {
                this.logger.log(`订阅已存在，跳过处理: ${subscription}`);
                return true;
            }

            // 获取订阅详情
            const subscriptionDetails = await this.stripeService.getSubscription(subscription);
            const priceId = subscriptionDetails.items.data[0]?.price.id;
            const customerId = subscriptionDetails.customer as string;
            const status = subscriptionDetails.status;

            // 获取用户ID和计划ID
            const userId = client_reference_id;
            const planId = metadata?.plan_id;

            if (!planId) {
                this.logger.warn(`结账会话缺少计划ID: ${subscription}`);
                return false;
            }

            // 更新支付记录
            const { data: paymentData = {}, error: paymentError } = await this.supabase
                .from('payments')
                .update({
                    subscription_id: subscription,
                    status: PaymentStatus.COMPLETED,
                    subscription_details: {
                        subscription_id: subscription,
                        customer_id: customerId,
                        subscription_status: status,
                        price_id: priceId,
                        current_period_start: new Date(subscriptionDetails.current_period_start * 1000).toISOString(),
                        current_period_end: new Date(subscriptionDetails.current_period_end * 1000).toISOString()
                    },
                    updated_at: new Date().toISOString()
                })
                .eq('id', metadata.payment_id);

            if (paymentError) {
                this.logger.error(`更新支付记录失败: ${metadata.payment_id}`, paymentError);
                return false;
            }

            // 激活用户会员
            const success = await this.membershipService.activateMembershipSubscription(userId, {
                paymentId: metadata.payment_id,
                subscription_id: subscription,
                plan_id: planId,
                billing_cycle: metadata?.billing_cycle,
                current_period_start: new Date(subscriptionDetails.current_period_start * 1000),
                current_period_end: new Date(subscriptionDetails.current_period_end * 1000),
                cancel_at_period_end: subscriptionDetails.cancel_at_period_end
            });

            if (!success) {
                this.logger.error(`开通会员失败: ${subscription}`);
                return false;
            }

            if (metadata?.coupon_id) {
                // 如果使用了优惠券，标记为已使用
                try {
                    await this.couponService.markCouponAsUsed(metadata.coupon_id, metadata.payment_id);
                    this.logger.log(`优惠券使用成功: 用户=${userId}, 优惠券=${metadata.coupon_id}`);
                } catch (error) {
                    this.logger.error(`标记优惠券为已使用失败: ${metadata.coupon_id}`, error);
                }
            }

            return true;
        } catch (error) {
            this.logger.error(`处理[checkout.session.completed]事件失败: ${subscription}`, error);
            return false;
        }
    }

    /**
     * 处理Stripe发票支付成功事件（续费）
     * @param invoice Stripe发票
     * @returns 处理结果
     */
    private async handleStripeInvoicePaid(invoice: any): Promise<boolean> {
        const { subscription, customer, billing_reason } = invoice;

        // 记录收到的发票信息
        this.logger.log(`收到Stripe发票支付事件: ID=${invoice.id}, 订阅=${subscription}, 原因=${billing_reason}`);

        if (!subscription) {
            this.logger.warn('发票缺少订阅ID');
            return false;
        }

        try {
            // 获取订阅详情
            const subscriptionDetails = await this.stripeService.getSubscription(subscription);
            const customerId = customer;
            const status = subscriptionDetails.status;
            const priceId = subscriptionDetails.items.data[0]?.price.id;
            const amount = invoice.amount_paid;

            // 检查这个发票是否已经处理过
            const { data: existingPayment } = await this.supabase
                .from('payments')
                .select('*')
                .eq('subscription_id', subscription)
                .eq('transaction_id', invoice.id)
                .maybeSingle();

            if (existingPayment) {
                this.logger.log(`发票已处理过，跳过: ${invoice.id}`);
                return true;
            }

            // 查找用户ID和计划ID
            const { data: membershipData } = await this.supabase
                .from('user_memberships')
                .select('user_id, plan_id')
                .or(`subscription_id.eq.${subscription},subscription_id.eq.${subscription}`)
                .eq('is_active', true)
                .single();

            if (!membershipData) {
                this.logger.warn(`未找到与订阅关联的会员: ${subscription}`);
                return false;
            }

            const userId = membershipData.user_id;
            const planId = membershipData.plan_id;

            // 记录会员续费日志
            this.logger.log(`检测到会员续费: 用户=${userId}, 订阅=${subscription}, 金额=${amount / 100}USD`);

            // 更新会员订阅期限 - 这里会触发积分发放
            await this.membershipService.handleSubscriptionUpdate(
                subscription,
                status,
                new Date(subscriptionDetails.current_period_end * 1000),
                {
                    customerId,
                    cancelAtPeriodEnd: subscriptionDetails.cancel_at_period_end,
                    isRenewal: true, // 明确标记这是一次续费
                    invoiceId: invoice.id
                }
            );

            // 创建新的支付记录
            const { error: paymentError } = await this.supabase
                .from('payments')
                .insert({
                    user_id: userId,
                    amount: amount / 100, // 将分转换为元
                    payment_type: 'subscription',
                    status: PaymentStatus.COMPLETED,
                    transaction_id: invoice.id,
                    subscription_id: subscription,
                    plan_id: planId,
                    metadata: {
                        payment_type: 'subscription_renewal',
                        subscription_id: subscription,
                        is_renewal: true, // 明确标记这是一次续费
                        renewal_date: new Date().toISOString()
                    },
                    subscription_details: {
                        subscription_id: subscription,
                        customer_id: customerId,
                        subscription_status: status,
                        price_id: priceId,
                        current_period_start: new Date(subscriptionDetails.current_period_start * 1000).toISOString(),
                        current_period_end: new Date(subscriptionDetails.current_period_end * 1000).toISOString()
                    }
                })
                .select()
                .single();

            if (paymentError) {
                this.logger.error(`创建续费支付记录失败: ${paymentError.message}`, paymentError);
                return false;
            }

            this.logger.log(`会员续费处理完成: 用户=${userId}, 订阅=${subscription}`);
            return true;
        } catch (error) {
            this.logger.error(`处理发票支付事件失败: ${subscription}`, error);
            return false;
        }
    }

    /**
     * 处理Stripe订阅更新事件
     * @param subscription Stripe订阅
     * @returns 处理结果
     */
    private async handleStripeSubscriptionUpdated(subscription: any): Promise<boolean> {
        const { id, status, current_period_end, cancel_at_period_end, customer } = subscription;

        try {
            // 忽略特定的状态变更，避免重复处理
            if (status === 'incomplete' || status === 'incomplete_expired') {
                this.logger.log(`跳过处理不完整的订阅状态: ${id}, status: ${status}`);
                return true;
            }

            // 获取会员订阅
            const { data: membershipData } = await this.supabase
                .from('user_memberships')
                .select('id, user_id, plan_id')
                .eq('subscription_id', id)
                .eq('is_active', true)
                .maybeSingle();

            if (!membershipData) {
                this.logger.warn(`未找到对应的订阅记录: ${id}`);
                return true; // 返回true避免重试
            }

            // 更新会员订阅状态 - 只更新状态，不影响计费周期
            await this.membershipService.handleSubscriptionUpdate(
                id,
                status,
                new Date(current_period_end * 1000),
                {
                    customerId: customer,
                    cancelAtPeriodEnd: cancel_at_period_end
                }
            );

            // 更新订阅相关的支付记录
            await this.supabase
                .from('payments')
                .update({
                    subscription_details: {
                        subscription_status: status,
                        cancel_at_period_end: cancel_at_period_end,
                        current_period_end: new Date(current_period_end * 1000).toISOString()
                    },
                    updated_at: new Date().toISOString()
                })
                .eq('subscription_id', id);

            return true;
        } catch (error) {
            this.logger.error(`处理订阅更新事件失败: ${id}`, error);
            return false;
        }
    }

    /**
     * 处理Stripe订阅删除事件
     * @param subscription Stripe订阅
     * @returns 处理结果
     */
    private async handleStripeSubscriptionDeleted(subscription: any): Promise<boolean> {
        try {
            const subscriptionId = subscription.id;
            this.logger.log(`Stripe订阅删除: ${subscriptionId}`);

            // 处理会员状态更新
            return await this.membershipService.handleSubscriptionUpdate(
                subscriptionId,
                'canceled',
                new Date(),
                {
                    cancelAtPeriodEnd: true
                }
            );
        } catch (error) {
            this.logger.error('处理Stripe订阅删除事件异常', error);
            return false;
        }
    }

    /**
     * 取消会员订阅
     * @param userId 用户ID
     * @param subscriptionId Stripe订阅ID
     * @returns 是否成功
     */
    async cancelMembershipSubscription(userId: string, subscriptionId: string): Promise<boolean> {
        try {
            // 获取用户会员信息
            const membership = await this.membershipService.getUserMembership(userId);
            if (!membership || (membership.subscription_id !== subscriptionId && membership.subscription_id !== subscriptionId)) {
                throw new BadRequestException('未找到有效的订阅');
            }

            // 在Stripe上取消订阅
            await this.stripeService.cancelSubscription(subscriptionId);

            // 更新会员状态
            const cancelDto: CancelMembershipDto = { subscription_id: subscriptionId };
            return await this.membershipService.cancelMembershipSubscription(userId, cancelDto);
        } catch (error) {
            this.logger.error(`取消会员订阅失败: ${userId}`, error);
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('取消订阅失败，请重试');
        }
    }

    /**
     * 查询用户支付记录
     * @param userId 用户ID
     * @param limit 分页限制
     * @param offset 分页偏移
     */
    async getUserPayments(userId: string, limit: number = 10, offset: number = 0): Promise<{ payments: PaymentResponseDto[], total: number }> {
        try {
            const { data, error, count } = await this.supabase
                .from('payments')
                .select('*', { count: 'exact' })
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            if (error) {
                this.logger.error(`获取用户支付记录失败: ${userId}`, error);
                throw new BadRequestException('获取支付记录失败');
            }

            return {
                payments: data,
                total: count || 0
            };
        } catch (error) {
            this.logger.error(`查询支付记录异常: ${userId}`, error);
            throw new BadRequestException('获取支付记录失败');
        }
    }

    /**
     * 获取用户当前订阅详情
     * @param userId 用户ID
     * @returns 订阅详情
     */
    async getUserSubscription(userId: string): Promise<any> {
        try {
            // 获取用户会员信息
            const membership = await this.membershipService.getUserMembership(userId);
            if (!membership || !membership.subscription_id) {
                return null;
            }

            // 获取Stripe订阅详情
            const subscription = await this.stripeService.getSubscription(membership.subscription_id);

            return {
                subscription_id: subscription.id,
                status: subscription.status,
                current_period_start: new Date(subscription.current_period_start * 1000),
                current_period_end: new Date(subscription.current_period_end * 1000),
                cancel_at_period_end: subscription.cancel_at_period_end,
                plan_id: membership.plan_id,
                plan_name: membership.plan_name
            };
        } catch (error) {
            this.logger.error(`获取用户订阅详情失败: ${userId}`, error);
            return null;
        }
    }

    /**
     * 创建积分购买支付
     * @param userId 用户ID
     * @param amountUsd 美元金额
     * @param userOrigin 用户来源URL
     * @returns 结账URL
     */
    async createCreditsPurchase(
        userId: string,
        amountUsd: number,
        userOrigin: string
    ): Promise<{ checkoutUrl: string }> {
        // 验证金额是否有效
        if (amountUsd <= 0) {
            throw new BadRequestException('购买金额必须大于0');
        }

        // 计算积分数量（1USD = 100积分）
        const creditsAmount = amountUsd * 100;

        // 创建支付记录
        const { data: paymentData, error } = await this.supabase
            .from('payments')
            .insert({
                user_id: userId,
                amount: amountUsd * 100, // 转换为美分
                payment_type: 'credits_purchase',
                status: PaymentStatus.PENDING,
                metadata: {
                    user_id: userId,
                    usd_amount: amountUsd,
                    credits_amount: creditsAmount,
                }
            })
            .select('*')
            .single();

        if (error) {
            this.logger.error(`创建积分购买支付记录失败: ${error.message}`, error);
            throw new BadRequestException('创建支付记录失败');
        }

        // 构建成功和取消URL
        const payment_id = paymentData.id;
        const successUrl = `${userOrigin}/payment/success?payment_id=${payment_id}&type=credits`;
        const cancelUrl = `${userOrigin}/payment/cancel?payment_id=${payment_id}&type=credits`;

        try {
            // 创建Stripe结账会话
            const { url } = await this.stripeService.createCreditsPurchaseSession({
                userId,
                amountUsd,
                creditsAmount,
                paymentId: payment_id,
                successUrl,
                cancelUrl,
            });

            // 返回结账URL
            return { checkoutUrl: url };
        } catch (error) {
            this.logger.error(`创建积分购买支付失败: ${error.message}`, error);
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('创建积分购买支付失败，请重试');
        }
    }

    /**
     * 处理积分购买支付完成事件
     * @param session Stripe结账会话
     * @returns 处理结果
     */
    private async handleCreditsPurchaseCompleted(session: any): Promise<boolean> {
        const { client_reference_id, metadata } = session;

        if (!client_reference_id || !metadata) {
            this.logger.warn('积分购买会话缺少用户ID或元数据');
            return false;
        }

        const userId = client_reference_id;
        const paymentId = metadata.payment_id;
        const creditsAmount = parseInt(metadata.credits_amount, 10);
        const usdAmount = parseFloat(metadata.usd_amount || '0');

        if (!paymentId || isNaN(creditsAmount)) {
            this.logger.warn(`积分购买会话缺少支付ID或积分数量: ${client_reference_id}`);
            return false;
        }

        // 检查支付记录是否已经处理（避免重复处理）
        const { data: paymentData, error: checkError } = await this.supabase
            .from('payments')
            .select('status, metadata')
            .eq('id', paymentId)
            .single();

        if (checkError) {
            this.logger.error(`获取支付记录失败: ${paymentId}`, checkError);
            return false;
        }

        if (paymentData.status === PaymentStatus.PAID) {
            this.logger.log(`支付已完成，跳过处理: ${paymentId}`);
            return true;
        }

        // 1.更新支付状态：已支付
        const { error: paymentError } = await this.supabase
            .from('payments')
            .update({
                status: PaymentStatus.PAID,
                updated_at: new Date().toISOString()
            })
            .eq('id', paymentId);

        if (paymentError) {
            this.logger.error(`更新支付记录失败: ${paymentId}`, paymentError);
            return false;
        }

        // 2.调用积分服务创建并完成积分交易
        try {
            // 创建积分交易的DTO对象
            const createTransactionDto = {
                userId: userId,
                type: CreditTransactionType.DIRECT_PURCHASE,
                amount: creditsAmount,
                description: `购买${creditsAmount}积分`,
                paymentId: paymentId,
                status: CreditTransactionStatus.COMPLETED
            };

            // 调用积分服务的创建交易方法
            const result = await this.creditsService.createCreditTransaction(createTransactionDto);

            // 注意：result可能包含status='already_exists'，表示交易已存在
            // 这是正常情况，因为Stripe可能发送重复的webhook
            if (!result) {
                this.logger.error(`创建积分交易记录失败: ${paymentId}`);
                return false;
            }

            // 检查结果是否表明交易已存在
            if (result.status === 'already_exists') {
                this.logger.log(`积分交易记录已存在，无需重复处理: ${paymentId}`);
                return true;
            }

        } catch (error) {
            this.logger.error(`处理积分购买失败: ${error.message}`, error);
            return false;
        }

        // 3.更新支付状态：已完成
        const { error: updateError } = await this.supabase
            .from('payments')
            .update({
                status: PaymentStatus.COMPLETED,
                updated_at: new Date().toISOString()
            })
            .eq('id', paymentId);

        if (updateError) {
            this.logger.error(`更新支付状态失败: ${paymentId}`, updateError);
            return false;
        }

        // 记录日志
        this.logger.log(`积分购买完成: 用户=${userId}, 积分=${creditsAmount}, 金额=${usdAmount}USD, 支付ID=${paymentId}`);
        return true;
    }
}